<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class SubjectsSeeder extends Seeder
{
    public function run()
    {
        // Get all schools
        $schools = $this->db->table('schools')->get()->getResultArray();

        foreach ($schools as $school) {
            $subjects = [
                [
                    'school_id' => $school['id'],
                    'name' => 'Tamil',
                    'standards' => json_encode([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'English',
                    'standards' => json_encode([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Mathematics',
                    'standards' => json_encode([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Environmental Science',
                    'standards' => json_encode([1, 2, 3, 4, 5]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Science',
                    'standards' => json_encode([6, 7, 8, 9, 10]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Social Science',
                    'standards' => json_encode([6, 7, 8, 9, 10]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Physics',
                    'standards' => json_encode([11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Chemistry',
                    'standards' => json_encode([11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Biology',
                    'standards' => json_encode([11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Computer Science',
                    'standards' => json_encode([11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Accountancy',
                    'standards' => json_encode([11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Business Studies',
                    'standards' => json_encode([11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'school_id' => $school['id'],
                    'name' => 'Economics',
                    'standards' => json_encode([11, 12]),
                    'is_active' => true,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ];

            $this->db->table('subjects')->insertBatch($subjects);
            
            // Assign all subjects to all staff members of this school
            $staffMembers = $this->db->table('users')
                                   ->where('school_id', $school['id'])
                                   ->get()
                                   ->getResultArray();

            foreach ($staffMembers as $staff) {
                $schoolSubjects = $this->db->table('subjects')
                                         ->where('school_id', $school['id'])
                                         ->get()
                                         ->getResultArray();

                foreach ($schoolSubjects as $subject) {
                    $standards = json_decode($subject['standards'], true);
                    
                    $this->db->table('staff_subject_assignments')->insert([
                        'school_id' => $school['id'],
                        'staff_id' => $staff['id'],
                        'subject_id' => $subject['id'],
                        'standards' => json_encode($standards),
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ]);
                }
            }
        }

        echo "Subjects and staff assignments created successfully!\n";
    }
}
