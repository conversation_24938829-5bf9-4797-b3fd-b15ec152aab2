<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class UpdateSchoolPlansSeeder extends Seeder
{
    public function run()
    {
        // Update all schools without a plan to use the Free Trial plan (ID: 1)
        $result = $this->db->table('schools')
                          ->where('plan_id IS NULL')
                          ->update(['plan_id' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
        
        echo "Updated {$result} schools with Free Trial plan\n";
        
        // Show current school status
        $schools = $this->db->table('schools')
                           ->select('schools.name, plans.name as plan_name')
                           ->join('plans', 'plans.id = schools.plan_id', 'left')
                           ->get()
                           ->getResultArray();
        
        echo "\nCurrent school plans:\n";
        foreach ($schools as $school) {
            echo "- {$school['name']}: {$school['plan_name']}\n";
        }
    }
}
