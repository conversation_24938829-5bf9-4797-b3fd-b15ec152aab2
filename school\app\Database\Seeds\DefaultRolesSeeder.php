<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class DefaultRolesSeeder extends Seeder
{
    public function run()
    {
        // Get all schools
        $schools = $this->db->table('schools')->get()->getResultArray();
        
        $defaultRoles = [
            ['name' => 'Admin', 'priority' => 1],
            ['name' => 'Principal', 'priority' => 2],
            ['name' => 'Vice Principal', 'priority' => 3],
            ['name' => 'Head of Department', 'priority' => 4],
            ['name' => 'Senior Teacher', 'priority' => 5],
            ['name' => 'Teacher', 'priority' => 6],
            ['name' => 'Assistant Teacher', 'priority' => 7],
            ['name' => 'Reviewer', 'priority' => 8],
            ['name' => 'Staff', 'priority' => 9]
        ];

        foreach ($schools as $school) {
            $insertData = [];
            foreach ($defaultRoles as $role) {
                $insertData[] = [
                    'school_id' => $school['id'],
                    'name' => $role['name'],
                    'priority' => $role['priority'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
            }
            
            // Insert roles for this school
            $this->db->table('roles')->insertBatch($insertData);
            
            echo "Created default roles for school: " . $school['name'] . "\n";
        }
        
        echo "Default roles seeding completed!\n";
    }
}
