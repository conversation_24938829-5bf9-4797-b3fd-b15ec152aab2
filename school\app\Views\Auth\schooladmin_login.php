<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>SchoolAdmin <PERSON>gin - QuestionBank Pro</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 min-h-screen flex items-center justify-center p-4">

  <div class="w-full max-w-md bg-white rounded-xl shadow-2xl overflow-hidden">
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-5 text-center">
      <h1 class="text-3xl font-bold text-white">QuestionBank Pro</h1>
      <p class="text-indigo-100 mt-1">Sign in to your school account</p>
    </div>

    <div class="px-8 py-6">
    <?php if (session()->getFlashdata('success')): ?>
  <div class="mb-4 p-3 bg-green-100 text-green-800 rounded">
    <?= session()->getFlashdata('success') ?>
  </div>
<?php endif; ?>

<?php if (session()->getFlashdata('error')): ?>
  <div class="mb-4 p-3 bg-red-100 text-red-800 rounded">
    <?= session()->getFlashdata('error') ?>
  </div>
<?php endif; ?>

      <form method="post" action="<?= site_url('auth/login') ?>" class="space-y-5">
        <input type="hidden" name="role" value="schooladmin">

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Email <span class="text-red-500">*</span></label>
          <input type="email" name="email" required placeholder="Enter your school email"
                 class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">Password <span class="text-red-500">*</span></label>
          <input type="password" name="password" required placeholder="Enter your password"
                 class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
        </div>

        <div class="flex items-center justify-between">
          <label class="flex items-center">
            <input type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
            <span class="ml-2 text-sm text-gray-600">Remember me</span>
          </label>
          <a href="#" onclick="openForgotPasswordModal('school')" class="text-sm text-indigo-600 hover:text-indigo-500 hover:underline">
            Forgot password?
          </a>
        </div>

        <button type="submit"
                class="w-full bg-indigo-600 text-white py-3 rounded-lg font-semibold hover:bg-indigo-700 transition flex items-center justify-center">
          <i class="fas fa-sign-in-alt mr-2"></i>Sign In
        </button>
    </form>

    <div class="text-center text-sm text-gray-600 mt-6 pt-4 border-t border-gray-200">
      Don't have an account? <a href="<?= base_url('register') ?>" class="text-indigo-600 font-medium hover:underline">Register here</a>
    </div>
    </div>
  </div>

<!-- Forgot Password Modal -->
<div id="forgotPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex justify-between items-center p-6 border-b">
            <h2 class="text-xl font-semibold text-gray-800">
                <i class="fas fa-key mr-2 text-indigo-600"></i>School Admin Password Reset
            </h2>
            <button id="closeForgotPasswordModalBtn" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                &times;
            </button>
        </div>

        <!-- Modal Content -->
        <div class="p-6">
            <!-- Success/Error Messages -->
            <div id="forgotPasswordMessages" class="hidden mb-4"></div>

            <!-- Instructions -->
            <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                    <div class="text-sm text-blue-700">
                        <p class="font-medium mb-1">Password Reset Instructions:</p>
                        <ul class="list-disc list-inside space-y-1 text-xs">
                            <li>Enter your registered email address</li>
                            <li>Check your email for the reset link</li>
                            <li>The link will expire in 1 hour</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Forgot Password Form -->
            <form id="forgotPasswordForm" class="space-y-5">
                <?= csrf_field() ?>
                <input type="hidden" name="user_type" value="school">

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <div class="relative">
                        <input type="email" name="email" required
                               class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                               placeholder="Enter your registered email">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                    </div>
                </div>

                <button type="submit" id="forgotPasswordSubmitBtn"
                        class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300 flex items-center justify-center">
                    <i class="fas fa-paper-plane mr-2"></i>
                    Send Reset Link
                </button>
            </form>

            <!-- Back to Login -->
            <div class="mt-6 text-center">
                <button id="backToLoginBtn" class="text-sm text-indigo-600 hover:text-indigo-500 hover:underline">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Login
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function openForgotPasswordModal(userType) {
    document.getElementById('forgotPasswordModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
    document.getElementById('forgotPasswordForm').reset();
    document.getElementById('forgotPasswordMessages').classList.add('hidden');
}

// Close modal functionality
document.getElementById('closeForgotPasswordModalBtn').addEventListener('click', function() {
    document.getElementById('forgotPasswordModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
});

document.getElementById('backToLoginBtn').addEventListener('click', function() {
    document.getElementById('forgotPasswordModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
});

// Close modal when clicking outside
document.getElementById('forgotPasswordModal').addEventListener('click', function(e) {
    if (e.target === this) {
        this.classList.add('hidden');
        document.body.style.overflow = 'auto';
    }
});

// Handle form submission
document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const submitBtn = document.getElementById('forgotPasswordSubmitBtn');
    const originalText = submitBtn.innerHTML;

    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

    fetch('<?= base_url('forgot-password/send') ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const messagesDiv = document.getElementById('forgotPasswordMessages');
        const isSuccess = data.success;
        const bgColor = isSuccess ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
        const icon = isSuccess ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

        messagesDiv.innerHTML = `
            <div class="p-4 ${bgColor} border rounded-lg">
                <div class="flex items-center">
                    <i class="${icon} mr-2"></i>
                    ${data.message}
                </div>
            </div>
        `;
        messagesDiv.classList.remove('hidden');

        if (data.success) {
            this.reset();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        const messagesDiv = document.getElementById('forgotPasswordMessages');
        messagesDiv.innerHTML = `
            <div class="p-4 bg-red-100 border-red-400 text-red-700 border rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-2"></i>
                    An error occurred. Please try again.
                </div>
            </div>
        `;
        messagesDiv.classList.remove('hidden');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});
</script>

</body>
</html>
