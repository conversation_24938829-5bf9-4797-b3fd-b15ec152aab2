<?php
echo "=== Testing Email Templates ===\n\n";

// Simple test to verify email template generation
class MockEmailService {
    
    public function getSchoolApprovalEmailTemplate($schoolName) {
        $loginUrl = 'http://localhost/schoolquestionbank/school/public/login/schooladmin';
        
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>School Registration Approved</title>
        </head>
        <body>
            <h1>🎉 Congratulations!</h1>
            <h2>Welcome to School Question Bank, {$schoolName}!</h2>
            <p>Your school registration has been <strong>approved</strong>!</p>
            <p>You can now log in to your dashboard: <a href='{$loginUrl}'>Login Here</a></p>
        </body>
        </html>";
    }
    
    public function getSchoolRejectionEmailTemplate($schoolName, $rejectionReason) {
        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <title>School Registration Update</title>
        </head>
        <body>
            <h1>📋 Registration Update</h1>
            <h2>Dear {$schoolName},</h2>
            <p>We regret to inform you that your school registration could not be approved.</p>
            <p><strong>Reason:</strong> {$rejectionReason}</p>
            <p>Please contact our support team for assistance.</p>
        </body>
        </html>";
    }
}

$mockService = new MockEmailService();

echo "1. Testing Approval Email Template\n";
echo "==================================\n";

$approvalTemplate = $mockService->getSchoolApprovalEmailTemplate("Test High School");
echo "✅ Approval template generated successfully\n";
echo "   Length: " . strlen($approvalTemplate) . " characters\n";
echo "   Contains school name: " . (strpos($approvalTemplate, 'Test High School') !== false ? 'Yes' : 'No') . "\n";
echo "   Contains login link: " . (strpos($approvalTemplate, 'login/schooladmin') !== false ? 'Yes' : 'No') . "\n";

echo "\n2. Testing Rejection Email Template\n";
echo "===================================\n";

$rejectionReason = "Incomplete documentation provided";
$rejectionTemplate = $mockService->getSchoolRejectionEmailTemplate("Test High School", $rejectionReason);
echo "✅ Rejection template generated successfully\n";
echo "   Length: " . strlen($rejectionTemplate) . " characters\n";
echo "   Contains school name: " . (strpos($rejectionTemplate, 'Test High School') !== false ? 'Yes' : 'No') . "\n";
echo "   Contains reason: " . (strpos($rejectionTemplate, $rejectionReason) !== false ? 'Yes' : 'No') . "\n";

echo "\n3. Email Content Preview\n";
echo "========================\n";

echo "📧 APPROVAL EMAIL PREVIEW:\n";
echo "Subject: 🎉 School Registration Approved - Welcome to School Question Bank!\n";
echo "To: <EMAIL>\n";
echo "Content: HTML email with congratulations message and login link\n";

echo "\n📧 REJECTION EMAIL PREVIEW:\n";
echo "Subject: School Registration Update - School Question Bank\n";
echo "To: <EMAIL>\n";
echo "Content: HTML email with rejection reason and support contact info\n";

echo "\n=== Template Test Complete ===\n";

echo "\n📋 Implementation Summary:\n";
echo "✅ School approval email template created\n";
echo "✅ School rejection email template created\n";
echo "✅ EmailService methods added:\n";
echo "   - sendSchoolApprovalEmail()\n";
echo "   - sendSchoolRejectionEmail()\n";
echo "✅ SuperAdmin controller updated to send emails\n";
echo "✅ SchoolController updated to send emails\n";

echo "\n🎯 How it works:\n";
echo "1. SuperAdmin approves/rejects school in dashboard\n";
echo "2. Controller calls EmailService method\n";
echo "3. Email is sent with appropriate template\n";
echo "4. School receives notification about status change\n";

echo "\n🚀 Ready for testing in SuperAdmin dashboard!\n";
?>
