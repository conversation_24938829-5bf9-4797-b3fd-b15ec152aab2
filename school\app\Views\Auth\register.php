<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QuestionBank Pro - School Registration</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    .step-slide { display: none; }
    .step-slide.active { display: block; animation: fadeIn 0.3s ease-in-out; }
    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    .otp-input { width: 40px; height: 40px; text-align: center; font-size: 18px; }
    .progress-step { width: 30px; height: 30px; }
    .input-error { border-color: #f87171 !important; }
    .error-message { color: #f87171; font-size: 0.875rem; margin-top: 0.25rem; }
    .success-message { color: #10b981; font-size: 0.875rem; margin-top: 0.25rem; }
  </style>
</head>
<body class="bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 min-h-screen flex items-center justify-center p-4">
  <div class="w-full max-w-2xl bg-white rounded-xl shadow-2xl overflow-hidden">
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-6 py-5 text-center">
      <h1 class="text-3xl font-bold text-white">QuestionBank Pro</h1>
      <p class="text-indigo-100 mt-1">Register your school account</p>
    </div>

    <!-- Progress Steps -->
    <div class="flex justify-center py-4 bg-gray-50">
      <div class="flex items-center">
        <div class="flex flex-col items-center mx-2">
          <div id="step1-indicator" class="progress-step rounded-full bg-indigo-600 text-white flex items-center justify-center">
            <i class="fas fa-check"></i>
          </div>
          <span class="text-xs mt-1 font-medium">School Info</span>
        </div>
        <div class="h-1 w-12 bg-gray-300 rounded-full">
          <div id="step1-progress" class="h-full bg-indigo-600 rounded-full w-0"></div>
        </div>
        <div class="flex flex-col items-center mx-2">
          <div id="step2-indicator" class="progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
            <span>2</span>
          </div>
          <span class="text-xs mt-1 font-medium text-gray-500">Plan Selection</span>
        </div>
        <div class="h-1 w-12 bg-gray-300 rounded-full">
          <div id="step2-progress" class="h-full bg-gray-300 rounded-full w-0"></div>
        </div>
        <div class="flex flex-col items-center mx-2">
          <div id="step3-indicator" class="progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
            <span>3</span>
          </div>
          <span class="text-xs mt-1 font-medium text-gray-500">Password</span>
        </div>
        <div class="h-1 w-12 bg-gray-300 rounded-full">
          <div id="step3-progress" class="h-full bg-gray-300 rounded-full w-0"></div>
        </div>
        <div class="flex flex-col items-center mx-2">
          <div id="step4-indicator" class="progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
            <span>4</span>
          </div>
          <span class="text-xs mt-1 font-medium text-gray-500">Details</span>
        </div>
        <div class="h-1 w-12 bg-gray-300 rounded-full">
          <div id="step4-progress" class="h-full bg-gray-300 rounded-full w-0"></div>
        </div>
        <div class="flex flex-col items-center mx-2">
          <div id="step5-indicator" class="progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center">
            <span>5</span>
          </div>
          <span class="text-xs mt-1 font-medium text-gray-500">Complete</span>
        </div>
      </div>
    </div>

    <div class="px-8 py-6">
      <?php if (session()->getFlashdata('success')): ?>
        <div class="mb-4 p-3 bg-green-100 text-green-700 border border-green-300 rounded-lg flex items-center">
          <i class="fas fa-check-circle mr-2"></i>
          <?= session()->getFlashdata('success') ?>
        </div>
      <?php endif; ?>

      <form id="registrationForm" method="post" action="<?= base_url('school/register') ?>" class="space-y-5">
        <!-- Step 1: School Information -->
        <div id="step-1" class="step-slide active">
          <div>
            <label for="schoolName" class="block text-sm font-medium text-gray-700 mb-1">School Name <span class="text-red-500">*</span></label>
            <input type="text" id="schoolName" name="name" placeholder="Enter your school name" 
                   class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                   value="<?= old('name') ?>">
            <div id="schoolName-error" class="error-message hidden">Please enter your school name</div>
          <?php if (session()->getFlashdata('errors') && array_key_exists('name', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['name'] ?></div>
            <?php endif; ?>
          </div>

          <div>
            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address <span class="text-red-500">*</span></label>
            <div class="relative">
              <input type="email" id="email" name="email" placeholder="<EMAIL>" 
                     class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition pr-24"
                     value="<?= old('email') ?>">
              <button type="button" id="sendOtpBtn" 
                      class="absolute right-2 top-2 px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition">
                Send OTP
              </button>
            </div>
            <div id="email-error" class="error-message hidden">Please enter a valid email address</div>
            <?php if (session()->getFlashdata('errors') && array_key_exists('email', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['email'] ?></div>
            <?php endif; ?>
            <div id="otpSuccess" class="success-message hidden"></div>
          </div>

          <div id="otpContainer" class="hidden mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div class="flex justify-between items-center mb-3">
              <p class="text-sm text-gray-600 flex items-center">
                <i class="fas fa-envelope mr-2"></i> Enter the 6-digit OTP sent to your email:
              </p>
              <button type="button" id="editEmailBtn" class="text-sm text-indigo-600 hover:text-indigo-700 font-medium transition">
                <i class="fas fa-edit mr-1"></i>Edit Email
              </button>
            </div>
            <div class="flex justify-center space-x-3">
              <?php for ($i = 0; $i < 6; $i++): ?>
                <input type="text" name="otp[]" maxlength="1" 
                       class="otp-input border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
              <?php endfor; ?>
            </div>
            <div id="otp-error" class="error-message hidden text-center mt-2">Please enter the complete 6-digit OTP</div>
          </div>

          <div id="verifyOtpSection" class="hidden mt-4 flex justify-center">
            <button type="button" id="verifyOtpBtn" 
                    class="px-5 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center">
              <i class="fas fa-check-circle mr-2"></i> Verify OTP
            </button>
          </div>

          <div class="mt-6 flex justify-end">
            <button type="button" id="step1-next"
                    class="px-6 py-2.5 bg-gray-400 text-gray-600 rounded-lg cursor-not-allowed transition"
                    disabled>
              Next <i class="fas fa-arrow-right ml-2"></i>
            </button>
          </div>
        </div>

        <!-- Step 2: Plan Selection -->
        <div id="step-2" class="step-slide">
          <div>
            <label for="plan" class="block text-sm font-medium text-gray-700 mb-1">Select Plan <span class="text-red-500">*</span></label>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
              <div class="plan-option border rounded-lg p-4 cursor-pointer hover:border-indigo-400 transition" data-value="1" data-plan="free">
                <h3 class="font-bold text-lg text-gray-700">Free Trial</h3>
                <p class="text-gray-600 text-sm mt-1">30 days free trial</p>
                <p class="text-2xl font-bold mt-2">₹0<span class="text-sm font-normal text-gray-500">/month</span></p>
                <ul class="text-sm text-gray-600 mt-2 space-y-1">
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Up to 50 questions</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> 5 subjects maximum</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> 5 users maximum</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Email support</li>
                </ul>
              </div>
              <div class="plan-option border-2 border-indigo-500 bg-indigo-50 rounded-lg p-4 cursor-pointer hover:border-indigo-600 transition relative" data-value="2" data-plan="professional">
                <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                  <span class="bg-indigo-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Most Popular</span>
                </div>
                <h3 class="font-bold text-lg text-indigo-700">Professional</h3>
                <p class="text-gray-600 text-sm mt-1">Full features</p>
                <p class="text-2xl font-bold mt-2">₹999<span class="text-sm font-normal text-gray-500">/month</span></p>
                <ul class="text-sm text-gray-600 mt-2 space-y-1">
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Unlimited questions</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Unlimited subjects</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Up to 50 users</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Export & Print</li>
                  <li class="flex items-center"><i class="fas fa-check-circle text-green-500 mr-2"></i> Priority support</li>
                </ul>
              </div>
            </div>
            <input type="hidden" id="plan" name="plan_id" value="<?= old('plan_id') ?>">
            <div id="plan-error" class="error-message hidden">Please select a plan</div>
            <?php if (session()->getFlashdata('errors') && array_key_exists('plan_id', session()->getFlashdata('errors'))): ?>
              <div class="error-message"><?= session()->getFlashdata('errors')['plan_id'] ?></div>
            <?php endif; ?>
          </div>

          <div class="mt-6 flex justify-between">
            <button type="button" onclick="prevStep(2)" 
                    class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition flex items-center">
              <i class="fas fa-arrow-left mr-2"></i> Previous
            </button>
            <button type="button" id="step2-next" 
                    class="px-6 py-2.5 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
              Next <i class="fas fa-arrow-right ml-2"></i>
            </button>
          </div>
        </div>

        <!-- Step 3: Password Setup -->
        <div id="step-3" class="step-slide">
          <div class="space-y-6">
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password <span class="text-red-500">*</span></label>
              <div class="relative">
                <input type="password" id="password" name="password" placeholder="Create a strong password"
                       class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
                <i class="fas fa-eye-slash absolute right-3 top-3.5 text-gray-400 cursor-pointer" id="togglePassword"></i>
              </div>

              <!-- Password Requirements - Single Line Display -->
              <div id="password-requirements" class="mt-2 text-sm text-red-600 hidden">
                <i class="fas fa-exclamation-circle mr-1"></i>
                <span id="missing-requirements"></span>
              </div>

              <div id="password-error" class="error-message hidden mt-2"></div>
              <?php if (session()->getFlashdata('errors') && array_key_exists('password', session()->getFlashdata('errors'))): ?>
                <div class="error-message mt-2"><?= session()->getFlashdata('errors')['password'] ?></div>
              <?php endif; ?>
            </div>

            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">Confirm Password <span class="text-red-500">*</span></label>
              <div class="relative">
                <input type="password" id="confirmPassword" name="confirm_password" placeholder="Confirm your password"
                       class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition">
                <i class="fas fa-eye-slash absolute right-3 top-3.5 text-gray-400 cursor-pointer" id="toggleConfirmPassword"></i>
              </div>
              <div id="confirmPassword-error" class="error-message hidden mt-2">Passwords do not match</div>
              <div id="confirmPassword-success" class="text-green-600 text-sm mt-2 hidden">
                <i class="fas fa-check-circle mr-1"></i>Passwords match
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-between">
            <button type="button" onclick="prevStep(3)"
                    class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition flex items-center">
              <i class="fas fa-arrow-left mr-2"></i> Previous
            </button>
            <button type="button" id="step3-next"
                    class="px-6 py-2.5 bg-gray-400 text-gray-600 rounded-lg cursor-not-allowed transition"
                    disabled>
              Next <i class="fas fa-arrow-right ml-2"></i>
            </button>
          </div>
        </div>

        <!-- Step 4: Contact Details -->
        <div id="step-4" class="step-slide">
          <div class="space-y-6">
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number <span class="text-red-500">*</span></label>
              <input type="tel" id="phone" name="phone" placeholder="+91 98765 43210"
                     class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                     value="<?= old('phone') ?>">
              <div class="text-xs text-gray-500 mt-1">
                <i class="fas fa-info-circle mr-1"></i> Enter a valid 10-digit mobile number
              </div>
              <div id="phone-error" class="error-message hidden">Please enter a valid phone number</div>
              <?php if (session()->getFlashdata('errors') && array_key_exists('phone', session()->getFlashdata('errors'))): ?>
                <div class="error-message"><?= session()->getFlashdata('errors')['phone'] ?></div>
              <?php endif; ?>
            </div>

            <div>
              <label for="address" class="block text-sm font-medium text-gray-700 mb-1">School Address <span class="text-red-500">*</span></label>
              <textarea id="address" name="address" rows="4" placeholder="Enter complete school address including city, state, and pincode"
                        class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition resize-none"><?= old('address') ?></textarea>
              <div class="text-xs text-gray-500 mt-1">
                <i class="fas fa-info-circle mr-1"></i> Include street address, city, state, and postal code
              </div>
              <div id="address-error" class="error-message hidden">Please enter your school address</div>
              <?php if (session()->getFlashdata('errors') && array_key_exists('address', session()->getFlashdata('errors'))): ?>
                <div class="error-message"><?= session()->getFlashdata('errors')['address'] ?></div>
              <?php endif; ?>
            </div>
          </div>

          <?php if (session()->getFlashdata('error')): ?>
            <div class="mb-4 p-3 bg-red-100 text-red-700 border border-red-300 rounded-lg flex items-center">
              <i class="fas fa-exclamation-circle mr-2"></i>
              <?= session()->getFlashdata('error') ?>
            </div>
          <?php endif; ?>

          <div class="mt-6 flex justify-between">
            <button type="button" onclick="prevStep(4)"
                    class="px-6 py-2.5 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition flex items-center">
              <i class="fas fa-arrow-left mr-2"></i> Previous
            </button>
            <button type="submit" id="submitBtn"
                    class="px-6 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled>
              <i class="fas fa-check-circle mr-2"></i> Register School
            </button>
          </div>
        </div>

        <!-- Step 5: Success Message -->
        <div id="step-5" class="step-slide">
          <div class="text-center py-8">
            <div class="mb-6">
              <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check-circle text-green-600 text-4xl"></i>
              </div>
              <h2 class="text-2xl font-bold text-gray-800 mb-2">Registration Submitted Successfully!</h2>
              <p class="text-gray-600 mb-6">Thank you for registering with QuestionBank Pro</p>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <i class="fas fa-info-circle text-blue-600 text-xl mt-1"></i>
                </div>
                <div class="ml-3 text-left">
                  <h3 class="text-lg font-semibold text-blue-800 mb-2">What happens next?</h3>
                  <ul class="text-blue-700 space-y-2">
                    <li class="flex items-start">
                      <i class="fas fa-clock text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                      <span>Your registration is currently under review by our superadmin team</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-envelope text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                      <span>You will receive an email notification once your account is approved</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-calendar-alt text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                      <span>Please check back within 24 hours for approval status</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                <p class="text-yellow-800 text-sm">
                  <strong>Important:</strong> Please keep your registration email safe. You'll need it to log in once approved.
                </p>
              </div>
            </div>

            <div class="flex justify-center">
              <button type="button" onclick="closeRegistrationModal()"
                      class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition flex items-center justify-center">
                <i class="fas fa-home mr-2"></i> Back to Home
              </button>
            </div>
          </div>
        </div>

        <div class="text-center text-sm text-gray-600 mt-6 pt-4 border-t border-gray-200">
          Already have an account? <a href="#" onclick="openLoginPopup()" class="text-indigo-600 font-medium hover:underline">Sign in here</a>
        </div>
      </form>
    </div>
  </div>

  <!-- Cancel Confirmation Modal -->
  <div id="cancelConfirmationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95">
      <div class="p-6">
        <div class="flex items-center justify-center w-16 h-16 mx-auto bg-red-100 rounded-full mb-4">
          <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
        </div>
        <h3 class="text-xl font-bold text-gray-900 text-center mb-2">Cancel Registration?</h3>
        <p class="text-gray-600 text-center mb-6">
          Are you sure you want to cancel your registration? All entered information will be lost and you'll need to start over.
        </p>
        <div class="flex space-x-3">
          <button id="confirmCancel" class="flex-1 bg-red-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 transition duration-200 flex items-center justify-center">
            <i class="fas fa-check mr-2"></i>Yes, Cancel
          </button>
          <button id="keepRegistering" class="flex-1 bg-gray-200 text-gray-800 py-3 px-4 rounded-lg font-medium hover:bg-gray-300 transition duration-200 flex items-center justify-center">
            <i class="fas fa-arrow-left mr-2"></i>Keep Going
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    let currentStep = 1;
    let isOtpVerified = false;
    let otpSent = false;

    // Initialize UI
    document.addEventListener('DOMContentLoaded', function() {
      updateProgressIndicator();
      setupEventListeners();

      // Handle plan pre-selection from landing page
      const urlParams = new URLSearchParams(window.location.search);
      const planParam = urlParams.get('plan');
      const selectedPlan = planParam || sessionStorage.getItem('selectedPlan') || 'free';

      // Pre-select the plan if specified
      if (selectedPlan) {
        const planOptions = document.querySelectorAll('.plan-option');
        planOptions.forEach(option => {
          const planValue = option.getAttribute('data-plan');
          if (planValue === selectedPlan) {
            // Select plan without triggering click event
            document.querySelectorAll('.plan-option').forEach(opt => {
              opt.classList.remove('border-indigo-500', 'bg-indigo-50');
            });
            option.classList.add('border-indigo-500', 'bg-indigo-50');
            document.getElementById('plan').value = option.getAttribute('data-value');
            checkStep2Validity();
          }
        });

        // Clear session storage
        sessionStorage.removeItem('selectedPlan');
      }

      // Check if there are server-side errors and show the relevant step
      <?php if (session()->getFlashdata('errors')): ?>
        const errors = <?= json_encode(session()->getFlashdata('errors')) ?>;
        if (errors.plan_id) {
          showStep(2);
        } else if (errors.password || errors.phone || errors.address) {
          showStep(3);
        }
      <?php endif; ?>
    });

    function showStep(step) {
      document.querySelectorAll('.step-slide').forEach(div => div.classList.remove('active'));
      document.getElementById(`step-${step}`).classList.add('active');
      currentStep = step;
      updateProgressIndicator();
    }

    function nextStep(step) {
      if (validateStep(step)) {
        showStep(step + 1);
      }
    }

    function prevStep(step) {
      showStep(step - 1);
    }

    function updateProgressIndicator() {
      // Update step indicators
      for (let i = 1; i <= 5; i++) {
        const indicator = document.getElementById(`step${i}-indicator`);
        const progress = document.getElementById(`step${i}-progress`);

        if (i < currentStep) {
          indicator.innerHTML = '<i class="fas fa-check"></i>';
          indicator.className = 'progress-step rounded-full bg-indigo-600 text-white flex items-center justify-center';
          if (progress) progress.className = 'h-full bg-indigo-600 rounded-full w-full';
        } else if (i === currentStep) {
          indicator.innerHTML = i;
          indicator.className = 'progress-step rounded-full bg-indigo-100 text-indigo-600 border-2 border-indigo-600 flex items-center justify-center';
          if (progress) progress.className = 'h-full bg-indigo-600 rounded-full w-0';
        } else {
          indicator.innerHTML = i;
          indicator.className = 'progress-step rounded-full bg-gray-300 text-gray-600 flex items-center justify-center';
          if (progress) progress.className = 'h-full bg-gray-300 rounded-full w-0';
        }
      }

      // Special case for progress between steps
      if (currentStep === 2) {
        document.getElementById('step1-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
      } else if (currentStep === 3) {
        document.getElementById('step1-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
        document.getElementById('step2-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
      } else if (currentStep === 4) {
        document.getElementById('step1-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
        document.getElementById('step2-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
        document.getElementById('step3-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
      } else if (currentStep === 5) {
        document.getElementById('step1-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
        document.getElementById('step2-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
        document.getElementById('step3-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
        document.getElementById('step4-progress').className = 'h-full bg-indigo-600 rounded-full w-full';
      }
    }

    function validateStep(step) {
      let isValid = true;
      
      if (step === 1) {
        const schoolName = document.getElementById('schoolName').value.trim();
        const email = document.getElementById('email').value.trim();
        
        // Validate school name
        if (schoolName === '') {
          document.getElementById('schoolName').classList.add('input-error');
          document.getElementById('schoolName-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('schoolName').classList.remove('input-error');
          document.getElementById('schoolName-error').classList.add('hidden');
        }
        
        // Validate email
        if (email === '' || !email.includes('@') || !email.includes('.')) {
          document.getElementById('email').classList.add('input-error');
          document.getElementById('email-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('email').classList.remove('input-error');
          document.getElementById('email-error').classList.add('hidden');
        }
        
        // Always require OTP verification to proceed
        if (!isOtpVerified) {
          alert('Please verify your OTP before proceeding.');
          isValid = false;
        }
        
        return isValid;
      }
      else if (step === 2) {
        const plan = document.getElementById('plan').value;
        
        // Validate plan selection
        if (plan === '') {
          document.getElementById('plan-error').classList.remove('hidden');
          isValid = false;
        } else {
          document.getElementById('plan-error').classList.add('hidden');
        }
        
        return isValid;
      }
      else if (step === 3) {
        // Step 3 validation is handled by enhanced password validation
        return validatePassword() && validateConfirmPassword();
      }
      else if (step === 4) {
        // Step 4 validation is handled by enhanced phone and address validation
        return validatePhone() && validateAddress();
      }
      
      return true;
    }

    function setupEventListeners() {
      // Step 1 validation
      document.getElementById('schoolName').addEventListener('input', checkStep1Validity);
      document.getElementById('email').addEventListener('input', checkStep1Validity);
      
      // Step 2 validation
      document.getElementById('plan').addEventListener('change', checkStep2Validity);
      
      // Step 3 validation (Password only)
      document.getElementById('password').addEventListener('input', function() {
        validatePassword();
        checkStep3Validity();
      });
      document.getElementById('confirmPassword').addEventListener('input', function() {
        validateConfirmPassword();
        checkStep3Validity();
      });

      // Step 4 validation (Contact details)
      document.getElementById('phone').addEventListener('input', function() {
        validatePhone();
        checkStep4Validity();
      });
      document.getElementById('address').addEventListener('input', function() {
        validateAddress();
        checkStep4Validity();
      });
      
      // Plan selection
      document.querySelectorAll('.plan-option').forEach(option => {
        option.addEventListener('click', function() {
          document.querySelectorAll('.plan-option').forEach(opt => {
            opt.classList.remove('border-indigo-500', 'bg-indigo-50');
          });
          this.classList.add('border-indigo-500', 'bg-indigo-50');
          document.getElementById('plan').value = this.getAttribute('data-value');
          checkStep2Validity();
        });
      });
      
      // Password visibility toggle
      document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        this.classList.toggle('fa-eye-slash');
        this.classList.toggle('fa-eye');
      });
      
      document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        const confirmInput = document.getElementById('confirmPassword');
        const type = confirmInput.getAttribute('type') === 'password' ? 'text' : 'password';
        confirmInput.setAttribute('type', type);
        this.classList.toggle('fa-eye-slash');
        this.classList.toggle('fa-eye');
      });
      
      // OTP input handling - Auto-tab functionality
      document.querySelectorAll('input[name="otp[]"]').forEach((input, index, all) => {
        input.addEventListener('input', (e) => {
          // Only allow single digit
          if (e.target.value.length > 1) {
            e.target.value = e.target.value.slice(0, 1);
          }

          if (e.target.value.length === 1 && index < all.length - 1) {
            all[index + 1].focus();
          }
        });

        input.addEventListener('keydown', (e) => {
          if (e.key === 'Backspace' && e.target.value.length === 0 && index > 0) {
            all[index - 1].focus();
          }
        });

        // Safe paste handling that doesn't interfere with OTP sending
        input.addEventListener('paste', (e) => {
          e.preventDefault();

          // Get pasted content and extract only digits
          const paste = (e.clipboardData || window.clipboardData).getData('text');
          const digits = paste.replace(/\D/g, '');

          if (digits.length > 0) {
            // Fill inputs starting from current position
            for (let i = 0; i < digits.length && (index + i) < all.length; i++) {
              all[index + i].value = digits[i];
            }

            // Move focus to next empty input or last filled
            const nextFocusIndex = Math.min(index + digits.length, all.length - 1);
            all[nextFocusIndex].focus();
          }
        });
      });

      // Send OTP Button
      document.getElementById('sendOtpBtn').addEventListener('click', function () {
        const email = document.getElementById('email').value;
        
        // Clear previous messages
        document.getElementById('email-error').classList.add('hidden');
        document.getElementById('otpSuccess').classList.add('hidden');
        
        // Basic email validation
        if (!email.includes('@') || !email.includes('.')) {
            document.getElementById('email-error').textContent = 'Please enter a valid email address';
            document.getElementById('email-error').classList.remove('hidden');
            return;
        }

        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Checking...';

        // First check if email exists
        fetch('<?= base_url('school/check-email') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ email: email })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                // Email already registered
                btn.disabled = false;
                btn.innerHTML = 'Send OTP';
                document.getElementById('email-error').textContent = 'This email is already registered';
                document.getElementById('email-error').classList.remove('hidden');
                document.getElementById('email').classList.add('input-error');
            } else {
                // Email not registered, proceed with OTP
                btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Sending...';
                
                fetch('<?= base_url('register/send-otp') ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        btn.classList.add('hidden');
                        document.getElementById('otpContainer').classList.remove('hidden');
                        document.getElementById('verifyOtpSection').classList.remove('hidden');
                        document.getElementById('otpSuccess').innerHTML = '<i class="fas fa-check-circle mr-1"></i> OTP sent successfully!';
                        document.getElementById('otpSuccess').classList.remove('hidden');
                        otpSent = true;
                        checkStep1Validity();
                        
                        // Auto-fill OTP for development (remove in production)
                        if (data.debug_otp) {
                            const otpInputs = document.querySelectorAll('input[name="otp[]"]');
                            const otpDigits = data.debug_otp.split('');
                            otpInputs.forEach((input, index) => {
                                input.value = otpDigits[index] || '';
                            });
                        }
                    } else {
                        btn.disabled = false;
                        btn.innerHTML = 'Send OTP';
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    btn.disabled = false;
                    btn.innerHTML = 'Send OTP';
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btn.disabled = false;
            btn.innerHTML = 'Send OTP';
        });
      });

      // Verify OTP Button
      document.getElementById('verifyOtpBtn').addEventListener('click', function () {
        const email = document.getElementById('email').value;
        const otpInputs = document.querySelectorAll('input[name="otp[]"]');
        let enteredOtp = '';
        otpInputs.forEach(input => enteredOtp += input.value);

        if (enteredOtp.length !== 6) {
            document.getElementById('otp-error').textContent = 'Please enter complete 6-digit OTP';
            document.getElementById('otp-error').classList.remove('hidden');
            return;
        }

        const btn = this;
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Verifying...';
        
        // Clear any previous error
        document.getElementById('otp-error').classList.add('hidden');
        
        fetch('<?= base_url('register/verify-otp') ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ email: email, otp: enteredOtp })
        })
        .then(res => res.json())
        .then(data => {
            if (data.status === 'success') {
                isOtpVerified = true;
                btn.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Verified';
                
                // Update success message
                const otpSuccess = document.getElementById('otpSuccess');
                otpSuccess.innerHTML = '<i class="fas fa-check-circle mr-1"></i> OTP verified successfully!';
                otpSuccess.classList.remove('hidden');
                
                // Lock the email field after verification
                const emailInput = document.getElementById('email');
                emailInput.readOnly = true;
                emailInput.classList.add('bg-gray-100');
                
                // Hide OTP-related elements
                document.getElementById('otpContainer').classList.add('hidden');
                document.getElementById('verifyOtpSection').classList.add('hidden');
                
                // Enable next button
                checkStep1Validity();
            } else {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Verify OTP';
                document.getElementById('otp-error').textContent = data.message || 'Invalid OTP code';
                document.getElementById('otp-error').classList.remove('hidden');
                otpInputs.forEach(input => input.value = '');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Verify OTP';
            alert('Failed to verify OTP. Please try again.');
        });
      });

      // Edit Email Button - Allow user to change email and resend OTP
      document.getElementById('editEmailBtn').addEventListener('click', function() {
        // Reset OTP-related states
        isOtpVerified = false;
        otpSent = false;

        // Clear OTP inputs
        document.querySelectorAll('input[name="otp[]"]').forEach(input => {
          input.value = '';
        });

        // Hide OTP-related elements
        document.getElementById('otpContainer').classList.add('hidden');
        document.getElementById('verifyOtpSection').classList.add('hidden');
        document.getElementById('otpSuccess').classList.add('hidden');

        // Clear any error messages
        document.getElementById('email-error').classList.add('hidden');
        document.getElementById('otp-error').classList.add('hidden');

        // Enable email field for editing
        const emailInput = document.getElementById('email');
        emailInput.readOnly = false;
        emailInput.classList.remove('bg-gray-100', 'input-error');
        emailInput.focus(); // Focus on email field for user convenience

        // Show and enable Send OTP button
        const sendOtpBtn = document.getElementById('sendOtpBtn');
        sendOtpBtn.classList.remove('hidden');
        sendOtpBtn.disabled = false;
        sendOtpBtn.innerHTML = 'Send OTP';

        // Update step validity
        checkStep1Validity();

        // Show a helpful message
        const otpSuccess = document.getElementById('otpSuccess');
        otpSuccess.innerHTML = '<i class="fas fa-info-circle mr-1"></i> Please enter the correct email address and click Send OTP again.';
        otpSuccess.classList.remove('hidden');
        otpSuccess.className = 'text-blue-600 text-sm mt-2'; // Change to info styling
      });

      // Next button for step 1
      document.getElementById('step1-next').addEventListener('click', function() {
        nextStep(1);
      });
      
      // Next button for step 2
      document.getElementById('step2-next').addEventListener('click', function() {
        nextStep(2);
      });

      // Next button for step 3
      document.getElementById('step3-next').addEventListener('click', function() {
        nextStep(3);
      });

      // Handle form submission
      document.getElementById('registrationForm').addEventListener('submit', function(e) {
        e.preventDefault(); // Prevent default form submission

        // Validate step 4 before submission
        if (validateStep(4)) {
          // Submit form via AJAX
          const formData = new FormData(this);

          // Show loading state
          const submitBtn = document.getElementById('submitBtn');
          const originalText = submitBtn.innerHTML;
          submitBtn.disabled = true;
          submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Registering...';

          fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // Show success step
              showSuccessStep();
            } else {
              // Handle errors
              alert(data.message || 'Registration failed. Please try again.');
              submitBtn.disabled = false;
              submitBtn.innerHTML = originalText;
            }
          })
          .catch(error => {
            console.error('Registration error:', error);
            alert('Registration failed. Please try again.');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
          });
        }
      });
      
      // Email blur check
      document.getElementById('email').addEventListener('blur', function() {
          const email = this.value.trim();
          if (email.includes('@') && email.includes('.')) {
              fetch('<?= base_url('school/check-email') ?>', {
                  method: 'POST',
                  headers: {
                      'Content-Type': 'application/json',
                      'X-Requested-With': 'XMLHttpRequest'
                  },
                  body: JSON.stringify({ email: email })
              })
              .then(response => response.json())
              .then(data => {
                  if (data.exists) {
                      document.getElementById('email-error').textContent = 'Email is already registered';
                      document.getElementById('email-error').classList.remove('hidden');
                      document.getElementById('email').classList.add('input-error');
                      document.getElementById('sendOtpBtn').disabled = true;
                  }
              });
          }
      });
    }

    function checkStep1Validity() {
      const schoolName = document.getElementById('schoolName').value.trim();
      const email = document.getElementById('email').value.trim();
      const nextBtn = document.getElementById('step1-next');

      // Next button should only be enabled if OTP is verified
      if (schoolName !== '' && email !== '' && email.includes('@') && email.includes('.') && isOtpVerified) {
        nextBtn.disabled = false;
        nextBtn.classList.remove('bg-gray-400', 'text-gray-600', 'cursor-not-allowed');
        nextBtn.classList.add('bg-indigo-600', 'text-white', 'hover:bg-indigo-700');
      } else {
        nextBtn.disabled = true;
        nextBtn.classList.remove('bg-indigo-600', 'text-white', 'hover:bg-indigo-700');
        nextBtn.classList.add('bg-gray-400', 'text-gray-600', 'cursor-not-allowed');
      }
    }



    function checkStep2Validity() {
      const plan = document.getElementById('plan').value;
      const nextBtn = document.getElementById('step2-next');
      
      if (plan !== '') {
        nextBtn.disabled = false;
      } else {
        nextBtn.disabled = true;
      }
    }

    // Reset registration form to initial state
    function resetRegistrationForm() {
      // Reset step to 1
      currentStep = 1;

      // Reset flags
      isOtpVerified = false;
      otpSent = false;

      // Clear all form fields
      document.getElementById('registrationForm').reset();

      // Reset step visibility
      document.querySelectorAll('.step-slide').forEach(step => {
        step.classList.remove('active');
      });
      document.getElementById('step-1').classList.add('active');

      // Reset progress indicator
      updateProgressIndicator();

      // Reset step 1 elements
      const emailInput = document.getElementById('email');
      emailInput.readOnly = false;
      emailInput.classList.remove('bg-gray-100');

      // Hide OTP related elements
      document.getElementById('otpContainer').classList.add('hidden');
      document.getElementById('verifyOtpSection').classList.add('hidden');
      document.getElementById('otpSuccess').classList.add('hidden');

      // Reset Send OTP button
      const sendOtpBtn = document.getElementById('sendOtpBtn');
      sendOtpBtn.classList.remove('hidden');
      sendOtpBtn.disabled = false;
      sendOtpBtn.innerHTML = 'Send OTP';

      // Reset step buttons
      checkStep1Validity();
      checkStep2Validity();
      checkStep3Validity();
      checkStep4Validity();

      // Clear any error messages
      document.querySelectorAll('.error-message').forEach(error => {
        error.classList.add('hidden');
      });

      // Remove error styling from inputs
      document.querySelectorAll('.input-error').forEach(input => {
        input.classList.remove('input-error');
      });

      // Reset plan selection
      document.querySelectorAll('.plan-option').forEach(option => {
        option.classList.remove('border-indigo-500', 'bg-indigo-50');
        option.classList.add('border-gray-200');
      });
      document.getElementById('plan').value = '';
    }



    // Enhanced password validation
    function validatePassword() {
      const password = document.getElementById('password').value;
      const checks = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
      };

      // Get missing requirements
      const missing = [];
      if (!checks.length) missing.push('8+ characters');
      if (!checks.uppercase) missing.push('uppercase letter');
      if (!checks.lowercase) missing.push('lowercase letter');
      if (!checks.number) missing.push('number');
      if (!checks.special) missing.push('special character');

      // Update display
      const requirementsDiv = document.getElementById('password-requirements');
      const missingSpan = document.getElementById('missing-requirements');

      if (missing.length > 0 && password.length > 0) {
        missingSpan.textContent = `Missing: ${missing.join(', ')}`;
        requirementsDiv.classList.remove('hidden');
      } else {
        requirementsDiv.classList.add('hidden');
      }

      return Object.values(checks).every(check => check);
    }

    function validateConfirmPassword() {
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;
      const errorDiv = document.getElementById('confirmPassword-error');
      const successDiv = document.getElementById('confirmPassword-success');

      if (confirmPassword.length > 0) {
        if (password === confirmPassword) {
          errorDiv.classList.add('hidden');
          successDiv.classList.remove('hidden');
          return true;
        } else {
          errorDiv.classList.remove('hidden');
          successDiv.classList.add('hidden');
          return false;
        }
      } else {
        errorDiv.classList.add('hidden');
        successDiv.classList.add('hidden');
        return false;
      }
    }

    function checkStep3Validity() {
      const isPasswordValid = validatePassword();
      const isConfirmPasswordValid = validateConfirmPassword();
      const nextBtn = document.getElementById('step3-next');

      if (isPasswordValid && isConfirmPasswordValid) {
        nextBtn.disabled = false;
        nextBtn.classList.remove('bg-gray-400', 'text-gray-600', 'cursor-not-allowed');
        nextBtn.classList.add('bg-indigo-600', 'text-white', 'hover:bg-indigo-700');
      } else {
        nextBtn.disabled = true;
        nextBtn.classList.remove('bg-indigo-600', 'text-white', 'hover:bg-indigo-700');
        nextBtn.classList.add('bg-gray-400', 'text-gray-600', 'cursor-not-allowed');
      }
    }

    // Phone validation
    function validatePhone() {
      const phone = document.getElementById('phone').value.trim();
      const phoneError = document.getElementById('phone-error');
      const phoneInput = document.getElementById('phone');

      // Remove any non-digit characters for validation
      const digitsOnly = phone.replace(/\D/g, '');

      let isValid = true;
      let errorMessage = '';

      if (phone.length === 0) {
        isValid = false;
        errorMessage = 'Phone number is required';
      } else if (phone.length > 20) {
        isValid = false;
        errorMessage = 'Phone number exceeds maximum length (20 characters)';
      } else if (digitsOnly.length < 10) {
        isValid = false;
        errorMessage = 'Phone number must be at least 10 digits';
      } else if (digitsOnly.length > 15) {
        isValid = false;
        errorMessage = 'Phone number cannot exceed 15 digits';
      } else if (!/^[\d\s\-\+\(\)]+$/.test(phone)) {
        isValid = false;
        errorMessage = 'Phone number contains invalid characters';
      } else if (digitsOnly.length === 10 && !digitsOnly.match(/^[6-9]/)) {
        isValid = false;
        errorMessage = 'Indian mobile numbers should start with 6, 7, 8, or 9';
      }

      // Update UI
      if (isValid) {
        phoneInput.classList.remove('input-error');
        phoneError.classList.add('hidden');
      } else {
        phoneInput.classList.add('input-error');
        phoneError.textContent = errorMessage;
        phoneError.classList.remove('hidden');
      }

      return isValid;
    }

    // Address validation
    function validateAddress() {
      const address = document.getElementById('address').value.trim();
      const addressError = document.getElementById('address-error');
      const addressInput = document.getElementById('address');

      let isValid = true;
      let errorMessage = '';

      if (address.length === 0) {
        isValid = false;
        errorMessage = 'School address is required';
      } else if (address.length < 20) {
        isValid = false;
        errorMessage = 'Please provide a complete address (minimum 20 characters)';
      } else if (address.length > 500) {
        isValid = false;
        errorMessage = 'Address exceeds maximum length (500 characters)';
      } else if (!/[0-9]/.test(address)) {
        isValid = false;
        errorMessage = 'Address should include house/building number';
      } else if (address.split(' ').length < 4) {
        isValid = false;
        errorMessage = 'Please provide a detailed address with street, area, city';
      } else if (!/[a-zA-Z]/.test(address)) {
        isValid = false;
        errorMessage = 'Address must contain alphabetic characters';
      }

      // Update UI
      if (isValid) {
        addressInput.classList.remove('input-error');
        addressError.classList.add('hidden');
      } else {
        addressInput.classList.add('input-error');
        addressError.textContent = errorMessage;
        addressError.classList.remove('hidden');
      }

      return isValid;
    }

    function checkStep4Validity() {
      const isPhoneValid = validatePhone();
      const isAddressValid = validateAddress();
      const submitBtn = document.getElementById('submitBtn');

      if (isPhoneValid && isAddressValid) {
        submitBtn.disabled = false;
      } else {
        submitBtn.disabled = true;
      }
    }

    // Function to open login popup in parent window
    function openLoginPopup() {
      // Send message to parent window to close registration modal and open login modal
      if (window.parent && window.parent !== window) {
        window.parent.postMessage({
          action: 'switchToLogin',
          type: 'schooladmin'
        }, '*');
      } else {
        // Fallback if not in iframe
        window.location.href = '<?= base_url('login') ?>';
      }
    }

    // Handle successful registration
    function showSuccessStep() {
      currentStep = 5;
      showStep(5);
    }

    // Close registration modal (for parent window)
    function closeRegistrationModal() {
      if (window.parent && window.parent !== window) {
        window.parent.postMessage('registrationSuccess', '*');
      }
    }

    // Start new registration
    function startNewRegistration() {
      resetRegistrationForm();
    }

    // Listen for messages from parent window
    window.addEventListener('message', function(e) {
      if (e.data === 'resetRegistrationForm') {
        resetRegistrationForm();
      }
    });
  </script>
</body>
</html>