// Superadmin Profile Popup Functions

// Open profile popup
function openProfilePopup() {
    const popup = document.getElementById('superadmin-profile-popup');
    if (popup) {
        popup.classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Load profile data
        loadProfileData();

        // Add animation
        setTimeout(() => {
            const card = popup.querySelector('.transform');
            if (card) {
                card.classList.add('scale-100');
                card.classList.remove('scale-95');
            }
        }, 10);
    }
}

// Close profile popup
function closeProfilePopup() {
    const popup = document.getElementById('superadmin-profile-popup');
    if (popup) {
        const card = popup.querySelector('.transform');
        if (card) {
            card.classList.add('scale-95');
            card.classList.remove('scale-100');
        }
        
        setTimeout(() => {
            popup.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }, 150);
    }
}

// Open change password modal
function openChangePasswordModal() {
    closeProfilePopup(); // Close profile popup first
    
    setTimeout(() => {
        const modal = document.getElementById('change-password-modal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            
            // Clear form
            document.getElementById('change-password-form').reset();
            document.getElementById('password-change-message').classList.add('hidden');
        }
    }, 200);
}

// Close change password modal
function closeChangePasswordModal() {
    const modal = document.getElementById('change-password-modal');
    if (modal) {
        modal.classList.add('hidden');
        document.body.style.overflow = 'auto';
        
        // Clear form
        document.getElementById('change-password-form').reset();
        document.getElementById('password-change-message').classList.add('hidden');
    }
}

// Handle change password form submission
function handleChangePasswordSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const messageDiv = document.getElementById('password-change-message');
    
    // Validate passwords
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');
    
    if (newPassword !== confirmPassword) {
        showPasswordMessage('New passwords do not match!', 'error');
        return;
    }
    
    if (newPassword.length < 6) {
        showPasswordMessage('Password must be at least 6 characters long!', 'error');
        return;
    }
    
    // Show loading state
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
    submitBtn.disabled = true;
    
    // Clear previous messages
    messageDiv.classList.add('hidden');
    
    // Submit form
    fetch('/index.php/superadmin/changePassword', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showPasswordMessage('Password updated successfully!', 'success');
            setTimeout(() => {
                closeChangePasswordModal();
            }, 2000);
        } else {
            showPasswordMessage(data.message || 'Failed to update password', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showPasswordMessage('Network error occurred. Please try again.', 'error');
    })
    .finally(() => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Show password change message
function showPasswordMessage(message, type) {
    const messageDiv = document.getElementById('password-change-message');
    const alertClass = type === 'error' ? 'bg-red-100 text-red-800 border-red-200' : 'bg-green-100 text-green-800 border-green-200';
    
    messageDiv.innerHTML = `
        <div class="p-3 rounded border ${alertClass}">
            <div class="flex items-center">
                <i class="fas ${type === 'error' ? 'fa-exclamation-circle' : 'fa-check-circle'} mr-2"></i>
                ${message}
            </div>
        </div>
    `;
    messageDiv.classList.remove('hidden');
}

// Initialize profile popup functionality
function initializeProfilePopup() {
    // Add click handlers to SA icons
    const saIcons = document.querySelectorAll('.gradient-bg');
    saIcons.forEach(icon => {
        if (icon.querySelector('span')?.textContent.trim() === 'SA') {
            icon.style.cursor = 'pointer';
            icon.addEventListener('click', openProfilePopup);
        }
    });
    
    // Add click handler to profile section in sidebar
    const profileSection = document.querySelector('.border-t.border-gray-200.flex-shrink-0');
    if (profileSection) {
        profileSection.style.cursor = 'pointer';
        profileSection.addEventListener('click', openProfilePopup);
        
        // Add hover effect
        profileSection.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f9fafb';
        });
        
        profileSection.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    }
    
    // Add form submit handler
    const changePasswordForm = document.getElementById('change-password-form');
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', handleChangePasswordSubmit);
    }
    
    // Close popup on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            if (!document.getElementById('change-password-modal').classList.contains('hidden')) {
                closeChangePasswordModal();
            } else if (!document.getElementById('superadmin-profile-popup').classList.contains('hidden')) {
                closeProfilePopup();
            }
        }
    });
}

// Load profile data from server
function loadProfileData() {
    console.log('Loading profile data...');
    fetch('/index.php/superadmin/getProfile', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.json();
    })
    .then(data => {
        console.log('Profile data received:', data);
        if (data.success) {
            const profile = data.profile;

            // Update profile information
            document.getElementById('profile-name').textContent = profile.name;
            document.getElementById('profile-email').textContent = profile.email;
            document.getElementById('profile-role-detail').textContent = profile.role;
            document.getElementById('profile-status-text').textContent = profile.status;

            // Update statistics
            document.getElementById('profile-last-login').textContent = profile.last_login;
            document.getElementById('profile-created').textContent = profile.account_created;
            document.getElementById('profile-schools').textContent = profile.schools_managed;
            document.getElementById('profile-sessions').textContent = profile.total_sessions;
        } else {
            console.error('Failed to load profile:', data.message);
            // Show error in UI
            document.getElementById('profile-email').textContent = 'Error: ' + data.message;
        }
    })
    .catch(error => {
        console.error('Error loading profile:', error);
        // Show fallback data
        showFallbackProfileData();
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeProfilePopup();
});
