<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class AuthFilter implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = session();

        // If not logged in
        if (!$session->get('logged_in')) {
            // Detect role from filter argument, fallback to superadmin
            $targetRole = $arguments[0] ?? 'superadmin';
            $loginUrl = ($targetRole === 'schooladmin') ? '/login/schooladmin' : '/login/superadmin';

            return redirect()->to($loginUrl)->with('error', 'Please login as ' . $targetRole . '.');
        }

        // If role mismatch (e.g., schooladmin accessing superadmin dashboard)
        if (!empty($arguments)) {
            $expectedRole = $arguments[0];
            $currentRole = $session->get('role');

            if ($expectedRole !== $currentRole) {
                return redirect()->to('/logout')->with('error', 'Unauthorized access detected.');
            }
        }

        // Allow access
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No post-processing needed for now
    }
}
