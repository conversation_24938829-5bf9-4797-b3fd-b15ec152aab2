<?php
/*
namespace App\Models;

use CodeIgniter\Model;

class SchoolModel extends Model
{
    protected $table = 'schools';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;

    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'name', 'email', 'password', 'plan_id', 'address', 'phone', 'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
}
*/

namespace App\Models;

use CodeIgniter\Model;

class SchoolModel extends Model
{
    protected $table = 'schools';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
    'name', 'email', 'password', 'plan_id', 'address', 'phone', 'status', 'rejection_reason'
];
    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Add these methods while keeping your existing structure
    public function approveSchool($id)
    {
        return $this->update($id, ['status' => 'active']);
    }

    public function getPendingSchools()
    {
        return $this->where('status', 'inactive')->orderBy('created_at', 'DESC')->findAll();
    }
}