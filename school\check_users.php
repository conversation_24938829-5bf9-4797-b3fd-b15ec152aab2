<?php
// Simple database connection
$host = 'localhost';
$dbname = 'school';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get all users
    $stmt = $pdo->prepare("SELECT * FROM users ORDER BY id");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "All users in the database:\n";
    echo "=========================\n";
    
    foreach ($users as $user) {
        echo "ID: " . $user['id'] . "\n";
        echo "Name: " . ($user['name'] ?? 'N/A') . "\n";
        echo "Email: " . $user['email'] . "\n";
        echo "Status: " . ($user['status'] ?? 'N/A') . "\n";
        echo "Is Deleted: " . ($user['is_deleted'] ?? 'N/A') . "\n";
        echo "School ID: " . ($user['school_id'] ?? 'N/A') . "\n";
        echo "Password Hash: " . substr($user['password'], 0, 50) . "...\n";
        echo "Created: " . ($user['created_at'] ?? 'N/A') . "\n";
        echo "Updated: " . ($user['updated_at'] ?? 'N/A') . "\n";
        echo "------------------------\n";
    }
    
    echo "\nTotal users: " . count($users) . "\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
