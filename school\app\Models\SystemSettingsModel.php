<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * SuperAdmin Settings Model
 * Handles global system settings for superadmin (NOT school-specific settings)
 */
class SystemSettingsModel extends Model
{
    protected $table = 'superadmin_settings';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false; // Disabled soft deletes
    protected $protectFields = true;
    protected $allowedFields = [
        'setting_key',
        'setting_value',
        'setting_type',
        'category',
        'description',
        'is_active'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'setting_key' => 'required|max_length[100]',
        'setting_value' => 'required',
        'setting_type' => 'required|in_list[string,integer,boolean,json]', // Removed decimal for now
        'category' => 'required|max_length[50]'
    ];

    protected $validationMessages = [
        'setting_key' => [
            'required' => 'Setting key is required'
        ]
    ];

    /**
     * Get all global superadmin settings organized by category
     */
    public function getAllSettings()
    {
        try {
            // Get all active superadmin settings
            $settings = $this->where('is_active', 1)
                            ->orderBy('category', 'ASC')
                            ->orderBy('setting_key', 'ASC')
                            ->findAll();

            log_message('info', 'Found ' . count($settings) . ' superadmin settings');

            $organized = [];
            foreach ($settings as $setting) {
                $value = $this->castValue($setting['setting_value'], $setting['setting_type']);
                $organized[$setting['category']][$setting['setting_key']] = [
                    'value' => $value,
                    'type' => $setting['setting_type'],
                    'description' => $setting['description'] ?? '',
                    'is_editable' => $this->isSettingEditable($setting['setting_key']),
                    'validation_rules' => $this->getSettingValidationRules($setting['setting_key'])
                ];
            }

            return $organized;
        } catch (\Exception $e) {
            log_message('error', 'Error in getAllSettings: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get a specific global superadmin setting value
     */
    public function getSetting($key, $default = null)
    {
        $setting = $this->where('setting_key', $key)
                        ->first();

        if (!$setting) {
            return $default;
        }

        return $this->castValue($setting['setting_value'], $setting['setting_type']);
    }

    /**
     * Set a global superadmin setting value
     */
    public function setSetting($key, $value, $type = 'string', $category = 'general', $description = '')
    {
        $stringValue = $this->valueToString($value, $type);

        $data = [
            'setting_key' => $key,
            'setting_value' => $stringValue,
            'setting_type' => $type,
            'category' => $category,
            'description' => $description,
            'is_active' => 1  // Default to active
        ];

        // Check if setting exists
        $existing = $this->where('setting_key', $key)
                        ->first();

        if ($existing) {
            // Don't allow updating non-editable settings
            if (!$this->isSettingEditable($key)) {
                throw new \Exception("Setting '{$key}' is not editable");
            }
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Update multiple global superadmin settings at once
     */
    public function updateSettings($settings)
    {
        $this->db->transStart();

        foreach ($settings as $category => $categorySettings) {
            foreach ($categorySettings as $key => $value) {
                // Check if setting exists and is editable
                $existing = $this->where('setting_key', $key)
                                ->first();

                if ($existing && !$this->isSettingEditable($key)) {
                    continue; // Skip non-editable settings
                }

                // Determine type based on existing setting or value
                $type = $existing ? $existing['setting_type'] : $this->determineType($value);
                $this->setSetting($key, $value, $type, $category);
            }
        }

        $this->db->transComplete();

        return $this->db->transStatus();
    }

    /**
     * Cast string value to appropriate type
     */
    private function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            case 'string':
            default:
                return $value;
        }
    }

    /**
     * Convert value to string for storage
     */
    private function valueToString($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'json':
                return json_encode($value);
            case 'integer':
            case 'string':
            default:
                return (string) $value;
        }
    }

    /**
     * Determine type based on value
     */
    private function determineType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_array($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }

    /**
     * Check if a setting is editable
     */
    private function isSettingEditable($key)
    {
        $nonEditableSettings = [
            'platform_name',
            'system_version',
            'password_min_length'
        ];

        return !in_array($key, $nonEditableSettings);
    }

    /**
     * Get validation rules for a specific setting
     */
    private function getSettingValidationRules($key)
    {
        $validationRules = [
            'trial_period_days' => 'required|integer|greater_than[0]|less_than_equal_to[365]',
            'max_questions_trial' => 'required|integer|greater_than[0]',
            'max_schools_limit' => 'required|integer|greater_than[0]',
            'admin_email' => 'required|valid_email',
            'system_timezone' => 'required',
            'backup_frequency' => 'required|in_list[daily,weekly,monthly]',
            'session_timeout' => 'required|integer|greater_than[300]|less_than_equal_to[86400]',
            'max_login_attempts' => 'required|integer|greater_than[0]|less_than_equal_to[10]'
        ];

        return $validationRules[$key] ?? '';
    }

    /**
     * Initialize default global superadmin settings
     */
    public function initializeDefaults()
    {
        try {
            log_message('info', 'Initializing default superadmin settings...');

            $defaultSettings = [
                // Platform Configuration
                ['setting_key' => 'trial_period_days', 'setting_value' => '30', 'setting_type' => 'integer', 'category' => 'platform', 'description' => 'Trial period duration in days'],
                ['setting_key' => 'max_questions_trial', 'setting_value' => '50', 'setting_type' => 'integer', 'category' => 'platform', 'description' => 'Maximum questions allowed in trial'],
                ['setting_key' => 'max_schools_limit', 'setting_value' => '1000', 'setting_type' => 'integer', 'category' => 'platform', 'description' => 'Maximum schools allowed on platform'],
                ['setting_key' => 'platform_name', 'setting_value' => 'School Question Bank', 'setting_type' => 'string', 'category' => 'platform', 'description' => 'Platform display name'],

                // Email Notifications
                ['setting_key' => 'email_trial_expiration', 'setting_value' => '1', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Send trial expiration warnings'],
                ['setting_key' => 'email_new_registrations', 'setting_value' => '1', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Send new school registration notifications'],
                ['setting_key' => 'email_daily_reports', 'setting_value' => '0', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Send daily usage reports'],
                ['setting_key' => 'admin_email', 'setting_value' => '<EMAIL>', 'setting_type' => 'string', 'category' => 'notifications', 'description' => 'Admin email for notifications'],

                // System Configuration
                ['setting_key' => 'system_timezone', 'setting_value' => 'Asia/Kolkata', 'setting_type' => 'string', 'category' => 'system', 'description' => 'System timezone'],
                ['setting_key' => 'system_maintenance_mode', 'setting_value' => '0', 'setting_type' => 'boolean', 'category' => 'system', 'description' => 'Enable maintenance mode'],
                ['setting_key' => 'system_version', 'setting_value' => '1.0.0', 'setting_type' => 'string', 'category' => 'system', 'description' => 'System version'],
                ['setting_key' => 'backup_frequency', 'setting_value' => 'daily', 'setting_type' => 'string', 'category' => 'system', 'description' => 'Backup frequency'],

                // Security Settings
                ['setting_key' => 'session_timeout', 'setting_value' => '3600', 'setting_type' => 'integer', 'category' => 'security', 'description' => 'Session timeout in seconds'],
                ['setting_key' => 'max_login_attempts', 'setting_value' => '5', 'setting_type' => 'integer', 'category' => 'security', 'description' => 'Maximum login attempts before lockout'],
                ['setting_key' => 'password_min_length', 'setting_value' => '8', 'setting_type' => 'integer', 'category' => 'security', 'description' => 'Minimum password length'],
            ];

            $insertedCount = 0;
            foreach ($defaultSettings as $setting) {
                // Check if setting already exists
                $existing = $this->where('setting_key', $setting['setting_key'])
                                ->first();
                if (!$existing) {
                    $setting['created_at'] = date('Y-m-d H:i:s');
                    $setting['updated_at'] = date('Y-m-d H:i:s');
                    $setting['is_active'] = 1; // Default to active
                    $this->insert($setting);
                    $insertedCount++;
                }
            }

            log_message('info', "Inserted {$insertedCount} new default superadmin settings");
        } catch (\Exception $e) {
            log_message('error', 'Error initializing default superadmin settings: ' . $e->getMessage());
            throw $e;
        }
    }
}
