<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\SubscriptionModel;
use App\Models\SchoolModel;
use App\Services\EmailService;

class SendSubscriptionReminders extends BaseCommand
{
    protected $group       = 'subscription';
    protected $name        = 'subscription:reminders';
    protected $description = 'Send subscription expiry reminder emails';
    protected $usage       = 'subscription:reminders [days]';
    protected $arguments   = [
        'days' => 'Number of days before expiry to send reminders (default: 7)'
    ];

    protected $subscriptionModel;
    protected $schoolModel;
    protected $emailService;

    public function __construct($logger, $commands)
    {
        parent::__construct($logger, $commands);
        $this->subscriptionModel = new SubscriptionModel();
        $this->schoolModel = new SchoolModel();
        $this->emailService = new EmailService();
    }

    public function run(array $params)
    {
        $days = $params[0] ?? 7;
        
        CLI::write("Checking for subscriptions expiring in {$days} days...", 'yellow');

        // Get expiring subscriptions
        $expiringSubscriptions = $this->subscriptionModel->getExpiringSubscriptions($days);

        if (empty($expiringSubscriptions)) {
            CLI::write("No subscriptions found expiring in {$days} days.", 'green');
            return;
        }

        CLI::write("Found " . count($expiringSubscriptions) . " subscriptions expiring soon.", 'yellow');

        $successCount = 0;
        $failureCount = 0;

        foreach ($expiringSubscriptions as $subscription) {
            CLI::write("Processing: {$subscription['school_name']} ({$subscription['school_email']})", 'white');

            try {
                $result = $this->emailService->sendSubscriptionExpiryReminder(
                    $subscription['school_email'],
                    $subscription['school_name'],
                    $subscription,
                    $subscription['school_id']
                );

                if ($result) {
                    CLI::write("✓ Reminder sent successfully", 'green');
                    $successCount++;
                } else {
                    CLI::write("✗ Failed to send reminder", 'red');
                    $failureCount++;
                }

            } catch (\Exception $e) {
                CLI::write("✗ Error: " . $e->getMessage(), 'red');
                $failureCount++;
            }

            // Small delay to avoid overwhelming email server
            usleep(500000); // 0.5 seconds
        }

        CLI::write("", 'white');
        CLI::write("Summary:", 'yellow');
        CLI::write("✓ Successful: {$successCount}", 'green');
        CLI::write("✗ Failed: {$failureCount}", 'red');
        CLI::write("Total processed: " . count($expiringSubscriptions), 'white');
    }
}
