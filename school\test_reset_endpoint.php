<?php
echo "=== Testing Reset Password Endpoint ===\n\n";

// Test the endpoint directly
$testToken = 'b7d7d73b730eefd04f65b6bf3e49b44e591e24a49f3d023853a40fac2d9c5e73';
$resetUrl = "http://localhost/schoolquestionbank/school/public/forgot-password/reset?token=" . $testToken;

echo "1. Testing URL: $resetUrl\n\n";

// Use cURL to get detailed response
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $resetUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // Don't follow redirects automatically
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, false);
curl_setopt($ch, CURLOPT_VERBOSE, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);

curl_close($ch);

$headers = substr($response, 0, $headerSize);
$body = substr($response, $headerSize);

echo "2. HTTP Response Code: $httpCode\n";
echo "3. Response Headers:\n";
echo $headers . "\n";

if ($httpCode >= 300 && $httpCode < 400) {
    echo "✅ Redirect detected!\n";
    // Extract Location header
    if (preg_match('/Location: (.+)/i', $headers, $matches)) {
        $redirectUrl = trim($matches[1]);
        echo "4. Redirect URL: $redirectUrl\n";
        
        // Parse redirect URL
        $parsedUrl = parse_url($redirectUrl);
        if (isset($parsedUrl['query'])) {
            parse_str($parsedUrl['query'], $params);
            echo "5. Redirect Parameters:\n";
            foreach ($params as $key => $value) {
                if ($key === 'reset_token') {
                    echo "   - $key: " . substr($value, 0, 20) . "...\n";
                } else {
                    echo "   - $key: $value\n";
                }
            }
        }
    }
} else {
    echo "4. Response Body (first 500 chars):\n";
    echo substr($body, 0, 500) . "\n";
}

echo "\n=== Test Complete ===\n";
?>
