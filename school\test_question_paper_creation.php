<?php
// Test question paper creation manually
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'schoolquestionbank';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Test Question Paper Creation</h2>";
    
    // First, let's check if we have any questions
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM questions WHERE status = 'approved'");
    $questionCount = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<h3>Approved Questions Count: " . $questionCount['count'] . "</h3>";
    
    if ($questionCount['count'] > 0) {
        // Get some sample questions
        $stmt = $pdo->query("SELECT * FROM questions WHERE status = 'approved' LIMIT 5");
        $questions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "<h3>Sample Questions:</h3>";
        echo "<pre>" . print_r($questions, true) . "</pre>";
        
        // Try to create a test question paper
        $pdo->beginTransaction();
        
        try {
            // Insert question paper
            $stmt = $pdo->prepare("
                INSERT INTO question_papers (school_id, title, standard, subject, duration, total_marks, status, created_by, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $paperData = [
                1, // school_id
                'Test Question Paper',
                '10',
                'Mathematics',
                2.5,
                100,
                'draft',
                1, // created_by
                date('Y-m-d H:i:s')
            ];
            
            $stmt->execute($paperData);
            $paperId = $pdo->lastInsertId();
            
            echo "<h3>Created Question Paper with ID: $paperId</h3>";
            
            // Insert paper questions
            $stmt = $pdo->prepare("
                INSERT INTO paper_questions (paper_id, question_id, question_order, marks, created_at) 
                VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($questions as $index => $question) {
                $paperQuestionData = [
                    $paperId,
                    $question['id'],
                    $index + 1,
                    1,
                    date('Y-m-d H:i:s')
                ];
                
                $stmt->execute($paperQuestionData);
                echo "<p>Inserted question " . ($index + 1) . " (ID: " . $question['id'] . ")</p>";
            }
            
            $pdo->commit();
            echo "<h3 style='color: green;'>Transaction committed successfully!</h3>";
            
            // Now verify the data
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM paper_questions WHERE paper_id = ?");
            $stmt->execute([$paperId]);
            $count = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<h3>Paper Questions Count for Paper $paperId: " . $count['count'] . "</h3>";
            
            // Get the questions with details
            $stmt = $pdo->prepare("
                SELECT pq.*, q.question_text, q.question_type
                FROM paper_questions pq
                LEFT JOIN questions q ON q.id = pq.question_id
                WHERE pq.paper_id = ?
                ORDER BY pq.question_order ASC
            ");
            $stmt->execute([$paperId]);
            $paperQuestions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "<h3>Retrieved Paper Questions:</h3>";
            echo "<pre>" . print_r($paperQuestions, true) . "</pre>";
            
        } catch (Exception $e) {
            $pdo->rollback();
            echo "<h3 style='color: red;'>Transaction failed: " . $e->getMessage() . "</h3>";
        }
        
    } else {
        echo "<p style='color: red;'>No approved questions found. Please create and approve some questions first.</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database connection failed: " . $e->getMessage() . "</p>";
}
?>
