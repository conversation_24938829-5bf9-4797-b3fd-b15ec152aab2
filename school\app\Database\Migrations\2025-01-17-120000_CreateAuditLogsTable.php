<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateAuditLogsTable extends Migration
{
    public function up()
    {
        // Create audit_logs table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'action' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'comment' => 'create, update, delete, login, logout, approve, reject, etc.',
            ],
            'entity_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'comment' => 'user, school, question, role, subscription, etc.',
            ],
            'entity_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'ID of the affected entity',
            ],
            'entity_name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'comment' => 'Name/title of the affected entity for easy identification',
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Human-readable description of the action',
            ],
            'old_values' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Previous values before change (for updates)',
            ],
            'new_values' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'New values after change (for creates/updates)',
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
                'comment' => 'IP address of the user performing the action',
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Browser/device information',
            ],
            'severity' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'critical'],
                'default' => 'low',
                'comment' => 'Severity level of the action',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['success', 'failed', 'warning'],
                'default' => 'success',
                'comment' => 'Status of the action',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('school_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('action');
        $this->forge->addKey('entity_type');
        $this->forge->addKey('entity_id');
        $this->forge->addKey('severity');
        $this->forge->addKey('status');
        $this->forge->addKey('created_at');
        
        // Add foreign keys
        $this->forge->addForeignKey('school_id', 'schools', 'id', 'SET NULL', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('audit_logs');

        // Create activity_logs table for user activity tracking
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'activity_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'comment' => 'login, logout, page_view, action, etc.',
            ],
            'activity_description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Description of the activity',
            ],
            'url' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
                'comment' => 'URL where the activity occurred',
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'session_id' => [
                'type' => 'VARCHAR',
                'constraint' => 128,
                'null' => true,
            ],
            'duration' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'comment' => 'Duration in seconds (for session activities)',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('school_id');
        $this->forge->addKey('user_id');
        $this->forge->addKey('activity_type');
        $this->forge->addKey('created_at');
        
        // Add foreign keys
        $this->forge->addForeignKey('school_id', 'schools', 'id', 'SET NULL', 'CASCADE');
        $this->forge->addForeignKey('user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('activity_logs');

        // Create superadmin_logs table for super admin specific actions
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'admin_user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'Super admin user performing the action',
            ],
            'action' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'comment' => 'Action performed by super admin',
            ],
            'target_school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'School affected by the action',
            ],
            'target_user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'comment' => 'User affected by the action',
            ],
            'description' => [
                'type' => 'TEXT',
                'null' => true,
                'comment' => 'Detailed description of the action',
            ],
            'data_before' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Data state before the action',
            ],
            'data_after' => [
                'type' => 'JSON',
                'null' => true,
                'comment' => 'Data state after the action',
            ],
            'ip_address' => [
                'type' => 'VARCHAR',
                'constraint' => 45,
                'null' => true,
            ],
            'user_agent' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'severity' => [
                'type' => 'ENUM',
                'constraint' => ['low', 'medium', 'high', 'critical'],
                'default' => 'medium',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('admin_user_id');
        $this->forge->addKey('target_school_id');
        $this->forge->addKey('target_user_id');
        $this->forge->addKey('action');
        $this->forge->addKey('severity');
        $this->forge->addKey('created_at');
        
        // Add foreign keys
        $this->forge->addForeignKey('admin_user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        $this->forge->addForeignKey('target_school_id', 'schools', 'id', 'SET NULL', 'CASCADE');
        $this->forge->addForeignKey('target_user_id', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('superadmin_logs');
    }

    public function down()
    {
        $this->forge->dropTable('superadmin_logs', true);
        $this->forge->dropTable('activity_logs', true);
        $this->forge->dropTable('audit_logs', true);
    }
}
