<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?> - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2"><?= $title ?></h1>
                <p class="text-gray-600">Manage your subscription and monitor usage</p>
            </div>

            <!-- Subscription Status Card -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold">Subscription Details</h2>
                    <span class="px-3 py-1 rounded-full text-sm font-medium <?= $subscription['status'] === 'active' ? 'bg-green-100 text-green-800' : ($subscription['status'] === 'trial' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800') ?>">
                        <?= ucfirst($subscription['status']) ?>
                    </span>
                </div>

                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Current Plan</h3>
                        <p class="text-2xl font-bold text-indigo-600"><?= $subscription['plan_display_name'] ?></p>
                        <p class="text-gray-600">₹<?= number_format($subscription['amount']) ?>/<?= $subscription['billing_cycle'] ?></p>
                    </div>

                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">
                            <?= $subscription['status'] === 'trial' ? 'Trial Ends' : 'Next Billing' ?>
                        </h3>
                        <p class="text-lg font-semibold">
                            <?php 
                            $date = $subscription['status'] === 'trial' ? $subscription['trial_ends_at'] : $subscription['expires_at'];
                            echo date('M d, Y', strtotime($date));
                            ?>
                        </p>
                        <p class="text-sm text-gray-600">
                            <?php
                            $days = ceil((strtotime($date) - time()) / (60 * 60 * 24));
                            echo $days > 0 ? "$days days remaining" : "Expired";
                            ?>
                        </p>
                    </div>
                </div>

                <div class="mt-6 flex space-x-4">
                    <a href="/subscription/upgrade" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition duration-300">
                        <i class="fas fa-arrow-up mr-2"></i>Upgrade Plan
                    </a>
                    <a href="/payment/history" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition duration-300">
                        <i class="fas fa-history mr-2"></i>Payment History
                    </a>
                </div>
            </div>

            <!-- Usage Statistics -->
            <?php if (!empty($usage)): ?>
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <h2 class="text-xl font-semibold mb-6">Usage Statistics</h2>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <?php foreach ($usage as $featureKey => $usageData): ?>
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="font-medium"><?= $usageData['name'] ?></h3>
                            <span class="text-sm px-2 py-1 rounded <?= $usageData['status'] === 'danger' ? 'bg-red-100 text-red-800' : ($usageData['status'] === 'warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800') ?>">
                                <?= ucfirst($usageData['status']) ?>
                            </span>
                        </div>
                        
                        <div class="mb-2">
                            <div class="flex justify-between text-sm text-gray-600 mb-1">
                                <span><?= $usageData['current'] ?> used</span>
                                <span><?= $usageData['limit'] === 'Unlimited' ? 'Unlimited' : $usageData['limit'] . ' limit' ?></span>
                            </div>
                            
                            <?php if ($usageData['limit'] !== 'Unlimited'): ?>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="h-2 rounded-full <?= $usageData['status'] === 'danger' ? 'bg-red-500' : ($usageData['status'] === 'warning' ? 'bg-yellow-500' : 'bg-green-500') ?>" 
                                     style="width: <?= min($usageData['percentage'], 100) ?>%"></div>
                            </div>
                            <p class="text-xs text-gray-500 mt-1"><?= $usageData['percentage'] ?>% used</p>
                            <?php else: ?>
                            <div class="w-full bg-green-200 rounded-full h-2">
                                <div class="h-2 rounded-full bg-green-500" style="width: 100%"></div>
                            </div>
                            <p class="text-xs text-green-600 mt-1">Unlimited usage</p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-6">Quick Actions</h2>
                
                <div class="grid md:grid-cols-3 gap-4">
                    <a href="/subscription/upgrade" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-300">
                        <i class="fas fa-arrow-up text-indigo-600 text-xl mr-3"></i>
                        <div>
                            <div class="font-medium">Upgrade Plan</div>
                            <div class="text-sm text-gray-600">Get more features</div>
                        </div>
                    </a>

                    <a href="/payment/history" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-300">
                        <i class="fas fa-receipt text-green-600 text-xl mr-3"></i>
                        <div>
                            <div class="font-medium">Payment History</div>
                            <div class="text-sm text-gray-600">View all payments</div>
                        </div>
                    </a>

                    <button onclick="cancelSubscription()" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition duration-300 text-left w-full">
                        <i class="fas fa-times-circle text-red-600 text-xl mr-3"></i>
                        <div>
                            <div class="font-medium">Cancel Subscription</div>
                            <div class="text-sm text-gray-600">End your subscription</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Back to Dashboard -->
            <div class="text-center mt-8">
                <a href="/schooladmin/dashboard" class="text-indigo-600 hover:text-indigo-800 font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
            </div>

        </div>
    </div>

    <!-- Cancel Confirmation Modal -->
    <div id="cancelModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 max-w-md mx-4">
            <h3 class="text-lg font-semibold mb-4">Cancel Subscription</h3>
            <p class="text-gray-600 mb-6">Are you sure you want to cancel your subscription? This action cannot be undone.</p>
            
            <div class="flex space-x-4">
                <button onclick="confirmCancel()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition duration-300">
                    Yes, Cancel
                </button>
                <button onclick="closeCancelModal()" class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition duration-300">
                    No, Keep It
                </button>
            </div>
        </div>
    </div>

    <script>
        function cancelSubscription() {
            document.getElementById('cancelModal').classList.remove('hidden');
            document.getElementById('cancelModal').classList.add('flex');
        }

        function closeCancelModal() {
            document.getElementById('cancelModal').classList.add('hidden');
            document.getElementById('cancelModal').classList.remove('flex');
        }

        function confirmCancel() {
            fetch('/subscription/cancel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: 'reason=User requested cancellation'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Subscription cancelled successfully');
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
                closeCancelModal();
            })
            .catch(error => {
                alert('An error occurred. Please try again.');
                console.error('Error:', error);
                closeCancelModal();
            });
        }
    </script>
</body>
</html>
