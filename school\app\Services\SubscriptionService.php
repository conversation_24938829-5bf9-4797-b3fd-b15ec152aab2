<?php

namespace App\Services;

use App\Models\SubscriptionModel;
use App\Models\PlanModel;
use App\Models\PlanFeatureModel;
use App\Models\UsageTrackerModel;
use App\Models\PaymentLogModel;

class SubscriptionService
{
    protected $subscriptionModel;
    protected $planModel;
    protected $planFeatureModel;
    protected $usageTrackerModel;
    protected $paymentLogModel;

    public function __construct()
    {
        $this->subscriptionModel = new SubscriptionModel();
        $this->planModel = new PlanModel();
        $this->planFeatureModel = new PlanFeatureModel();
        $this->usageTrackerModel = new UsageTrackerModel();
        $this->paymentLogModel = new PaymentLogModel();
    }

    /**
     * Check if school can use a specific feature
     */
    public function canUseFeature($schoolId, $featureKey, $requestedAmount = 1)
    {
        // Check if subscription is active
        if ($this->subscriptionModel->isSubscriptionExpired($schoolId)) {
            return false;
        }

        // Check usage limits
        return $this->usageTrackerModel->canUseFeature($schoolId, $featureKey, $requestedAmount);
    }

    /**
     * Use a feature (increment usage)
     */
    public function useFeature($schoolId, $featureKey, $amount = 1)
    {
        if (!$this->canUseFeature($schoolId, $featureKey, $amount)) {
            return false;
        }

        return $this->usageTrackerModel->incrementUsage($schoolId, $featureKey, $amount);
    }

    /**
     * Release feature usage (decrement when something is deleted)
     */
    public function releaseFeature($schoolId, $featureKey, $amount = 1)
    {
        return $this->usageTrackerModel->decrementUsage($schoolId, $featureKey, $amount);
    }

    /**
     * Get school's current plan and features
     */
    public function getSchoolPlan($schoolId)
    {
        $subscription = $this->subscriptionModel->getCurrentSubscription($schoolId);
        
        if (!$subscription) {
            return null;
        }

        $features = $this->planFeatureModel->getPlanFeaturesArray($subscription['plan_id']);
        $usage = $this->usageTrackerModel->getSchoolUsage($schoolId);

        return [
            'subscription' => $subscription,
            'features' => $features,
            'usage' => $usage
        ];
    }

    /**
     * Create subscription for new school
     */
    public function createSchoolSubscription($schoolId, $planName = 'free', $billingCycle = 'monthly', $startTrial = true)
    {
        $plan = $this->planModel->getPlanByName($planName);
        
        if (!$plan) {
            return false;
        }

        // Create subscription
        $subscriptionId = $this->subscriptionModel->createSubscription(
            $schoolId, 
            $plan['id'], 
            $billingCycle, 
            0, // Amount will be set during payment
            $startTrial
        );

        if ($subscriptionId) {
            // Initialize usage tracking
            $this->usageTrackerModel->initializeSchoolUsage($schoolId, $plan['id']);
            return $subscriptionId;
        }

        return false;
    }

    /**
     * Upgrade/Downgrade school subscription
     */
    public function changeSchoolPlan($schoolId, $newPlanName, $billingCycle = 'monthly')
    {
        $newPlan = $this->planModel->getPlanByName($newPlanName);
        
        if (!$newPlan) {
            return false;
        }

        // Change subscription
        $subscriptionId = $this->subscriptionModel->changeSubscription($schoolId, $newPlan['id'], $billingCycle);

        if ($subscriptionId) {
            // Update usage tracking for new plan
            $this->usageTrackerModel->initializeSchoolUsage($schoolId, $newPlan['id']);
            return $subscriptionId;
        }

        return false;
    }

    /**
     * Process payment and activate subscription
     */
    public function processPayment($schoolId, $subscriptionId, $amount, $paymentGateway, $paymentMethod = null)
    {
        // Generate transaction ID
        $transactionId = $this->paymentLogModel->generateTransactionId();

        // Create payment log
        $paymentLogId = $this->paymentLogModel->createPaymentLog(
            $schoolId,
            $subscriptionId,
            $transactionId,
            $paymentGateway,
            $amount,
            'INR',
            $paymentMethod
        );

        if ($paymentLogId) {
            return $transactionId;
        }

        return false;
    }

    /**
     * Complete payment and activate subscription
     */
    public function completePayment($transactionId, $gatewayTransactionId, $gatewayResponse = null)
    {
        $payment = $this->paymentLogModel->getPaymentByTransactionId($transactionId);
        
        if (!$payment) {
            return false;
        }

        // Update payment status
        $this->paymentLogModel->updatePaymentStatus(
            $transactionId,
            'completed',
            $gatewayTransactionId,
            $gatewayResponse
        );

        // Activate/renew subscription
        return $this->subscriptionModel->renewSubscription(
            $payment['subscription_id'],
            $payment['amount'],
            $payment['payment_method']
        );
    }

    /**
     * Handle failed payment
     */
    public function failPayment($transactionId, $reason = null, $gatewayResponse = null)
    {
        return $this->paymentLogModel->updatePaymentStatus(
            $transactionId,
            'failed',
            null,
            $gatewayResponse,
            $reason
        );
    }

    /**
     * Get feature usage summary for school
     */
    public function getUsageSummary($schoolId)
    {
        $planInfo = $this->getSchoolPlan($schoolId);
        
        if (!$planInfo) {
            return null;
        }

        $summary = [];
        
        foreach ($planInfo['features'] as $featureKey => $feature) {
            if (strpos($featureKey, 'max_') === 0) {
                $usage = $planInfo['usage'][$featureKey] ?? ['current' => 0, 'limit' => null];
                
                $summary[$featureKey] = [
                    'name' => $feature['name'],
                    'current' => $usage['current'],
                    'limit' => $feature['unlimited'] ? 'Unlimited' : $feature['value'],
                    'percentage' => $usage['percentage'] ?? 0,
                    'status' => $this->getUsageStatus($usage['current'], $feature['unlimited'] ? null : $feature['value'])
                ];
            }
        }

        return $summary;
    }

    /**
     * Get usage status (safe, warning, danger)
     */
    private function getUsageStatus($current, $limit)
    {
        if ($limit === null) {
            return 'unlimited';
        }

        $percentage = ($current / $limit) * 100;

        if ($percentage >= 100) {
            return 'danger';
        } elseif ($percentage >= 80) {
            return 'warning';
        } else {
            return 'safe';
        }
    }

    /**
     * Get schools approaching limits (for notifications)
     */
    public function getSchoolsApproachingLimits($threshold = 80)
    {
        return $this->usageTrackerModel->getSchoolsApproachingLimits($threshold);
    }

    /**
     * Get expiring subscriptions
     */
    public function getExpiringSubscriptions($days = 7)
    {
        return $this->subscriptionModel->getExpiringSubscriptions($days);
    }

    /**
     * Get trial subscriptions ending soon
     */
    public function getTrialsEndingSoon($days = 3)
    {
        return $this->subscriptionModel->getTrialsEndingSoon($days);
    }

    /**
     * Cancel school subscription
     */
    public function cancelSubscription($schoolId, $reason = null)
    {
        return $this->subscriptionModel->cancelCurrentSubscription($schoolId);
    }

    /**
     * Get subscription analytics for admin
     */
    public function getSubscriptionAnalytics()
    {
        $db = \Config\Database::connect();
        
        // Get subscription statistics
        $subscriptionStats = $db->query("
            SELECT 
                plans.display_name,
                COUNT(subscriptions.id) as subscription_count,
                SUM(CASE WHEN subscriptions.status = 'active' THEN 1 ELSE 0 END) as active_count,
                SUM(CASE WHEN subscriptions.status = 'trial' THEN 1 ELSE 0 END) as trial_count,
                SUM(CASE WHEN subscriptions.status = 'expired' THEN 1 ELSE 0 END) as expired_count
            FROM plans 
            LEFT JOIN subscriptions ON subscriptions.plan_id = plans.id
            GROUP BY plans.id, plans.display_name
            ORDER BY plans.sort_order
        ")->getResultArray();

        // Get revenue statistics
        $revenueStats = $this->paymentLogModel->getPaymentStatistics();

        // Get usage statistics
        $usageStats = $this->usageTrackerModel->getUsageStatistics();

        return [
            'subscriptions' => $subscriptionStats,
            'revenue' => $revenueStats,
            'usage' => $usageStats
        ];
    }
}
