<?php

namespace App\Models;

use CodeIgniter\Model;

class PaymentLogModel extends Model
{
    protected $table = 'payment_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'subscription_id', 'transaction_id', 'gateway_transaction_id',
        'payment_gateway', 'amount', 'currency', 'status', 'payment_method',
        'gateway_response', 'notes', 'processed_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'subscription_id' => 'required|integer',
        'transaction_id' => 'required|max_length[255]',
        'payment_gateway' => 'required|max_length[50]',
        'amount' => 'required|decimal',
        'currency' => 'required|max_length[3]',
        'status' => 'required|in_list[pending,completed,failed,refunded,cancelled]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Create payment log entry
     */
    public function createPaymentLog($schoolId, $subscriptionId, $transactionId, $gateway, $amount, $currency = 'INR', $paymentMethod = null)
    {
        return $this->insert([
            'school_id' => $schoolId,
            'subscription_id' => $subscriptionId,
            'transaction_id' => $transactionId,
            'payment_gateway' => $gateway,
            'amount' => $amount,
            'currency' => $currency,
            'status' => 'pending',
            'payment_method' => $paymentMethod
        ]);
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus($transactionId, $status, $gatewayTransactionId = null, $gatewayResponse = null, $notes = null)
    {
        $updateData = [
            'status' => $status,
            'processed_at' => date('Y-m-d H:i:s')
        ];

        if ($gatewayTransactionId) {
            $updateData['gateway_transaction_id'] = $gatewayTransactionId;
        }

        if ($gatewayResponse) {
            $updateData['gateway_response'] = is_array($gatewayResponse) ? json_encode($gatewayResponse) : $gatewayResponse;
        }

        if ($notes) {
            $updateData['notes'] = $notes;
        }

        return $this->where('transaction_id', $transactionId)->set($updateData)->update();
    }

    /**
     * Get payment by transaction ID
     */
    public function getPaymentByTransactionId($transactionId)
    {
        return $this->where('transaction_id', $transactionId)->first();
    }

    /**
     * Get recent transactions for a school
     */
    public function getRecentTransactions($schoolId, $limit = 5)
    {
        return $this->select('payment_logs.*, plans.display_name as plan_name')
                   ->join('subscriptions', 'subscriptions.id = payment_logs.subscription_id', 'left')
                   ->join('plans', 'plans.id = subscriptions.plan_id', 'left')
                   ->where('payment_logs.school_id', $schoolId)
                   ->orderBy('payment_logs.created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get payment by gateway transaction ID
     */
    public function getPaymentByGatewayTransactionId($gatewayTransactionId)
    {
        return $this->where('gateway_transaction_id', $gatewayTransactionId)->first();
    }

    /**
     * Get school payment history with filters
     */
    public function getSchoolPaymentHistory($schoolId, $status = null, $dateFrom = null, $dateTo = null, $limit = 50)
    {
        $builder = $this->select('payment_logs.*, subscriptions.billing_cycle, plans.display_name as plan_name')
                        ->join('subscriptions', 'subscriptions.id = payment_logs.subscription_id')
                        ->join('plans', 'plans.id = subscriptions.plan_id')
                        ->where('payment_logs.school_id', $schoolId);

        // Apply filters
        if ($status) {
            $builder->where('payment_logs.status', $status);
        }

        if ($dateFrom) {
            $builder->where('payment_logs.created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $builder->where('payment_logs.created_at <=', $dateTo . ' 23:59:59');
        }

        return $builder->orderBy('payment_logs.created_at', 'DESC')
                      ->limit($limit)
                      ->findAll();
    }

    /**
     * Get payment summary for a school
     */
    public function getPaymentSummary($schoolId)
    {
        $summary = $this->select('
            COUNT(*) as total_transactions,
            SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_paid,
            MAX(CASE WHEN status = "completed" THEN processed_at ELSE NULL END) as last_payment
        ')
        ->where('school_id', $schoolId)
        ->first();

        return [
            'total_transactions' => (int)($summary['total_transactions'] ?? 0),
            'total_paid' => (float)($summary['total_paid'] ?? 0),
            'last_payment' => $summary['last_payment']
        ];
    }

    /**
     * Get transaction details with related data
     */
    public function getTransactionDetails($transactionId, $schoolId)
    {
        return $this->select('
            payment_logs.*,
            subscriptions.billing_cycle,
            plans.display_name as plan_name,
            plans.name as plan_code
        ')
        ->join('subscriptions', 'subscriptions.id = payment_logs.subscription_id')
        ->join('plans', 'plans.id = subscriptions.plan_id')
        ->where('payment_logs.id', $transactionId)
        ->where('payment_logs.school_id', $schoolId)
        ->first();
    }

    /**
     * Get successful payments for a school
     */
    public function getSuccessfulPayments($schoolId)
    {
        return $this->where('school_id', $schoolId)
                   ->where('status', 'completed')
                   ->orderBy('processed_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get failed payments for a school
     */
    public function getFailedPayments($schoolId)
    {
        return $this->where('school_id', $schoolId)
                   ->where('status', 'failed')
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get pending payments (for cleanup/retry)
     */
    public function getPendingPayments($olderThanMinutes = 30)
    {
        $cutoffTime = date('Y-m-d H:i:s', strtotime("-{$olderThanMinutes} minutes"));
        
        return $this->where('status', 'pending')
                   ->where('created_at <', $cutoffTime)
                   ->findAll();
    }

    /**
     * Get payment statistics for admin dashboard
     */
    public function getPaymentStatistics($startDate = null, $endDate = null)
    {
        $builder = $this->select('
            COUNT(*) as total_transactions,
            SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as successful_transactions,
            SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_transactions,
            SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_revenue,
            AVG(CASE WHEN status = "completed" THEN amount ELSE NULL END) as avg_transaction_amount,
            payment_gateway,
            currency
        ');

        if ($startDate) {
            $builder->where('created_at >=', $startDate);
        }

        if ($endDate) {
            $builder->where('created_at <=', $endDate);
        }

        return $builder->groupBy(['payment_gateway', 'currency'])->findAll();
    }

    /**
     * Get monthly revenue report
     */
    public function getMonthlyRevenue($year = null)
    {
        if (!$year) {
            $year = date('Y');
        }

        return $this->select('
            MONTH(processed_at) as month,
            YEAR(processed_at) as year,
            COUNT(*) as transaction_count,
            SUM(amount) as total_revenue,
            currency
        ')
        ->where('status', 'completed')
        ->where('YEAR(processed_at)', $year)
        ->groupBy(['YEAR(processed_at)', 'MONTH(processed_at)', 'currency'])
        ->orderBy('month', 'ASC')
        ->findAll();
    }

    /**
     * Get top paying schools
     */
    public function getTopPayingSchools($limit = 10)
    {
        return $this->select('
            payment_logs.school_id,
            schools.name as school_name,
            schools.email as school_email,
            COUNT(*) as payment_count,
            SUM(payment_logs.amount) as total_paid,
            MAX(payment_logs.processed_at) as last_payment
        ')
        ->join('schools', 'schools.id = payment_logs.school_id')
        ->where('payment_logs.status', 'completed')
        ->groupBy('payment_logs.school_id')
        ->orderBy('total_paid', 'DESC')
        ->limit($limit)
        ->findAll();
    }

    /**
     * Generate unique transaction ID
     */
    public function generateTransactionId($prefix = 'TXN')
    {
        do {
            $transactionId = $prefix . '_' . date('Ymd') . '_' . strtoupper(uniqid());
        } while ($this->getPaymentByTransactionId($transactionId));

        return $transactionId;
    }

    /**
     * Mark payment as refunded
     */
    public function markAsRefunded($transactionId, $refundAmount = null, $refundNotes = null)
    {
        $payment = $this->getPaymentByTransactionId($transactionId);
        
        if (!$payment) {
            return false;
        }

        $updateData = [
            'status' => 'refunded',
            'processed_at' => date('Y-m-d H:i:s')
        ];

        if ($refundAmount) {
            $updateData['amount'] = -abs($refundAmount); // Negative amount for refund
        }

        if ($refundNotes) {
            $updateData['notes'] = $refundNotes;
        }

        return $this->update($payment['id'], $updateData);
    }

    /**
     * Get refunded payments
     */
    public function getRefundedPayments($schoolId = null)
    {
        $builder = $this->where('status', 'refunded');
        
        if ($schoolId) {
            $builder->where('school_id', $schoolId);
        }

        return $builder->orderBy('processed_at', 'DESC')->findAll();
    }
}
