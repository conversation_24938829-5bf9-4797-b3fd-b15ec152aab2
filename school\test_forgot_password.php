<?php
/**
 * Test Forgot Password Functionality
 * Run this from the school directory: php test_forgot_password.php
 */

require_once 'vendor/autoload.php';

echo "=== Testing Forgot Password Functionality ===\n\n";

try {
    // Test 1: Check if password reset tables exist
    echo "1. Testing database tables:\n";
    
    $host = 'localhost';
    $dbname = 'school';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check password_resets table
    $stmt = $pdo->query("SHOW TABLES LIKE 'password_resets'");
    if ($stmt->rowCount() > 0) {
        echo "   - password_resets table exists ✓\n";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE password_resets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $expectedColumns = ['id', 'email', 'token', 'user_type', 'expires_at', 'used', 'created_at', 'updated_at'];
        
        $missingColumns = array_diff($expectedColumns, $columns);
        if (empty($missingColumns)) {
            echo "   - password_resets table structure is correct ✓\n";
        } else {
            echo "   - Missing columns in password_resets: " . implode(', ', $missingColumns) . " ❌\n";
        }
    } else {
        echo "   - password_resets table missing ❌\n";
        exit(1);
    }
    
    // Check login_attempts table
    $stmt = $pdo->query("SHOW TABLES LIKE 'login_attempts'");
    if ($stmt->rowCount() > 0) {
        echo "   - login_attempts table exists ✓\n";
    } else {
        echo "   - login_attempts table missing ❌\n";
    }
    
    // Check user_tokens table
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_tokens'");
    if ($stmt->rowCount() > 0) {
        echo "   - user_tokens table exists ✓\n";
    } else {
        echo "   - user_tokens table missing ❌\n";
    }

    // Test 2: Check if we have test users
    echo "\n2. Testing user data:\n";
    
    // Check for school admin
    $stmt = $pdo->prepare("SELECT id, name, email FROM schools WHERE status = 'active' LIMIT 1");
    $stmt->execute();
    $schoolAdmin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($schoolAdmin) {
        echo "   - Found school admin: " . $schoolAdmin['email'] . " ✓\n";
    } else {
        echo "   - No active school admin found ⚠️\n";
    }
    
    // Check for staff user
    $stmt = $pdo->prepare("SELECT id, name, email, school_id FROM users WHERE status = 'active' AND is_deleted = 0 LIMIT 1");
    $stmt->execute();
    $staffUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($staffUser) {
        echo "   - Found staff user: " . $staffUser['email'] . " ✓\n";
    } else {
        echo "   - No active staff user found ⚠️\n";
    }

    // Test 3: Test password reset token generation
    echo "\n3. Testing password reset token generation:\n";
    
    if ($schoolAdmin) {
        // Test school admin reset token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        $stmt = $pdo->prepare("INSERT INTO password_resets (email, token, user_type, expires_at, used) VALUES (?, ?, 'school', ?, 0)");
        $result = $stmt->execute([$schoolAdmin['email'], $token, $expiresAt]);
        
        if ($result) {
            echo "   - School admin reset token created ✓\n";
            echo "   - Token: " . substr($token, 0, 16) . "...\n";
            echo "   - Expires: " . $expiresAt . "\n";
            
            // Test token verification
            $stmt = $pdo->prepare("SELECT * FROM password_resets WHERE token = ? AND used = 0 AND expires_at > NOW()");
            $stmt->execute([$token]);
            $resetData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($resetData) {
                echo "   - Token verification successful ✓\n";
            } else {
                echo "   - Token verification failed ❌\n";
            }
        } else {
            echo "   - Failed to create school admin reset token ❌\n";
        }
    }
    
    if ($staffUser) {
        // Test staff user reset token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        $stmt = $pdo->prepare("INSERT INTO password_resets (email, token, user_type, expires_at, used) VALUES (?, ?, 'user', ?, 0)");
        $result = $stmt->execute([$staffUser['email'], $token, $expiresAt]);
        
        if ($result) {
            echo "   - Staff user reset token created ✓\n";
            echo "   - Token: " . substr($token, 0, 16) . "...\n";
        } else {
            echo "   - Failed to create staff user reset token ❌\n";
        }
    }

    // Test 4: Test login attempt logging
    echo "\n4. Testing login attempt logging:\n";
    
    $testEmail = '<EMAIL>';
    $testIP = '127.0.0.1';
    $testUserAgent = 'Test User Agent';
    
    $stmt = $pdo->prepare("INSERT INTO login_attempts (email, ip_address, user_agent, success, failure_reason, user_type) VALUES (?, ?, ?, 0, 'Invalid password', 'user')");
    $result = $stmt->execute([$testEmail, $testIP, $testUserAgent]);
    
    if ($result) {
        echo "   - Login attempt logged successfully ✓\n";
        
        // Test failed attempts count
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM login_attempts WHERE email = ? AND success = 0 AND created_at > DATE_SUB(NOW(), INTERVAL 15 MINUTE)");
        $stmt->execute([$testEmail]);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "   - Failed attempts count for {$testEmail}: {$count} ✓\n";
    } else {
        echo "   - Failed to log login attempt ❌\n";
    }

    // Test 5: Clean up test data
    echo "\n5. Cleaning up test data:\n";
    
    $stmt = $pdo->prepare("DELETE FROM password_resets WHERE email IN (?, ?)");
    $stmt->execute([$schoolAdmin['email'] ?? '', $staffUser['email'] ?? '']);
    echo "   - Cleaned up password reset tokens ✓\n";
    
    $stmt = $pdo->prepare("DELETE FROM login_attempts WHERE email = ?");
    $stmt->execute([$testEmail]);
    echo "   - Cleaned up test login attempts ✓\n";

    echo "\n=== Forgot Password Test Results ===\n";
    echo "✅ Database tables created successfully\n";
    echo "✅ Password reset token generation working\n";
    echo "✅ Login attempt logging working\n";
    echo "✅ All core functionality is ready\n";
    
    echo "\n📋 Next Steps:\n";
    echo "1. Open your browser and go to any login page\n";
    echo "2. Click 'Forgot password?' link\n";
    echo "3. Enter a registered email address\n";
    echo "4. Check your email for the reset link\n";
    echo "5. Follow the link to reset your password\n";
    
    echo "\n🔗 Test URLs:\n";
    echo "- Landing Page (with modals): " . "http://localhost/schoolquestionbank/school/public/\n";
    echo "- SuperAdmin Login: " . "http://localhost/schoolquestionbank/school/public/login\n";
    echo "- School Admin Login: " . "http://localhost/schoolquestionbank/school/public/login/schooladmin\n";
    echo "- Staff Login: " . "http://localhost/schoolquestionbank/school/public/staff/login\n";
    echo "\n📋 How to Test:\n";
    echo "1. Go to any login page\n";
    echo "2. Click 'Forgot password?' link\n";
    echo "3. Modal will open (no page redirect)\n";
    echo "4. Enter email and submit\n";
    echo "5. Check email for reset link\n";
    
    if ($schoolAdmin) {
        echo "\n👤 Test School Admin Email: " . $schoolAdmin['email'] . "\n";
    }
    if ($staffUser) {
        echo "👤 Test Staff Email: " . $staffUser['email'] . "\n";
    }

} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
