<?php
// Simple email test script
// Access via: http://localhost:8080/test_email.php

// Include CodeIgniter bootstrap
require_once '../app/Config/Paths.php';
$paths = new Config\Paths();

// Define FCPATH if not defined
if (!defined('FCPATH')) {
    define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
}

// Bootstrap CodeIgniter
require_once $paths->systemDirectory . '/bootstrap.php';

// Initialize CodeIgniter
$app = Config\Services::codeigniter();
$app->initialize();

echo "<h2>Email Test</h2>";

try {
    // Load EmailService
    $emailService = new \App\Services\EmailService();
    
    // Test data
    $testSchoolEmail = '<EMAIL>'; // Change this to your email for testing
    $testSchoolName = 'Test School';
    $testRejectionReason = 'This is a test rejection email to verify email functionality is working.';
    
    echo "<p><strong>Testing rejection email...</strong></p>";
    echo "<p>To: $testSchoolEmail</p>";
    echo "<p>School: $testSchoolName</p>";
    echo "<p>Reason: $testRejectionReason</p>";
    
    // Send test email
    $result = $emailService->sendSchoolRejectionEmail(
        $testSchoolEmail,
        $testSchoolName,
        $testRejectionReason,
        1
    );
    
    if ($result) {
        echo "<p style='color: green;'><strong>✅ Email sent successfully!</strong></p>";
        echo "<p>Check the email inbox for: $testSchoolEmail</p>";
    } else {
        echo "<p style='color: red;'><strong>❌ Failed to send email</strong></p>";
        echo "<p>Check the logs for more details.</p>";
    }
    
} catch (\Exception $e) {
    echo "<p style='color: red;'><strong>❌ Error: " . $e->getMessage() . "</strong></p>";
}

echo "<hr>";
echo "<p><small>Test completed at: " . date('Y-m-d H:i:s') . "</small></p>";
?>
