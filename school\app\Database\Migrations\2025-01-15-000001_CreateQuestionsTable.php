<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateQuestionsTable extends Migration
{
    public function up()
    {
        // Questions table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'staff_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'standard' => [
                'type' => 'INT',
                'constraint' => 2,
            ],
            'subject' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'question_type' => [
                'type' => 'ENUM',
                'constraint' => ['multiple_choice', 'short_answer', 'long_answer', 'essay'],
            ],
            'difficulty' => [
                'type' => 'ENUM',
                'constraint' => ['easy', 'medium', 'hard'],
            ],
            'marks' => [
                'type' => 'INT',
                'constraint' => 3,
            ],
            'question_text' => [
                'type' => 'TEXT',
            ],
            'option_a' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'option_b' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'option_c' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'option_d' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'correct_answer' => [
                'type' => 'CHAR',
                'constraint' => 1,
                'null' => true,
            ],
            'answer' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['draft', 'pending', 'approved', 'rejected'],
                'default' => 'draft',
            ],
            'admin_feedback' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'reviewed_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
            ],
            'reviewed_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('school_id');
        $this->forge->addKey('staff_id');
        $this->forge->addKey('status');
        $this->forge->addKey('standard');
        $this->forge->addKey('subject');
        
        $this->forge->addForeignKey('school_id', 'schools', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('staff_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('reviewed_by', 'users', 'id', 'SET NULL', 'CASCADE');
        
        $this->forge->createTable('questions');

        // Subjects table for managing school subjects
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'standards' => [
                'type' => 'JSON',
                'comment' => 'Array of standards this subject is taught in',
            ],
            'is_active' => [
                'type' => 'BOOLEAN',
                'default' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('school_id');
        $this->forge->addUniqueKey(['school_id', 'name']);
        
        $this->forge->addForeignKey('school_id', 'schools', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('subjects');

        // Staff Subject Assignments table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'staff_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'subject_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'standards' => [
                'type' => 'JSON',
                'comment' => 'Array of standards this staff can create questions for in this subject',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addKey('school_id');
        $this->forge->addKey('staff_id');
        $this->forge->addKey('subject_id');
        $this->forge->addUniqueKey(['staff_id', 'subject_id']);
        
        $this->forge->addForeignKey('school_id', 'schools', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('staff_id', 'users', 'id', 'CASCADE', 'CASCADE');
        $this->forge->addForeignKey('subject_id', 'subjects', 'id', 'CASCADE', 'CASCADE');
        
        $this->forge->createTable('staff_subject_assignments');
    }

    public function down()
    {
        $this->forge->dropTable('staff_subject_assignments');
        $this->forge->dropTable('subjects');
        $this->forge->dropTable('questions');
    }
}
