<?php

namespace App\Services;

use Razorpay\Api\Api;
use Config\Razorpay as RazorpayConfig;
use Exception;

class RazorpayService
{
    protected $api;
    protected $config;

    public function __construct()
    {
        $this->config = new RazorpayConfig();
        $this->api = new Api($this->config->getKeyId(), $this->config->getKeySecret());
    }

    /**
     * Create a Razorpay order
     */
    public function createOrder($amount, $currency = 'INR', $receipt = null, $notes = [])
    {
        try {
            // Convert amount to paise (Razorpay expects amount in smallest currency unit)
            $amountInPaise = $amount * 100;

            $orderData = [
                'receipt' => $receipt ?: 'order_' . time(),
                'amount' => $amountInPaise,
                'currency' => $currency,
                'notes' => $notes
            ];

            $order = $this->api->order->create($orderData);
            
            return [
                'success' => true,
                'order' => $order,
                'order_id' => $order['id']
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify payment signature
     */
    public function verifyPaymentSignature($orderId, $paymentId, $signature)
    {
        try {
            $attributes = [
                'razorpay_order_id' => $orderId,
                'razorpay_payment_id' => $paymentId,
                'razorpay_signature' => $signature
            ];

            $this->api->utility->verifyPaymentSignature($attributes);
            return true;
        } catch (Exception $e) {
            log_message('error', 'Razorpay signature verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment details
     */
    public function getPayment($paymentId)
    {
        try {
            $payment = $this->api->payment->fetch($paymentId);
            return [
                'success' => true,
                'payment' => $payment
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Refund a payment
     */
    public function refundPayment($paymentId, $amount = null, $notes = [])
    {
        try {
            $refundData = ['notes' => $notes];
            
            if ($amount !== null) {
                $refundData['amount'] = $amount * 100; // Convert to paise
            }

            $refund = $this->api->payment->fetch($paymentId)->refund($refundData);
            
            return [
                'success' => true,
                'refund' => $refund
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get order details
     */
    public function getOrder($orderId)
    {
        try {
            $order = $this->api->order->fetch($orderId);
            return [
                'success' => true,
                'order' => $order
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature($payload, $signature)
    {
        try {
            $this->api->utility->verifyWebhookSignature($payload, $signature, $this->config->webhookSecret);
            return true;
        } catch (Exception $e) {
            log_message('error', 'Webhook signature verification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get payment options for frontend
     */
    public function getPaymentOptions($orderId, $amount, $schoolName, $schoolEmail)
    {
        $options = $this->config->getPaymentOptions();
        
        return array_merge($options, [
            'key' => $this->config->getKeyId(),
            'order_id' => $orderId,
            'amount' => $amount * 100, // Convert to paise
            'name' => 'QuestionBank Pro',
            'description' => 'Subscription Payment',
            'image' => base_url('assets/images/logo.png'),
            'prefill' => [
                'name' => $schoolName,
                'email' => $schoolEmail
            ],
            'notes' => [
                'school_name' => $schoolName,
                'subscription_type' => 'monthly'
            ],
            'theme' => [
                'color' => '#4F46E5'
            ]
        ]);
    }
}
