<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSuperAdminUsersTable extends Migration
{
    public function up()
    {
        // Create superadmin_users table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Super admin full name',
            ],
            'email' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Super admin email address',
            ],
            'password' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'comment' => 'Hashed password',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'default' => 'active',
                'comment' => 'Account status',
            ],
            'last_login_at' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Last successful login timestamp',
            ],
            'login_attempts' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'comment' => 'Failed login attempts counter',
            ],
            'locked_until' => [
                'type' => 'DATETIME',
                'null' => true,
                'comment' => 'Account lock expiry time',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);

        $this->forge->addKey('id', true);
        $this->forge->addUniqueKey('email');
        $this->forge->addKey('status');
        $this->forge->addKey('last_login_at');
        $this->forge->createTable('superadmin_users');

        // Insert default superadmin user
        $this->db->table('superadmin_users')->insert([
            'name' => 'Super Administrator',
            'email' => '<EMAIL>',
            'password' => password_hash('1234', PASSWORD_DEFAULT),
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function down()
    {
        $this->forge->dropTable('superadmin_users');
    }
}
