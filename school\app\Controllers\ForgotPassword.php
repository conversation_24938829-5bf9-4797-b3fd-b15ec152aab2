<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\SchoolModel;
use App\Models\PasswordResetModel;
use App\Models\LoginAttemptModel;
use App\Services\EmailService;
use App\Services\AuditLogger;

class ForgotPassword extends BaseController
{
    protected $userModel;
    protected $schoolModel;
    protected $passwordResetModel;
    protected $loginAttemptModel;
    protected $emailService;
    protected $auditLogger;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->schoolModel = new SchoolModel();
        $this->passwordResetModel = new PasswordResetModel();
        $this->loginAttemptModel = new LoginAttemptModel();
        $this->emailService = new EmailService();
        $this->auditLogger = new AuditLogger();
    }

    /**
     * Show forgot password form
     */
    public function index($userType = 'user')
    {
        $data = [
            'userType' => $userType,
            'title' => $userType === 'school' ? 'School Admin Password Reset' : 'Staff Password Reset'
        ];

        return view('auth/forgot_password', $data);
    }

    /**
     * Process forgot password request
     */
    public function sendResetLink()
    {
        try {
            // Set JSON response header
            $this->response->setContentType('application/json');

            $request = $this->request->getPost();
            $email = $request['email'] ?? '';
            $userType = $request['user_type'] ?? 'user';
            $isAjax = $this->request->isAJAX();

            // Log the request for debugging
            log_message('info', 'Forgot password request: ' . json_encode([
                'email' => $email,
                'userType' => $userType,
                'isAjax' => $isAjax,
                'requestData' => $request
            ]));

        // Validate input
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $response = [
                'success' => false,
                'message' => 'Please provide a valid email address.'
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->back()->with('error', $response['message']);
        }

        // Check for rate limiting
        $ipAddress = $this->request->getIPAddress();
        if ($this->loginAttemptModel->isIPLocked($ipAddress, 5, 15)) {
            $response = [
                'success' => false,
                'message' => 'Too many requests from your IP. Please try again later.'
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->back()->with('error', $response['message']);
        }

        // Check if email has recent reset request (prevent spam)
        if ($this->passwordResetModel->hasRecentResetRequest($email, $userType, 2)) {
            $response = [
                'success' => false,
                'message' => 'A password reset email was already sent recently. Please check your email or wait a few minutes before trying again.'
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->back()->with('error', $response['message']);
        }

        // Check if user exists
        $user = null;
        if ($userType === 'school') {
            $user = $this->schoolModel->where('email', $email)->where('status !=', 'rejected')->first();
        } else {
            $user = $this->userModel->where('email', $email)->where('status', 'active')->where('is_deleted', false)->first();
        }

        // Always show success message for security (don't reveal if email exists)
        $successMessage = 'If an account with that email exists, we have sent a password reset link to your email address.';

        if ($user) {
            try {
                // Create reset token
                $resetToken = $this->passwordResetModel->createResetToken($email, $userType);

                if ($resetToken) {
                    // Send reset email
                    $emailSent = $this->emailService->sendPasswordResetEmail(
                        $email,
                        $user['name'],
                        $resetToken,
                        $userType,
                        $userType === 'school' ? $user['id'] : $user['school_id'],
                        $user['id']
                    );

                    if ($emailSent) {
                        // Log the password reset request (non-critical, don't fail if logging fails)
                        try {
                            $this->auditLogger->logActivity(
                                $user['id'],
                                $userType === 'school' ? $user['id'] : $user['school_id'],
                                'password_reset_requested',
                                'Password reset requested for email: ' . $email,
                                null,
                                null,
                                'medium'
                            );
                        } catch (\Exception $e) {
                            // Log the error but don't fail the password reset process
                            log_message('error', 'Failed to log password reset request activity: ' . $e->getMessage());
                        }

                        log_message('info', "Password reset email sent to: {$email} (User Type: {$userType})");
                    } else {
                        log_message('error', "Failed to send password reset email to: {$email}");
                    }
                }
            } catch (\Exception $e) {
                log_message('error', 'Password reset error: ' . $e->getMessage());
            }
        }

        // Always return success message for security
        $response = [
            'success' => true,
            'message' => $successMessage
        ];

        if ($isAjax) {
            return $this->response->setJSON($response);
        } else {
            return redirect()->back()->with('success', $response['message']);
        }

        } catch (\Exception $e) {
            log_message('error', 'Forgot password error: ' . $e->getMessage());

            $response = [
                'success' => false,
                'message' => 'An error occurred while processing your request. Please try again.'
            ];

            if ($this->request->isAJAX()) {
                return $this->response->setJSON($response);
            } else {
                return redirect()->back()->with('error', $response['message']);
            }
        }
    }

    /**
     * Handle reset password links from email (with token as URL parameter)
     */
    public function resetFormFromUrl()
    {
        $token = $this->request->getGet('token');

        if (!$token) {
            return redirect()->to('/?error=missing_token')->with('error', 'Reset token is missing.');
        }

        return $this->resetForm($token);
    }

    /**
     * Show reset password form - redirect to landing page with token
     */
    public function resetForm($token)
    {
        // Verify token
        $resetData = $this->passwordResetModel->verifyResetToken($token);

        if (!$resetData) {
            return redirect()->to('/?error=invalid_token')->with('error', 'Invalid or expired password reset link.');
        }

        // Redirect to landing page with reset token parameter
        return redirect()->to('/?reset_token=' . $token . '&email=' . urlencode($resetData['email']) . '&user_type=' . $resetData['user_type']);
    }

    /**
     * Process password reset
     */
    public function resetPassword()
    {
        // Set JSON response header
        $this->response->setContentType('application/json');

        $request = $this->request->getPost();
        $token = $request['token'] ?? '';
        $newPassword = $request['password'] ?? '';
        $confirmPassword = $request['confirm_password'] ?? '';
        $isAjax = $this->request->isAJAX();

        // Validate input
        if (empty($token) || empty($newPassword) || empty($confirmPassword)) {
            $response = [
                'success' => false,
                'message' => 'All fields are required.'
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->back()->with('error', $response['message']);
        }

        if ($newPassword !== $confirmPassword) {
            $response = [
                'success' => false,
                'message' => 'Passwords do not match.'
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->back()->with('error', $response['message']);
        }

        // Enhanced password validation
        $passwordErrors = $this->validatePasswordStrength($newPassword);
        if (!empty($passwordErrors)) {
            $response = [
                'success' => false,
                'message' => 'Password does not meet requirements: ' . implode(', ', $passwordErrors)
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->back()->with('error', $response['message']);
        }

        // Verify token
        $resetData = $this->passwordResetModel->verifyResetToken($token);

        if (!$resetData) {
            $response = [
                'success' => false,
                'message' => 'Invalid or expired password reset link.'
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->to('/login')->with('error', $response['message']);
        }

        try {
            // Update password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $updateSuccess = false;

            if ($resetData['user_type'] === 'school') {
                $updateSuccess = $this->schoolModel->where('email', $resetData['email'])
                                                  ->set(['password' => $hashedPassword])
                                                  ->update();
            } else {
                $updateSuccess = $this->userModel->where('email', $resetData['email'])
                                                ->set(['password' => $hashedPassword])
                                                ->update();
            }

            if ($updateSuccess) {
                // Mark token as used
                $this->passwordResetModel->markTokenAsUsed($token);

                // Log the password reset
                $user = $resetData['user_type'] === 'school' 
                    ? $this->schoolModel->where('email', $resetData['email'])->first()
                    : $this->userModel->where('email', $resetData['email'])->first();

                if ($user) {
                    // Log the password reset completion (non-critical, don't fail if logging fails)
                    try {
                        $this->auditLogger->logActivity(
                            $user['id'],
                            $resetData['user_type'] === 'school' ? $user['id'] : $user['school_id'],
                            'password_reset_completed',
                            'Password successfully reset for email: ' . $resetData['email'],
                            null,
                            null,
                            'high'
                        );
                    } catch (\Exception $e) {
                        // Log the error but don't fail the password reset process
                        log_message('error', 'Failed to log password reset completion activity: ' . $e->getMessage());
                    }
                }

                $response = [
                    'success' => true,
                    'message' => 'Password reset successful! You can now login with your new password.',
                    'redirect' => $resetData['user_type'] === 'school' ? '/login/schooladmin' : '/staff/login'
                ];

                return $isAjax ? $this->response->setJSON($response) : redirect()->to($response['redirect'])->with('success', $response['message']);
            } else {
                throw new \Exception('Failed to update password in database');
            }

        } catch (\Exception $e) {
            log_message('error', 'Password reset error: ' . $e->getMessage());
            
            $response = [
                'success' => false,
                'message' => 'An error occurred while resetting your password. Please try again.'
            ];
            return $isAjax ? $this->response->setJSON($response) : redirect()->back()->with('error', $response['message']);
        }
    }

    /**
     * Validate password strength
     */
    private function validatePasswordStrength($password)
    {
        $errors = [];

        // Check minimum length
        if (strlen($password) < 8) {
            $errors[] = 'at least 8 characters';
        }

        // Check for uppercase letter
        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'one uppercase letter';
        }

        // Check for lowercase letter
        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'one lowercase letter';
        }

        // Check for number
        if (!preg_match('/\d/', $password)) {
            $errors[] = 'one number';
        }

        // Check for special character
        if (!preg_match('/[@$!%*?&]/', $password)) {
            $errors[] = 'one special character (@$!%*?&)';
        }

        return $errors;
    }
}
