<?php
// Simple database connection
$host = 'localhost';
$dbname = 'school';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // First, let's check the current user data
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        echo "Current user data:\n";
        echo "ID: " . $user['id'] . "\n";
        echo "Name: " . $user['name'] . "\n";
        echo "Email: " . $user['email'] . "\n";
        echo "Status: " . $user['status'] . "\n";
        echo "Is Deleted: " . $user['is_deleted'] . "\n";
        echo "School ID: " . $user['school_id'] . "\n";
        echo "Current password hash: " . $user['password'] . "\n\n";

        // Test current password with common passwords
        $testPasswords = ['123456', 'password', 'admin', 'schooladmin', 'arunkumar', 'test123', 'admin123'];

        foreach ($testPasswords as $testPassword) {
            if (password_verify($testPassword, $user['password'])) {
                echo "✅ Current password is: " . $testPassword . "\n";
                exit;
            }
        }

        echo "None of the test passwords worked. Setting new password...\n\n";

        // Update <NAME_EMAIL> to '123456'
        $newPassword = password_hash('123456', PASSWORD_DEFAULT);

        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
        $result = $stmt->execute([$newPassword, '<EMAIL>']);

        if ($result) {
            echo "✅ Password updated <NAME_EMAIL>\n";
            echo "New password: 123456\n";
            echo "Password hash: " . $newPassword . "\n";

            // Verify the update by fetching fresh data
            $stmt = $pdo->prepare("SELECT email, password FROM users WHERE email = ?");
            $stmt->execute(['<EMAIL>']);
            $updatedUser = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($updatedUser && password_verify('123456', $updatedUser['password'])) {
                echo "✅ Password verification successful!\n";
            } else {
                echo "❌ Password verification failed!\n";
                echo "Updated hash: " . $updatedUser['password'] . "\n";
            }
        } else {
            echo "❌ Failed to update password\n";
        }
    } else {
        echo "❌ User not found!\n";
    }

} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
