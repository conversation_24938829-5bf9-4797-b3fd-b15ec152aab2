<?php
/**
 * Simple Razorpay Test
 * Run this from the school directory: php simple_razorpay_test.php
 */

require_once 'vendor/autoload.php';

use Razorpay\Api\Api;

echo "=== Simple Razorpay Integration Test ===\n\n";

// Test credentials from your .env file
$keyId = 'rzp_test_T96GgyysiEduFd';
$keySecret = 'dLDscJXPaqXPtfqLNjsxdeIX';

try {
    echo "1. Testing Razorpay API Connection:\n";
    echo "   - Key ID: " . substr($keyId, 0, 12) . "...\n";
    echo "   - Key Secret: " . (strlen($keySecret) > 0 ? 'SET' : 'NOT SET') . "\n\n";

    // Initialize Razorpay API
    $api = new Api($keyId, $keySecret);
    echo "   - API initialized successfully ✓\n\n";

    echo "2. Testing Order Creation:\n";
    
    // Create test order
    $orderData = [
        'receipt' => 'test_order_' . time(),
        'amount' => 99900, // ₹999 in paise
        'currency' => 'INR',
        'notes' => [
            'test' => true,
            'school_id' => 1
        ]
    ];

    $order = $api->order->create($orderData);
    
    echo "   - Order created successfully ✓\n";
    echo "   - Order ID: " . $order['id'] . "\n";
    echo "   - Amount: ₹" . ($order['amount'] / 100) . "\n";
    echo "   - Currency: " . $order['currency'] . "\n";
    echo "   - Status: " . $order['status'] . "\n\n";

    echo "3. Testing Payment Options Generation:\n";
    
    $paymentOptions = [
        'key' => $keyId,
        'order_id' => $order['id'],
        'amount' => $order['amount'],
        'currency' => $order['currency'],
        'name' => 'QuestionBank Pro',
        'description' => 'Subscription Payment',
        'prefill' => [
            'name' => 'Test School',
            'email' => '<EMAIL>'
        ],
        'theme' => [
            'color' => '#4F46E5'
        ]
    ];
    
    echo "   - Payment options generated ✓\n";
    echo "   - Ready for frontend integration ✓\n\n";

    echo "✅ ALL TESTS PASSED!\n";
    echo "Razorpay integration is working correctly.\n";
    echo "You can now process payments through Razorpay.\n\n";
    
    echo "🔧 Next Steps:\n";
    echo "1. Go to your payment page: http://localhost:8080/index.php/payment/process\n";
    echo "2. Select Razorpay as payment method\n";
    echo "3. Click 'Pay Now' to test the integration\n";
    echo "4. Use Razorpay test cards for testing\n\n";
    
    echo "💳 Test Card Details:\n";
    echo "   - Card Number: 4111 1111 1111 1111\n";
    echo "   - Expiry: Any future date\n";
    echo "   - CVV: Any 3 digits\n";
    echo "   - Name: Any name\n\n";

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Razorpay integration failed.\n\n";
    
    echo "🔧 Possible Issues:\n";
    echo "1. Check your API credentials in .env file\n";
    echo "2. Ensure internet connection is working\n";
    echo "3. Verify Razorpay account is active\n\n";
}

echo "=== Test Complete ===\n";
