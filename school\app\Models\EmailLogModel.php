<?php

namespace App\Models;

use CodeIgniter\Model;

class EmailLogModel extends Model
{
    protected $table = 'email_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'user_id', 'email_to', 'subject', 'content', 
        'status', 'sent_at', 'created_at', 'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'permit_empty|integer',
        'user_id' => 'permit_empty|integer',
        'email_to' => 'required|valid_email',
        'subject' => 'required|max_length[255]',
        'content' => 'required',
        'status' => 'required|in_list[sent,failed]'
    ];

    protected $validationMessages = [
        'email_to' => [
            'required' => 'Email address is required',
            'valid_email' => 'Please provide a valid email address'
        ],
        'subject' => [
            'required' => 'Email subject is required'
        ],
        'content' => [
            'required' => 'Email content is required'
        ],
        'status' => [
            'required' => 'Email status is required',
            'in_list' => 'Status must be either sent or failed'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get email logs by school ID
     */
    public function getBySchool($schoolId, $limit = 100)
    {
        return $this->where('school_id', $schoolId)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get email logs by user ID
     */
    public function getByUser($userId, $limit = 50)
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Get failed emails for retry
     */
    public function getFailedEmails($schoolId = null, $limit = 50)
    {
        $builder = $this->where('status', 'failed');
        
        if ($schoolId) {
            $builder->where('school_id', $schoolId);
        }
        
        return $builder->orderBy('created_at', 'DESC')
                      ->limit($limit)
                      ->findAll();
    }

    /**
     * Get email statistics
     */
    public function getEmailStats($schoolId = null, $days = 30)
    {
        $builder = $this->select('status, COUNT(*) as count')
                       ->where('created_at >=', date('Y-m-d H:i:s', strtotime("-{$days} days")))
                       ->groupBy('status');
        
        if ($schoolId) {
            $builder->where('school_id', $schoolId);
        }
        
        $results = $builder->findAll();
        
        $stats = ['sent' => 0, 'failed' => 0, 'total' => 0];
        
        foreach ($results as $result) {
            $stats[$result['status']] = (int)$result['count'];
            $stats['total'] += (int)$result['count'];
        }
        
        return $stats;
    }

    /**
     * Clean old email logs (older than specified days)
     */
    public function cleanOldLogs($days = 90)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        return $this->where('created_at <', $cutoffDate)->delete();
    }
}
