<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class QuestionsSeeder extends Seeder
{
    public function run()
    {
        // Get first active school
        $schoolModel = new \App\Models\SchoolModel();
        $school = $schoolModel->where('status', 'active')->first();

        if (!$school) {
            echo "No active school found! Please create a school first.\n";
            return;
        }

        // Get staff users for this school
        $userModel = new \App\Models\UserModel();
        $staffUsers = $userModel->where('school_id', $school['id'])->findAll();

        if (empty($staffUsers)) {
            echo "No staff users found! Please create staff users first.\n";
            return;
        }

        echo "Creating sample questions for school: " . $school['name'] . " (ID: " . $school['id'] . ")\n";

        // Sample questions data
        $questions = [
            [
                'school_id' => $school['id'],
                'staff_id' => $staffUsers[0]['id'],
                'standard' => 10,
                'subject' => 'Mathematics',
                'chapter' => 'Algebra',
                'chapter_name' => 'Linear Equations',
                'topic_name' => 'Solving Linear Equations',
                'question_type' => 'multiple_choice',
                'difficulty' => 'medium',
                'marks' => 2,
                'question_text' => 'What is the value of x in the equation 2x + 5 = 15?',
                'option_a' => '5',
                'option_b' => '10',
                'option_c' => '15',
                'option_d' => '20',
                'correct_answer' => 'a',
                'status' => 'approved',
                'admin_feedback' => 'Good question with clear options',
                'reviewed_by' => 1,
                'reviewed_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-2 days'))
            ],
            [
                'school_id' => $school['id'],
                'staff_id' => $staffUsers[0]['id'],
                'standard' => 10,
                'subject' => 'Science',
                'chapter' => 'Physics',
                'chapter_name' => 'Light and Reflection',
                'topic_name' => 'Laws of Reflection',
                'question_type' => 'short_answer',
                'difficulty' => 'easy',
                'marks' => 3,
                'question_text' => 'State the two laws of reflection of light.',
                'answer' => '1. The incident ray, reflected ray, and normal all lie in the same plane. 2. The angle of incidence equals the angle of reflection.',
                'status' => 'rejected',
                'admin_feedback' => 'Question is too basic for grade 10. Please make it more challenging.',
                'reviewed_by' => 1,
                'reviewed_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-2 days')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-1 day'))
            ],
            [
                'school_id' => $school['id'],
                'staff_id' => count($staffUsers) > 1 ? $staffUsers[1]['id'] : $staffUsers[0]['id'],
                'standard' => 9,
                'subject' => 'English',
                'chapter' => 'Grammar',
                'chapter_name' => 'Tenses',
                'topic_name' => 'Present Perfect Tense',
                'question_type' => 'multiple_choice',
                'difficulty' => 'medium',
                'marks' => 1,
                'question_text' => 'Choose the correct sentence using present perfect tense:',
                'option_a' => 'I have completed my homework.',
                'option_b' => 'I completed my homework.',
                'option_c' => 'I am completing my homework.',
                'option_d' => 'I will complete my homework.',
                'correct_answer' => 'a',
                'status' => 'approved',
                'admin_feedback' => 'Well-structured grammar question',
                'reviewed_by' => 1,
                'reviewed_at' => date('Y-m-d H:i:s', strtotime('-3 hours')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-3 hours'))
            ],
            [
                'school_id' => $school['id'],
                'staff_id' => count($staffUsers) > 2 ? $staffUsers[2]['id'] : $staffUsers[0]['id'],
                'standard' => 11,
                'subject' => 'Chemistry',
                'chapter' => 'Organic Chemistry',
                'chapter_name' => 'Hydrocarbons',
                'topic_name' => 'Alkanes',
                'question_type' => 'long_answer',
                'difficulty' => 'hard',
                'marks' => 5,
                'question_text' => 'Explain the preparation methods of alkanes with suitable examples and chemical equations.',
                'answer' => 'Alkanes can be prepared by: 1. Wurtz reaction, 2. Kolbe electrolysis, 3. Reduction of alkyl halides, 4. Decarboxylation of carboxylic acids.',
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s', strtotime('-6 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-6 hours'))
            ],
            [
                'school_id' => $school['id'],
                'staff_id' => $staffUsers[0]['id'],
                'standard' => 12,
                'subject' => 'Mathematics',
                'chapter' => 'Calculus',
                'chapter_name' => 'Differentiation',
                'topic_name' => 'Chain Rule',
                'question_type' => 'multiple_choice',
                'difficulty' => 'hard',
                'marks' => 3,
                'question_text' => 'Find the derivative of f(x) = sin(x²)',
                'option_a' => '2x cos(x²)',
                'option_b' => 'cos(x²)',
                'option_c' => '2x sin(x²)',
                'option_d' => 'x cos(x²)',
                'correct_answer' => 'a',
                'status' => 'rejected',
                'admin_feedback' => 'Please provide step-by-step solution in the answer explanation',
                'reviewed_by' => 1,
                'reviewed_at' => date('Y-m-d H:i:s', strtotime('-4 hours')),
                'created_at' => date('Y-m-d H:i:s', strtotime('-8 hours')),
                'updated_at' => date('Y-m-d H:i:s', strtotime('-4 hours'))
            ]
        ];

        // Insert questions
        $questionModel = new \App\Models\QuestionModel();
        foreach ($questions as $question) {
            $questionModel->insert($question);
        }

        echo "Created " . count($questions) . " sample questions successfully!\n";
        echo "Questions include:\n";
        echo "- 2 Approved questions\n";
        echo "- 2 Rejected questions\n";
        echo "- 1 Pending question\n";
    }
}
