<?php

namespace App\Models;

use CodeIgniter\Model;

class LoginAttemptModel extends Model
{
    protected $table = 'login_attempts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'email', 'ip_address', 'user_agent', 'success', 'failure_reason', 'user_type'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = null; // No updated_at field

    // Validation
    protected $validationRules = [
        'email' => 'required|valid_email',
        'ip_address' => 'required|max_length[45]',
        'user_type' => 'required|in_list[superadmin,school,user]'
    ];

    /**
     * Log a login attempt
     */
    public function logAttempt($email, $success = false, $failureReason = null, $userType = 'user')
    {
        $request = \Config\Services::request();
        
        $data = [
            'email' => $email,
            'ip_address' => $request->getIPAddress(),
            'user_agent' => $request->getUserAgent()->getAgentString(),
            'success' => $success,
            'failure_reason' => $failureReason,
            'user_type' => $userType
        ];

        return $this->insert($data);
    }

    /**
     * Get failed login attempts count for email in specified time period
     */
    public function getFailedAttemptsCount($email, $minutes = 15)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        return $this->where('email', $email)
                   ->where('success', false)
                   ->where('created_at >', $timeLimit)
                   ->countAllResults();
    }

    /**
     * Get failed login attempts count for IP in specified time period
     */
    public function getFailedAttemptsCountByIP($ipAddress, $minutes = 15)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        return $this->where('ip_address', $ipAddress)
                   ->where('success', false)
                   ->where('created_at >', $timeLimit)
                   ->countAllResults();
    }

    /**
     * Check if email is temporarily locked due to failed attempts
     */
    public function isEmailLocked($email, $maxAttempts = 5, $lockoutMinutes = 15)
    {
        $failedAttempts = $this->getFailedAttemptsCount($email, $lockoutMinutes);
        return $failedAttempts >= $maxAttempts;
    }

    /**
     * Check if IP is temporarily locked due to failed attempts
     */
    public function isIPLocked($ipAddress, $maxAttempts = 10, $lockoutMinutes = 15)
    {
        $failedAttempts = $this->getFailedAttemptsCountByIP($ipAddress, $lockoutMinutes);
        return $failedAttempts >= $maxAttempts;
    }

    /**
     * Get time until email lockout expires
     */
    public function getEmailLockoutExpiry($email, $lockoutMinutes = 15)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$lockoutMinutes} minutes"));
        
        $lastFailedAttempt = $this->where('email', $email)
                                 ->where('success', false)
                                 ->where('created_at >', $timeLimit)
                                 ->orderBy('created_at', 'DESC')
                                 ->first();

        if ($lastFailedAttempt) {
            $lockoutExpiry = strtotime($lastFailedAttempt['created_at']) + ($lockoutMinutes * 60);
            return max(0, $lockoutExpiry - time());
        }

        return 0;
    }

    /**
     * Clean up old login attempts (for cron job)
     */
    public function cleanupOldAttempts($daysToKeep = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Get login statistics for a user
     */
    public function getUserLoginStats($email, $days = 30)
    {
        $startDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        $stats = [
            'total_attempts' => $this->where('email', $email)
                                   ->where('created_at >', $startDate)
                                   ->countAllResults(),
            'successful_logins' => $this->where('email', $email)
                                       ->where('success', true)
                                       ->where('created_at >', $startDate)
                                       ->countAllResults(),
            'failed_attempts' => $this->where('email', $email)
                                     ->where('success', false)
                                     ->where('created_at >', $startDate)
                                     ->countAllResults(),
            'last_successful_login' => $this->where('email', $email)
                                           ->where('success', true)
                                           ->orderBy('created_at', 'DESC')
                                           ->first(),
            'last_failed_attempt' => $this->where('email', $email)
                                         ->where('success', false)
                                         ->orderBy('created_at', 'DESC')
                                         ->first()
        ];

        return $stats;
    }

    /**
     * Get recent login attempts for admin dashboard
     */
    public function getRecentAttempts($limit = 50, $userType = null)
    {
        $builder = $this->orderBy('created_at', 'DESC');
        
        if ($userType) {
            $builder->where('user_type', $userType);
        }
        
        return $builder->limit($limit)->findAll();
    }

    /**
     * Get suspicious activity (multiple failed attempts from same IP)
     */
    public function getSuspiciousActivity($hours = 24, $minFailedAttempts = 5)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));
        
        return $this->select('ip_address, COUNT(*) as failed_attempts, MAX(created_at) as last_attempt')
                   ->where('success', false)
                   ->where('created_at >', $timeLimit)
                   ->groupBy('ip_address')
                   ->having('failed_attempts >=', $minFailedAttempts)
                   ->orderBy('failed_attempts', 'DESC')
                   ->findAll();
    }
}
