<?php

namespace App\Models;

use CodeIgniter\Model;

class AuditLogModel extends Model
{
    protected $table = 'audit_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'user_id', 'action', 'entity_type', 'entity_id', 'entity_name',
        'description', 'old_values', 'new_values', 'ip_address', 'user_agent',
        'severity', 'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'action' => 'required|max_length[50]',
        'entity_type' => 'required|max_length[50]',
        'severity' => 'in_list[low,medium,high,critical]',
        'status' => 'in_list[success,failed,warning]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Log an audit entry
     */
    public function logAction($data)
    {
        // Ensure required fields
        if (!isset($data['action']) || !isset($data['entity_type'])) {
            return false;
        }

        // Set defaults
        $logData = array_merge([
            'school_id' => null,
            'user_id' => null,
            'entity_id' => null,
            'entity_name' => null,
            'description' => null,
            'old_values' => null,
            'new_values' => null,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $this->getUserAgent(),
            'severity' => 'low',
            'status' => 'success'
        ], $data);

        // Convert arrays to JSON for old_values and new_values
        if (is_array($logData['old_values'])) {
            $logData['old_values'] = json_encode($logData['old_values']);
        }
        if (is_array($logData['new_values'])) {
            $logData['new_values'] = json_encode($logData['new_values']);
        }

        return $this->insert($logData);
    }

    /**
     * Get audit logs with filters and pagination
     */
    public function getAuditLogs($filters = [], $page = 1, $perPage = 50)
    {
        $builder = $this->select('
            audit_logs.*,
            users.name as user_name,
            users.email as user_email,
            schools.name as school_name
        ')
        ->join('users', 'users.id = audit_logs.user_id', 'left')
        ->join('schools', 'schools.id = audit_logs.school_id', 'left');

        // Apply filters
        if (!empty($filters['school_id'])) {
            $builder->where('audit_logs.school_id', $filters['school_id']);
        }

        if (!empty($filters['user_id'])) {
            $builder->where('audit_logs.user_id', $filters['user_id']);
        }

        if (!empty($filters['action'])) {
            $builder->where('audit_logs.action', $filters['action']);
        }

        if (!empty($filters['entity_type'])) {
            $builder->where('audit_logs.entity_type', $filters['entity_type']);
        }

        if (!empty($filters['severity'])) {
            $builder->where('audit_logs.severity', $filters['severity']);
        }

        if (!empty($filters['status'])) {
            $builder->where('audit_logs.status', $filters['status']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('audit_logs.created_at >=', $filters['date_from'] . ' 00:00:00');
        }

        if (!empty($filters['date_to'])) {
            $builder->where('audit_logs.created_at <=', $filters['date_to'] . ' 23:59:59');
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('audit_logs.description', $filters['search'])
                   ->orLike('audit_logs.entity_name', $filters['search'])
                   ->orLike('users.name', $filters['search'])
                   ->orLike('schools.name', $filters['search'])
                   ->groupEnd();
        }

        // Get total count
        $total = $builder->countAllResults(false);

        // Get paginated results
        $offset = ($page - 1) * $perPage;
        $logs = $builder->orderBy('audit_logs.created_at', 'DESC')
                       ->limit($perPage, $offset)
                       ->findAll();

        return [
            'logs' => $logs,
            'total' => $total,
            'page' => $page,
            'perPage' => $perPage,
            'totalPages' => ceil($total / $perPage)
        ];
    }

    /**
     * Get audit logs for a specific entity
     */
    public function getEntityAuditLogs($entityType, $entityId, $limit = 20)
    {
        return $this->select('
            audit_logs.*,
            users.name as user_name,
            users.email as user_email
        ')
        ->join('users', 'users.id = audit_logs.user_id', 'left')
        ->where('audit_logs.entity_type', $entityType)
        ->where('audit_logs.entity_id', $entityId)
        ->orderBy('audit_logs.created_at', 'DESC')
        ->limit($limit)
        ->findAll();
    }

    /**
     * Get recent audit logs for dashboard
     */
    public function getRecentAuditLogs($schoolId = null, $limit = 10)
    {
        $builder = $this->select('
            audit_logs.*,
            users.name as user_name,
            schools.name as school_name
        ')
        ->join('users', 'users.id = audit_logs.user_id', 'left')
        ->join('schools', 'schools.id = audit_logs.school_id', 'left');

        if ($schoolId) {
            $builder->where('audit_logs.school_id', $schoolId);
        }

        return $builder->orderBy('audit_logs.created_at', 'DESC')
                      ->limit($limit)
                      ->findAll();
    }

    /**
     * Get audit statistics
     */
    public function getAuditStats($schoolId = null, $dateFrom = null, $dateTo = null)
    {
        $builder = $this->db->table($this->table);

        if ($schoolId) {
            $builder->where('school_id', $schoolId);
        }

        if ($dateFrom) {
            $builder->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $builder->where('created_at <=', $dateTo . ' 23:59:59');
        }

        $stats = [
            'total_actions' => $builder->countAllResults(false),
            'actions_by_type' => $builder->select('action, COUNT(*) as count')
                                       ->groupBy('action')
                                       ->orderBy('count', 'DESC')
                                       ->get()->getResultArray(),
            'actions_by_severity' => $builder->select('severity, COUNT(*) as count')
                                           ->groupBy('severity')
                                           ->get()->getResultArray(),
            'actions_by_status' => $builder->select('status, COUNT(*) as count')
                                         ->groupBy('status')
                                         ->get()->getResultArray(),
            'top_users' => $builder->select('users.name, COUNT(*) as count')
                                 ->join('users', 'users.id = audit_logs.user_id', 'left')
                                 ->where('audit_logs.user_id IS NOT NULL')
                                 ->groupBy('audit_logs.user_id')
                                 ->orderBy('count', 'DESC')
                                 ->limit(10)
                                 ->get()->getResultArray()
        ];

        return $stats;
    }

    /**
     * Clean old audit logs (for maintenance)
     */
    public function cleanOldLogs($daysToKeep = 90)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Get client IP address
     */
    private function getClientIP()
    {
        try {
            if (is_cli()) {
                return '127.0.0.1';
            }
            $request = \Config\Services::request();
            return $request->getIPAddress();
        } catch (\Exception $e) {
            return '127.0.0.1';
        }
    }

    /**
     * Get user agent
     */
    private function getUserAgent()
    {
        try {
            $request = \Config\Services::request();
            if (is_cli()) {
                return 'CLI';
            }
            return $request->getUserAgent()->getAgentString();
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get actions list for filters
     */
    public function getDistinctActions()
    {
        return $this->select('action')
                   ->distinct()
                   ->orderBy('action', 'ASC')
                   ->findColumn('action');
    }

    /**
     * Get entity types list for filters
     */
    public function getDistinctEntityTypes()
    {
        return $this->select('entity_type')
                   ->distinct()
                   ->orderBy('entity_type', 'ASC')
                   ->findColumn('entity_type');
    }
}
