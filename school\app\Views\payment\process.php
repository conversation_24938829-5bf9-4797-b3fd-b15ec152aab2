<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?> - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-12">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2"><?= $title ?></h1>
                <p class="text-gray-600">Secure payment processing</p>
            </div>

            <!-- Payment Card -->
            <div class="bg-white rounded-xl shadow-lg p-8">
                
                <!-- Plan Summary -->
                <div class="border-b border-gray-200 pb-6 mb-6">
                    <h2 class="text-xl font-semibold mb-4">Order Summary</h2>
                    
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600">Plan:</span>
                        <span class="font-medium"><?= $plan['display_name'] ?></span>
                    </div>
                    
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-gray-600">Billing Cycle:</span>
                        <span class="font-medium capitalize"><?= $billing_cycle ?></span>
                    </div>
                    
                    <div class="flex justify-between items-center text-lg font-semibold">
                        <span>Total Amount:</span>
                        <span class="text-indigo-600">₹<?= number_format($amount) ?></span>
                    </div>
                </div>

                <!-- Payment Methods -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-4">Choose Payment Method</h3>

                    <div class="space-y-3">
                        <!-- Test Mode (Development Only) -->
                        <?php if (ENVIRONMENT === 'development' && env('PAYMENT_MODE', 'development') === 'development'): ?>
                        <label class="flex items-center p-4 border border-green-200 rounded-lg cursor-pointer hover:bg-green-50 bg-green-25">
                            <input type="radio" name="payment_gateway" value="test" class="mr-3" checked>
                            <div class="flex items-center">
                                <div class="w-16 h-6 bg-green-600 text-white text-xs flex items-center justify-center rounded mr-3">TEST</div>
                                <div>
                                    <div class="font-medium text-green-700">Test Payment</div>
                                    <div class="text-sm text-green-600">Development mode - No actual payment</div>
                                </div>
                            </div>
                        </label>
                        <?php endif; ?>

                        <!-- Razorpay -->
                        <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="payment_gateway" value="razorpay" class="mr-3" <?= (ENVIRONMENT !== 'development' || env('PAYMENT_MODE', 'development') === 'production') ? 'checked' : '' ?>>
                            <div class="flex items-center">
                                <img src="https://razorpay.com/assets/razorpay-logo.svg" alt="Razorpay" class="h-6 mr-3">
                                <div>
                                    <div class="font-medium">Razorpay</div>
                                    <div class="text-sm text-gray-500">Cards, UPI, Net Banking, Wallets</div>
                                    <?php if (env('PAYMENT_MODE', 'development') === 'production'): ?>
                                    <div class="text-xs text-green-600 font-medium">✓ Live Payments</div>
                                    <?php else: ?>
                                    <div class="text-xs text-orange-600 font-medium">⚠ Test Mode</div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </label>

                        <!-- Stripe -->
                        <label class="flex items-center p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input type="radio" name="payment_gateway" value="stripe" class="mr-3">
                            <div class="flex items-center">
                                <i class="fab fa-stripe text-2xl text-blue-600 mr-3"></i>
                                <div>
                                    <div class="font-medium">Stripe</div>
                                    <div class="text-sm text-gray-500">International Cards</div>
                                </div>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Payment Button -->
                <button 
                    id="payNowBtn" 
                    class="w-full bg-indigo-600 text-white py-4 rounded-lg text-lg font-semibold hover:bg-indigo-700 transition duration-300"
                >
                    <i class="fas fa-lock mr-2"></i>Pay ₹<?= number_format($amount) ?> Securely
                </button>

                <!-- Security Info -->
                <div class="mt-6 text-center text-sm text-gray-500">
                    <i class="fas fa-shield-alt mr-1"></i>
                    Your payment information is encrypted and secure
                </div>

                <!-- Back Link -->
                <div class="mt-6 text-center">
                    <a href="/subscription/plans" class="text-indigo-600 hover:text-indigo-800">
                        <i class="fas fa-arrow-left mr-1"></i>Back to Plans
                    </a>
                </div>

            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Processing payment...</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const payNowBtn = document.getElementById('payNowBtn');
            
            payNowBtn.addEventListener('click', function() {
                const selectedGateway = document.querySelector('input[name="payment_gateway"]:checked').value;
                
                // Show loading
                document.getElementById('loadingModal').classList.remove('hidden');
                document.getElementById('loadingModal').classList.add('flex');
                
                // Initiate payment
                fetch('<?= base_url() ?>/payment/initiate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: `plan_id=<?= $plan['id'] ?>&billing_cycle=<?= $billing_cycle ?>&payment_gateway=${selectedGateway}`
                })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loadingModal').classList.add('hidden');
                    document.getElementById('loadingModal').classList.remove('flex');
                    
                    if (data.success) {
                        if (data.gateway === 'test') {
                            initiateTestPayment(data.payment_data, data.transaction_id);
                        } else if (data.gateway === 'razorpay') {
                            initiateRazorpayPayment(data.payment_data, data.transaction_id);
                        } else if (data.gateway === 'stripe') {
                            initiateStripePayment(data.payment_data, data.transaction_id);
                        }
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    document.getElementById('loadingModal').classList.add('hidden');
                    document.getElementById('loadingModal').classList.remove('flex');
                    alert('An error occurred. Please try again.');
                    console.error('Error:', error);
                });
            });
        });

        function initiateTestPayment(paymentData, transactionId) {
            // Simulate payment processing
            setTimeout(function() {
                if (confirm(`Test Payment Simulation\n\nPlan: ${paymentData.plan_name}\nAmount: ₹${paymentData.amount}\n\nSimulate successful payment?`)) {
                    // Simulate successful payment
                    window.location.href = `<?= base_url() ?>/payment/success?transaction_id=${transactionId}&gateway_transaction_id=test_${Date.now()}&gateway=test`;
                } else {
                    // Simulate cancelled payment
                    window.location.href = `<?= base_url() ?>/payment/failure?transaction_id=${transactionId}&reason=Test payment cancelled`;
                }
            }, 1000);
        }

        function initiateRazorpayPayment(paymentData, transactionId) {
            // Check if Razorpay is loaded
            if (typeof Razorpay === 'undefined') {
                alert('Payment gateway not loaded. Please refresh the page and try again.');
                return;
            }

            console.log('Initiating Razorpay payment with data:', paymentData);

            const options = {
                key: paymentData.key,
                amount: paymentData.amount,
                currency: paymentData.currency,
                name: paymentData.name,
                description: paymentData.description,
                order_id: paymentData.order_id,
                prefill: paymentData.prefill,
                theme: paymentData.theme || {
                    color: '#4F46E5'
                },
                handler: function(response) {
                    console.log('Payment successful:', response);
                    // Payment successful
                    window.location.href = `<?= base_url() ?>/payment/success?transaction_id=${transactionId}&gateway_transaction_id=${response.razorpay_payment_id}&gateway=razorpay`;
                },
                modal: {
                    ondismiss: function() {
                        console.log('Payment cancelled by user');
                        // Payment cancelled
                        window.location.href = `<?= base_url() ?>/payment/failure?transaction_id=${transactionId}&reason=Payment cancelled by user`;
                    }
                }
            };

            try {
                const rzp = new Razorpay(options);
                rzp.on('payment.failed', function (response) {
                    console.error('Payment failed:', response.error);
                    alert('Payment failed: ' + response.error.description);
                    window.location.href = `<?= base_url() ?>/payment/failure?transaction_id=${transactionId}&reason=${response.error.description}`;
                });
                rzp.open();
            } catch (error) {
                console.error('Error initializing Razorpay:', error);
                alert('Error initializing payment gateway. Please try again.');
            }
        }

        function initiateStripePayment(paymentData, transactionId) {
            // Placeholder for Stripe integration
            alert('Stripe integration coming soon!');
        }
    </script>
</body>
</html>
