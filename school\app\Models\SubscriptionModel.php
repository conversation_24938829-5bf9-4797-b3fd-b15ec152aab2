<?php

namespace App\Models;

use CodeIgniter\Model;

class SubscriptionModel extends Model
{
    protected $table = 'subscriptions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'plan_id', 'status', 'billing_cycle', 'amount', 'currency',
        'started_at', 'expires_at', 'trial_ends_at', 'cancelled_at',
        'payment_method', 'payment_gateway', 'gateway_subscription_id'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'plan_id' => 'required|integer',
        'status' => 'required|in_list[active,expired,cancelled,suspended,trial]',
        'billing_cycle' => 'required|in_list[monthly,yearly,lifetime]',
        'amount' => 'required|decimal',
        'currency' => 'required|max_length[3]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get active subscription for a school
     */
    public function getActiveSubscription($schoolId)
    {
        return $this->select('subscriptions.*, plans.name as plan_name, plans.display_name as plan_display_name')
                   ->join('plans', 'plans.id = subscriptions.plan_id')
                   ->where('subscriptions.school_id', $schoolId)
                   ->where('subscriptions.status', 'active')
                   ->first();
    }

    /**
     * Get current subscription (active or trial)
     */
    public function getCurrentSubscription($schoolId)
    {
        return $this->select('subscriptions.*, plans.name as plan_name, plans.display_name as plan_display_name')
                   ->join('plans', 'plans.id = subscriptions.plan_id')
                   ->where('subscriptions.school_id', $schoolId)
                   ->whereIn('subscriptions.status', ['active', 'trial'])
                   ->orderBy('subscriptions.created_at', 'DESC')
                   ->first();
    }

    /**
     * Create subscription for school
     */
    public function createSubscription($schoolId, $planId, $billingCycle = 'monthly', $amount = 0, $isTrialPeriod = false)
    {
        $planModel = new PlanModel();
        $plan = $planModel->find($planId);

        if (!$plan) {
            return false;
        }

        // Calculate dates
        $now = date('Y-m-d H:i:s');
        $trialDays = 14; // 14-day trial
        $trialEndsAt = date('Y-m-d H:i:s', strtotime("+{$trialDays} days"));

        if ($isTrialPeriod) {
            $status = 'trial';
            $expiresAt = $trialEndsAt;
            $startedAt = $now;
        } else {
            $status = 'active';
            $startedAt = $now;
            
            // Calculate expiry based on billing cycle
            switch ($billingCycle) {
                case 'yearly':
                    $expiresAt = date('Y-m-d H:i:s', strtotime('+1 year'));
                    break;
                case 'lifetime':
                    $expiresAt = null;
                    break;
                case 'monthly':
                default:
                    $expiresAt = date('Y-m-d H:i:s', strtotime('+1 month'));
                    break;
            }
        }

        $subscriptionData = [
            'school_id' => $schoolId,
            'plan_id' => $planId,
            'status' => $status,
            'billing_cycle' => $billingCycle,
            'amount' => $amount,
            'currency' => $plan['currency'],
            'started_at' => $startedAt,
            'expires_at' => $expiresAt,
            'trial_ends_at' => $isTrialPeriod ? $trialEndsAt : null
        ];

        return $this->insert($subscriptionData);
    }

    /**
     * Upgrade/Downgrade subscription
     */
    public function changeSubscription($schoolId, $newPlanId, $billingCycle = 'monthly')
    {
        // Cancel current subscription
        $this->cancelCurrentSubscription($schoolId);

        // Get new plan details
        $planModel = new PlanModel();
        $plan = $planModel->find($newPlanId);

        if (!$plan) {
            return false;
        }

        // Calculate amount based on billing cycle
        $amount = ($billingCycle === 'yearly') ? $plan['price_yearly'] : $plan['price_monthly'];

        // Create new subscription
        return $this->createSubscription($schoolId, $newPlanId, $billingCycle, $amount, false);
    }

    /**
     * Cancel current subscription
     */
    public function cancelCurrentSubscription($schoolId)
    {
        $current = $this->getCurrentSubscription($schoolId);
        
        if ($current) {
            return $this->update($current['id'], [
                'status' => 'cancelled',
                'cancelled_at' => date('Y-m-d H:i:s')
            ]);
        }

        return true;
    }

    /**
     * Renew subscription
     */
    public function renewSubscription($subscriptionId, $amount, $paymentMethod = null)
    {
        $subscription = $this->find($subscriptionId);
        
        if (!$subscription) {
            return false;
        }

        // Calculate new expiry date
        switch ($subscription['billing_cycle']) {
            case 'yearly':
                $newExpiresAt = date('Y-m-d H:i:s', strtotime('+1 year'));
                break;
            case 'lifetime':
                $newExpiresAt = null;
                break;
            case 'monthly':
            default:
                $newExpiresAt = date('Y-m-d H:i:s', strtotime('+1 month'));
                break;
        }

        $updateData = [
            'status' => 'active',
            'expires_at' => $newExpiresAt,
            'amount' => $amount
        ];

        if ($paymentMethod) {
            $updateData['payment_method'] = $paymentMethod;
        }

        return $this->update($subscriptionId, $updateData);
    }

    /**
     * Check if subscription is expired
     */
    public function isSubscriptionExpired($schoolId)
    {
        $subscription = $this->getCurrentSubscription($schoolId);
        
        if (!$subscription) {
            return true; // No subscription = expired
        }

        if ($subscription['status'] === 'cancelled' || $subscription['status'] === 'suspended') {
            return true;
        }

        if ($subscription['expires_at'] && strtotime($subscription['expires_at']) < time()) {
            // Update status to expired
            $this->update($subscription['id'], ['status' => 'expired']);
            return true;
        }

        return false;
    }

    /**
     * Get subscription history for school
     */
    public function getSubscriptionHistory($schoolId)
    {
        return $this->select('subscriptions.*, plans.name as plan_name, plans.display_name as plan_display_name')
                   ->join('plans', 'plans.id = subscriptions.plan_id')
                   ->where('subscriptions.school_id', $schoolId)
                   ->orderBy('subscriptions.created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get expiring subscriptions (for notifications)
     */
    public function getExpiringSubscriptions($days = 7)
    {
        $expiryDate = date('Y-m-d H:i:s', strtotime("+{$days} days"));
        
        return $this->select('subscriptions.*, schools.name as school_name, schools.email as school_email, plans.display_name as plan_name')
                   ->join('schools', 'schools.id = subscriptions.school_id')
                   ->join('plans', 'plans.id = subscriptions.plan_id')
                   ->where('subscriptions.status', 'active')
                   ->where('subscriptions.expires_at <=', $expiryDate)
                   ->where('subscriptions.expires_at >', date('Y-m-d H:i:s'))
                   ->findAll();
    }

    /**
     * Get trial subscriptions ending soon
     */
    public function getTrialsEndingSoon($days = 3)
    {
        $expiryDate = date('Y-m-d H:i:s', strtotime("+{$days} days"));
        
        return $this->select('subscriptions.*, schools.name as school_name, schools.email as school_email, plans.display_name as plan_name')
                   ->join('schools', 'schools.id = subscriptions.school_id')
                   ->join('plans', 'plans.id = subscriptions.plan_id')
                   ->where('subscriptions.status', 'trial')
                   ->where('subscriptions.trial_ends_at <=', $expiryDate)
                   ->where('subscriptions.trial_ends_at >', date('Y-m-d H:i:s'))
                   ->findAll();
    }
}
