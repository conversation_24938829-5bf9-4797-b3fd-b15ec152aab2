<?php

namespace App\Controllers;

use App\Models\SchoolModel;

use App\Models\UserModel;
use App\Models\UserProfileModel;
use App\Models\AuditLogModel;
use App\Models\ActivityLogModel;
use App\Models\SuperAdminLogModel;
use App\Models\SuperAdminModel;
use App\Models\SystemSettingsModel;
use App\Services\AuditLogger;

use App\Models\SubscriptionModel;
use App\Models\PlanModel;
use App\Models\PaymentLogModel;


class SuperAdmin extends BaseController
{
    protected $schoolModel;
    protected $userModel;
    protected $userProfileModel;
    protected $auditLogModel;
    protected $emailService;
    protected $activityLogModel;
    protected $superAdminLogModel;
    protected $superAdminModel;
    protected $systemSettingsModel;
    protected $auditLogger;

    public function __construct()
    {
        $this->schoolModel = new SchoolModel();
        $this->userModel = new UserModel();
        $this->userProfileModel = new UserProfileModel();
        $this->auditLogModel = new AuditLogModel();
        $this->activityLogModel = new ActivityLogModel();
        $this->superAdminLogModel = new SuperAdminLogModel();
        $this->superAdminModel = new SuperAdminModel();
        $this->systemSettingsModel = new SystemSettingsModel();
        $this->auditLogger = new AuditLogger();
        $this->emailService = new \App\Services\EmailService();
    }

    public function dashboard()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
        }

        // Get user statistics
        $totalUsers = $this->userModel->countAllResults();
        $schoolAdmins = $this->userModel->join('schools', 'schools.id = users.school_id')
                                      ->where('users.is_deleted', 0)
                                      ->countAllResults();
        $staffMembers = $this->userModel->join('user_roles', 'user_roles.user_id = users.id')
                                       ->join('roles', 'roles.id = user_roles.role_id')
                                       ->where('roles.name !=', 'Admin')
                                       ->where('users.is_deleted', 0)
                                       ->countAllResults();

        // Get dynamic statistics
        $totalQuestions = $this->getTotalQuestions();
        $monthlyRevenue = $this->getMonthlyRevenue();
        $revenueGrowth = $this->getRevenueGrowth();

        // Get notification count
        $notificationCount = $this->getNotificationCount();

        $data = [
            'schools' => $this->schoolModel->findAll(),
            'pendingSchools' => $this->schoolModel->where('status', 'inactive')->findAll(),
            'totalUsers' => $totalUsers,
            'schoolAdmins' => $schoolAdmins,
            'staffMembers' => $staffMembers,
            'totalQuestions' => $totalQuestions,
            'monthlyRevenue' => $monthlyRevenue,
            'revenueGrowth' => $revenueGrowth,
            'notificationCount' => $notificationCount
        ];

        return view('superadmin/dashboard', $data);
    }

    /**
     * Schools management page
     */
    public function schools()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Please login first.');
        }

        $data = [
            'schools' => $this->schoolModel->orderBy('created_at', 'DESC')->findAll(),
            'pendingSchools' => $this->schoolModel->where('status', 'inactive')->findAll(),
            'activeSchools' => $this->schoolModel->where('status', 'active')->findAll(),
            'rejectedSchools' => $this->schoolModel->where('status', 'rejected')->findAll()
        ];

        return view('superadmin/schools', $data);
    }

    /**
     * Get total questions count from database
     */
    private function getTotalQuestions()
    {
        $questionModel = new \App\Models\QuestionModel();
        return $questionModel->where('deleted_at', null)->countAllResults();
    }

    /**
     * Get current month revenue
     */
    private function getMonthlyRevenue()
    {
        $paymentModel = new \App\Models\PaymentLogModel();
        $currentMonth = date('Y-m');

        $result = $paymentModel->select('SUM(amount) as total')
                              ->where('status', 'completed')
                              ->where('DATE_FORMAT(processed_at, "%Y-%m")', $currentMonth)
                              ->first();

        return $result['total'] ?? 0;
    }

    /**
     * Get revenue growth percentage compared to last month
     */
    private function getRevenueGrowth()
    {
        $paymentModel = new \App\Models\PaymentLogModel();
        $currentMonth = date('Y-m');
        $lastMonth = date('Y-m', strtotime('-1 month'));

        // Current month revenue
        $currentRevenue = $paymentModel->select('SUM(amount) as total')
                                     ->where('status', 'completed')
                                     ->where('DATE_FORMAT(processed_at, "%Y-%m")', $currentMonth)
                                     ->first();

        // Last month revenue
        $lastRevenue = $paymentModel->select('SUM(amount) as total')
                                  ->where('status', 'completed')
                                  ->where('DATE_FORMAT(processed_at, "%Y-%m")', $lastMonth)
                                  ->first();

        $current = $currentRevenue['total'] ?? 0;
        $last = $lastRevenue['total'] ?? 0;

        if ($last == 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $last) / $last) * 100, 1);
    }

    /**
     * Get notification count for SuperAdmin
     */
    private function getNotificationCount()
    {
        $count = 0;

        // Count pending school approvals
        $pendingSchools = $this->schoolModel->where('status', 'inactive')->countAllResults();
        $count += $pendingSchools;

        // Count recent critical activities (last 24 hours)
        $criticalLogs = $this->superAdminLogModel
                           ->where('severity', 'critical')
                           ->where('created_at >=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                           ->countAllResults();
        $count += $criticalLogs;

        // Count high severity activities (last 24 hours)
        $highSeverityLogs = $this->superAdminLogModel
                               ->where('severity', 'high')
                               ->where('created_at >=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                               ->countAllResults();
        $count += $highSeverityLogs;

        return $count;
    }

    /**
     * Get notifications for SuperAdmin
     */
    public function getNotifications()
    {
        try {
            $notifications = [];

            // Get pending school approvals
            $pendingSchools = $this->schoolModel->where('status', 'inactive')
                                               ->orderBy('created_at', 'DESC')
                                               ->findAll();

            foreach ($pendingSchools as $school) {
                $notifications[] = [
                    'id' => 'school_' . $school['id'],
                    'type' => 'school_approval',
                    'title' => 'School Approval Pending',
                    'message' => $school['name'] . ' is waiting for approval',
                    'time' => $school['created_at'],
                    'icon' => 'fas fa-school',
                    'color' => 'orange',
                    'priority' => 'high',
                    'action_url' => '/superadmin/schools',
                    'section' => 'schools'
                ];
            }

            // Get recent critical activities
            $criticalLogs = $this->superAdminLogModel
                               ->where('severity', 'critical')
                               ->where('created_at >=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                               ->orderBy('created_at', 'DESC')
                               ->limit(5)
                               ->findAll();

            foreach ($criticalLogs as $log) {
                $notifications[] = [
                    'id' => 'critical_' . $log['id'],
                    'type' => 'critical_alert',
                    'title' => 'Critical System Alert',
                    'message' => $log['description'] ?: $this->formatLogDescription($log),
                    'time' => $log['created_at'],
                    'icon' => 'fas fa-exclamation-triangle',
                    'color' => 'red',
                    'priority' => 'critical',
                    'action_url' => '#audit',
                    'section' => 'audit'
                ];
            }

            // Get high severity activities
            $highSeverityLogs = $this->superAdminLogModel
                                   ->where('severity', 'high')
                                   ->where('created_at >=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                                   ->orderBy('created_at', 'DESC')
                                   ->limit(3)
                                   ->findAll();

            foreach ($highSeverityLogs as $log) {
                $notifications[] = [
                    'id' => 'high_' . $log['id'],
                    'type' => 'high_alert',
                    'title' => 'High Priority Alert',
                    'message' => $log['description'] ?: $this->formatLogDescription($log),
                    'time' => $log['created_at'],
                    'icon' => 'fas fa-exclamation-circle',
                    'color' => 'yellow',
                    'priority' => 'high',
                    'section' => 'audit'
                ];
            }

            // Sort by time (most recent first)
            usort($notifications, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });

            // Limit to 10 most recent notifications
            $notifications = array_slice($notifications, 0, 10);

            return $this->response->setJSON([
                'success' => true,
                'notifications' => $notifications,
                'count' => count($notifications)
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Failed to get notifications: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON([
                'success' => false,
                'message' => 'Failed to load notifications'
            ]);
        }
    }

    // In app/Controllers/SuperAdmin.php

public function approveSchool($id)
{
    if (!$this->validate(['id' => 'required|is_natural_no_zero'])) {
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Invalid school ID'
        ]);
    }

    $school = $this->schoolModel->find($id);
    if (!$school) {
        return $this->response->setJSON(['success' => false, 'message' => 'School not found']);
    }

    $updated = $this->schoolModel->update($id, ['status' => 'active']);


    if ($updated) {
        try {
            $userId = session()->get('user_id');

            // Log the audit action
            $this->auditLogger->logApproval(
                'school',
                $id,
                $school['name'],
                $userId,
                $id,
                "Super admin approved school registration"
            );

            // Log super admin action
            $this->auditLogger->logSuperAdminAction(
                'approve_school',
                $userId,
                "Approved school registration: {$school['name']}",
                $id,
                null,
                ['status' => $school['status']],
                ['status' => 'active'],
                'high'
            );

            // Send approval email notification
            $emailSent = $this->emailService->sendSchoolApprovalEmail(
                $school['email'],
                $school['name'],
                $id
            );

            if (!$emailSent) {
                log_message('error', "Failed to send approval email to school: {$school['email']}");
            }

        } catch (\Exception $e) {
            log_message('error', "Failed to log school approval audit: " . $e->getMessage());
        }
    }


    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School approved successfully' : 'Failed to approve school'
    ]);
}

public function rejectSchool($id)
{
    // Log the request for debugging
    log_message('debug', "rejectSchool called with ID: {$id}");

    // Check authentication
    if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
        log_message('error', "Unauthorized access to rejectSchool");
        return $this->response->setStatusCode(401)->setJSON([
            'success' => false,
            'message' => 'Unauthorized access'
        ]);
    }

    // Get reason from POST or JSON
    $reason = null;
    if ($this->request->getPost('reason')) {
        $reason = $this->request->getPost('reason');
    } else {
        $jsonData = $this->request->getJSON(true);
        if ($jsonData && isset($jsonData['reason'])) {
            $reason = $jsonData['reason'];
        }
    }

    if (!$reason || trim($reason) === '') {
        log_message('error', "No reason provided for school rejection");
        return $this->response->setStatusCode(400)->setJSON([
            'success' => false,
            'message' => 'Reason for rejection is required'
        ]);
    }

    log_message('debug', "Rejection reason: {$reason}");

    $school = $this->schoolModel->find($id);
    if (!$school) {
        return $this->response->setJSON(['success' => false, 'message' => 'School not found']);
    }

    $updated = $this->schoolModel->update($id, [
        'status' => 'rejected',
        'rejection_reason' => $reason
    ]);


    if ($updated) {
        try {
            // Log the audit action
            $this->auditLogger->logRejection(
                'school',
                $id,
                $school['name'],
                $reason,
                session()->get('user_id'),
                $id
            );

            // Log super admin action
            $this->auditLogger->logSuperAdminAction(
                'reject_school',
                session()->get('user_id'),
                "Rejected school registration: {$school['name']} - Reason: {$reason}",
                $id,
                null,
                ['status' => $school['status']],
                ['status' => 'rejected', 'rejection_reason' => $reason],
                'high'
            );

            // Send rejection email notification
            log_message('debug', "Attempting to send rejection email to: {$school['email']}");
            $emailSent = $this->emailService->sendSchoolRejectionEmail(
                $school['email'],
                $school['name'],
                $reason,
                $id
            );

            if (!$emailSent) {
                log_message('error', "Failed to send rejection email to school: {$school['email']}");
            }

        } catch (\Exception $e) {
            log_message('error', "Failed to log school rejection audit: " . $e->getMessage());
        }
    }


    return $this->response->setJSON([
        'success' => $updated,
        'message' => $updated ? 'School rejected successfully' : 'Failed to reject school'
    ]);
}
public function viewSchoolDetails($id)
{
    if (!session()->get('isSuperAdmin')) {
        return redirect()->to('/login')->with('error', 'Access denied.');
    }

    $school = $this->schoolModel->find($id);

    if (!$school) {
        throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound("School not found");
    }

    return view('superadmin/view_school', ['school' => $school]);
}


    /**
     * Get all users for user management
     */
    public function getUsers()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            // Get users with school information and profiles
            $users = $this->userModel
                ->select('users.*, schools.name as school_name, user_profiles.designation, user_profiles.phone')
                ->join('schools', 'schools.id = users.school_id', 'left')
                ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                ->where('users.is_deleted', 0)
                ->orderBy('users.created_at', 'DESC')
                ->findAll();

            return $this->response->setJSON([
                'success' => true,
                'users' => $users
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch users: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get user details by ID
     */
    public function getUserDetails($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel
                ->select('users.*, schools.name as school_name, user_profiles.designation, user_profiles.phone')
                ->join('schools', 'schools.id = users.school_id', 'left')
                ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                ->where('users.id', $userId)
                ->where('users.is_deleted', 0)
                ->first();

            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            return $this->response->setJSON([
                'success' => true,
                'user' => $user
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch user details: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update user status (activate/deactivate)
     */
    public function toggleUserStatus($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel->find($userId);
            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            $newStatus = ($user['status'] === 'active') ? 'inactive' : 'active';
            $updated = $this->userModel->update($userId, ['status' => $newStatus]);

            if ($updated) {
                // TODO: Enable audit logging once audit tables are created
                // Log the audit action
                /*
                $this->auditLogger->logUpdate(
                    'user',
                    $userId,
                    $user['name'],
                    ['status' => $user['status']],
                    ['status' => $newStatus],
                    session()->get('user_id'),
                    null,
                    "Super admin changed user status from {$user['status']} to {$newStatus}"
                );

                // Log super admin action
                $this->auditLogger->logSuperAdminAction(
                    'toggle_user_status',
                    session()->get('user_id'),
                    "Changed user status for {$user['name']} from {$user['status']} to {$newStatus}",
                    $user['school_id'],
                    $userId,
                    ['status' => $user['status']],
                    ['status' => $newStatus],
                    'medium'
                );
                */
            }

            return $this->response->setJSON([
                'success' => $updated,
                'message' => $updated ? 'User status updated successfully' : 'Failed to update user status',
                'new_status' => $newStatus
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to update user status: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete user (soft delete)
     */
    public function deleteUser($userId)
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $user = $this->userModel->find($userId);
            if (!$user) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'User not found'
                ]);
            }

            // Soft delete the user
            $updated = $this->userModel->update($userId, [
                'is_deleted' => 1,
                'deleted_at' => date('Y-m-d H:i:s')
            ]);

            if ($updated) {
                // TODO: Enable audit logging once audit tables are created
                // Log the audit action
                /*
                $this->auditLogger->logDelete(
                    'user',
                    $userId,
                    $user['name'],
                    $user,
                    session()->get('user_id'),
                    $user['school_id'],
                    "Super admin deleted user: {$user['name']}"
                );

                // Log super admin action
                $this->auditLogger->logSuperAdminAction(
                    'delete_user',
                    session()->get('user_id'),
                    "Deleted user: {$user['name']} ({$user['email']})",
                    $user['school_id'],
                    $userId,
                    $user,
                    null,
                    'high'
                );
                */
            }

            return $this->response->setJSON([
                'success' => $updated,
                'message' => $updated ? 'User deleted successfully' : 'Failed to delete user'
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to delete user: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get audit logs with filters and pagination
     */
    public function getAuditLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $page = (int)($request->getGet('page') ?? 1);
            $perPage = (int)($request->getGet('per_page') ?? 50);

            $filters = [
                'school_id' => $request->getGet('school_id'),
                'user_id' => $request->getGet('user_id'),
                'action' => $request->getGet('action'),
                'entity_type' => $request->getGet('entity_type'),
                'severity' => $request->getGet('severity'),
                'status' => $request->getGet('status'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->auditLogModel->getAuditLogs($filters, $page, $perPage);

            return $this->response->setJSON([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch audit logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get audit log statistics
     */
    public function getAuditStats()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $schoolId = $request->getGet('school_id');
            $dateFrom = $request->getGet('date_from');
            $dateTo = $request->getGet('date_to');

            $stats = $this->auditLogModel->getAuditStats($schoolId, $dateFrom, $dateTo);

            return $this->response->setJSON([
                'success' => true,
                'stats' => $stats
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch audit statistics: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get super admin logs
     */
    public function getSuperAdminLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $request = $this->request;
            $page = (int)($request->getGet('page') ?? 1);
            $perPage = (int)($request->getGet('per_page') ?? 50);

            $filters = [
                'admin_user_id' => $request->getGet('admin_user_id'),
                'action' => $request->getGet('action'),
                'target_school_id' => $request->getGet('target_school_id'),
                'severity' => $request->getGet('severity'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            $result = $this->superAdminLogModel->getSuperAdminLogs($filters, $page, $perPage);

            return $this->response->setJSON([
                'success' => true,
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch super admin logs: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Export audit logs to CSV
     */
    public function exportAuditLogs()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return redirect()->to('/login/superadmin')->with('error', 'Access denied');
        }

        try {
            $request = $this->request;

            $filters = [
                'school_id' => $request->getGet('school_id'),
                'user_id' => $request->getGet('user_id'),
                'action' => $request->getGet('action'),
                'entity_type' => $request->getGet('entity_type'),
                'severity' => $request->getGet('severity'),
                'status' => $request->getGet('status'),
                'date_from' => $request->getGet('date_from'),
                'date_to' => $request->getGet('date_to'),
                'search' => $request->getGet('search')
            ];

            // Remove empty filters
            $filters = array_filter($filters, function($value) {
                return $value !== null && $value !== '';
            });

            // Get all logs (no pagination for export)
            $result = $this->auditLogModel->getAuditLogs($filters, 1, 10000);
            $logs = $result['logs'];

            // Set headers for CSV download
            $filename = 'audit_logs_' . date('Y-m-d_H-i-s') . '.csv';
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');

            // Create file pointer
            $output = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($output, [
                'ID', 'Date/Time', 'School', 'User', 'Action', 'Entity Type',
                'Entity Name', 'Description', 'Severity', 'Status', 'IP Address'
            ]);

            // Add data rows
            foreach ($logs as $log) {
                fputcsv($output, [
                    $log['id'],
                    $log['created_at'],
                    $log['school_name'] ?? 'N/A',
                    $log['user_name'] ?? 'System',
                    $log['action'],
                    $log['entity_type'],
                    $log['entity_name'] ?? 'N/A',
                    $log['description'],
                    $log['severity'],
                    $log['status'],
                    $log['ip_address'] ?? 'N/A'
                ]);
            }

            fclose($output);
            exit;
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to export audit logs: ' . $e->getMessage());
        }
    }

    /**
     * Get filter options for audit logs
     */
    public function getAuditFilterOptions()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            $actions = $this->auditLogModel->getDistinctActions();
            $entityTypes = $this->auditLogModel->getDistinctEntityTypes();
            $schools = $this->schoolModel->select('id, name')->findAll();

            return $this->response->setJSON([
                'success' => true,
                'options' => [
                    'actions' => $actions,
                    'entity_types' => $entityTypes,
                    'schools' => $schools,
                    'severities' => ['low', 'medium', 'high', 'critical'],
                    'statuses' => ['success', 'failed', 'warning']
                ]
            ]);
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch filter options: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get recent activities for SuperAdmin dashboard
     */
    public function getRecentActivities()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Access denied'
            ]);
        }

        try {
            // Get recent super admin logs
            $recentLogs = $this->superAdminLogModel->getRecentSuperAdminLogs(10);

            // Get recent school registrations
            $recentSchools = $this->schoolModel->select('id, name, created_at, status')
                                              ->orderBy('created_at', 'DESC')
                                              ->limit(5)
                                              ->findAll();

            // Get recent user registrations
            $recentUsers = $this->userModel->select('users.id, users.name, users.email, users.created_at, schools.name as school_name')
                                          ->join('schools', 'schools.id = users.school_id', 'left')
                                          ->where('users.is_deleted', 0)
                                          ->orderBy('users.created_at', 'DESC')
                                          ->limit(5)
                                          ->findAll();

            $activities = [];

            // Process super admin logs
            foreach ($recentLogs as $log) {
                $activities[] = [
                    'type' => 'admin_action',
                    'title' => $this->formatLogTitle($log['action']),
                    'description' => $log['description'] ?: $this->formatLogDescription($log),
                    'time' => $log['created_at'],
                    'icon' => $this->getLogIcon($log['action']),
                    'color' => $this->getLogColor($log['severity']),
                    'severity' => $log['severity']
                ];
            }

            // Process recent schools
            foreach ($recentSchools as $school) {
                $activities[] = [
                    'type' => 'school',
                    'title' => 'New school registered',
                    'description' => $school['name'] . ' joined the platform',
                    'time' => $school['created_at'],
                    'icon' => 'fas fa-school',
                    'color' => 'blue',
                    'severity' => 'medium'
                ];
            }

            // Process recent users
            foreach ($recentUsers as $user) {
                $activities[] = [
                    'type' => 'user',
                    'title' => 'New user registered',
                    'description' => $user['name'] . ' joined ' . ($user['school_name'] ?: 'the platform'),
                    'time' => $user['created_at'],
                    'icon' => 'fas fa-user-plus',
                    'color' => 'green',
                    'severity' => 'low'
                ];
            }

            // Sort activities by time (most recent first)
            usort($activities, function($a, $b) {
                return strtotime($b['time']) - strtotime($a['time']);
            });

            // Limit to 15 most recent activities
            $activities = array_slice($activities, 0, 15);

            return $this->response->setJSON([
                'success' => true,
                'activities' => $activities
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch recent activities: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Format log title based on action
     */
    private function formatLogTitle($action)
    {
        $titles = [
            'school_created' => 'School Created',
            'school_updated' => 'School Updated',
            'school_deleted' => 'School Deleted',
            'user_created' => 'User Created',
            'user_updated' => 'User Updated',
            'user_deleted' => 'User Deleted',
            'subscription_updated' => 'Subscription Updated',
            'system_settings_updated' => 'System Settings Updated',
            'login' => 'Admin Login',
            'logout' => 'Admin Logout'
        ];

        return $titles[$action] ?? ucwords(str_replace('_', ' ', $action));
    }

    /**
     * Format log description
     */
    private function formatLogDescription($log)
    {
        if ($log['target_school_name']) {
            return 'Action performed on ' . $log['target_school_name'];
        } elseif ($log['target_user_name']) {
            return 'Action performed on user ' . $log['target_user_name'];
        } elseif ($log['admin_name']) {
            return 'Action performed by ' . $log['admin_name'];
        }

        return 'System action performed';
    }

    /**
     * Get icon for log action
     */
    private function getLogIcon($action)
    {
        $icons = [
            'school_created' => 'fas fa-school',
            'school_updated' => 'fas fa-edit',
            'school_deleted' => 'fas fa-trash',
            'user_created' => 'fas fa-user-plus',
            'user_updated' => 'fas fa-user-edit',
            'user_deleted' => 'fas fa-user-minus',
            'subscription_updated' => 'fas fa-credit-card',
            'system_settings_updated' => 'fas fa-cogs',
            'login' => 'fas fa-sign-in-alt',
            'logout' => 'fas fa-sign-out-alt'
        ];

        return $icons[$action] ?? 'fas fa-info-circle';
    }

    /**
     * Get color for log severity
     */
    private function getLogColor($severity)
    {
        $colors = [
            'low' => 'green',
            'medium' => 'blue',
            'high' => 'yellow',
            'critical' => 'red'
        ];

        return $colors[$severity] ?? 'gray';
    }


public function getSchoolDetails($id)
{
    if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
        return $this->response->setJSON([
            'success' => false,
            'message' => 'Access denied'
        ]);
    }

    $school = $this->schoolModel->find($id);

    if (!$school) {
        return $this->response->setJSON([
            'success' => false,
            'message' => 'School not found'
        ]);
    }

    return $this->response->setJSON([
        'success' => true,
        'school' => $school
    ]);
}

public function getSubscriptionData()
{
    if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
        return $this->response->setJSON([
            'success' => false,
            'message' => 'Access denied'
        ]);
    }

    // Initialize models
    $subscriptionModel = new SubscriptionModel();
    $planModel = new PlanModel();
    $paymentLogModel = new PaymentLogModel();

    // Get subscription statistics
    $stats = [
        'active' => 0,
        'trial' => 0,
        'pending' => 0,
        'expired' => $subscriptionModel->whereIn('status', ['expired', 'cancelled', 'suspended'])->countAllResults(),
        'total_revenue' => $paymentLogModel->selectSum('amount')->where('status', 'completed')->get()->getRow()->amount ?? 0
    ];

    // Get detailed subscription data with school and plan information
    $subscriptions = $subscriptionModel
        ->select('subscriptions.*, schools.name as school_name, schools.email as school_email, plans.display_name as plan_name, plans.price_monthly, plans.price_yearly')
        ->join('schools', 'schools.id = subscriptions.school_id')
        ->join('plans', 'plans.id = subscriptions.plan_id')
        ->orderBy('subscriptions.created_at', 'DESC')
        ->findAll();

    // Update subscription status based on payment completion
    foreach ($subscriptions as &$subscription) {
        // Check if there are completed payments for this subscription
        $completedPayments = $paymentLogModel
            ->where('subscription_id', $subscription['id'])
            ->where('status', 'completed')
            ->countAllResults();

        // If subscription amount > 0 and no completed payments, mark as pending
        if ($subscription['amount'] > 0 && $completedPayments == 0) {
            $subscription['actual_status'] = 'pending';
        }
        // If it's a trial (amount = 0), keep as trial
        else if ($subscription['amount'] == 0) {
            $subscription['actual_status'] = 'trial';
        }
        // If has completed payments, use original status
        else {
            $subscription['actual_status'] = $subscription['status'];
        }

        // Update statistics based on actual status
        switch ($subscription['actual_status']) {
            case 'active':
                $stats['active']++;
                break;
            case 'trial':
                $stats['trial']++;
                break;
            case 'pending':
                $stats['pending']++;
                break;
        }
    }

    // Get recent payments
    $recentPayments = $paymentLogModel
        ->select('payment_logs.*, schools.name as school_name, plans.display_name as plan_name')
        ->join('subscriptions', 'subscriptions.id = payment_logs.subscription_id')
        ->join('schools', 'schools.id = payment_logs.school_id')
        ->join('plans', 'plans.id = subscriptions.plan_id')
        ->orderBy('payment_logs.created_at', 'DESC')
        ->limit(10)
        ->findAll();

    return $this->response->setJSON([
        'success' => true,
        'stats' => $stats,
        'subscriptions' => $subscriptions,
        'recent_payments' => $recentPayments
    ]);
}

/**
 * Get analytics data for the analytics dashboard
 */
public function getAnalyticsData()
{
    if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
        return $this->response->setJSON([
            'success' => false,
            'message' => 'Access denied'
        ]);
    }

    try {
        $request = $this->request;
        $dateRange = $request->getGet('date_range') ?? '30';
        $startDate = $request->getGet('start_date');
        $endDate = $request->getGet('end_date');

        // Calculate date ranges
        if ($dateRange === 'custom' && $startDate && $endDate) {
            $currentStart = $startDate;
            $currentEnd = $endDate;
            $daysDiff = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24);
            $previousStart = date('Y-m-d', strtotime($startDate . " -{$daysDiff} days"));
            $previousEnd = date('Y-m-d', strtotime($endDate . " -{$daysDiff} days"));
        } else {
            $days = (int)$dateRange;
            $currentStart = date('Y-m-d', strtotime("-{$days} days"));
            $currentEnd = date('Y-m-d');
            $previousStart = date('Y-m-d', strtotime("-" . ($days * 2) . " days"));
            $previousEnd = date('Y-m-d', strtotime("-{$days} days"));
        }

        // Get current period statistics
        $currentStats = $this->getStatsForPeriod($currentStart, $currentEnd);
        $previousStats = $this->getStatsForPeriod($previousStart, $previousEnd);

        // Calculate trends
        $trends = $this->calculateTrends($currentStats, $previousStats);

        // Get time series data for charts
        $chartData = $this->getChartData($currentStart, $currentEnd, $dateRange);

        // Get subscription distribution
        $subscriptionModel = new SubscriptionModel();
        $subscriptionStats = [
            'active' => $subscriptionModel->where('status', 'active')->countAllResults(),
            'trial' => $subscriptionModel->where('amount', 0)->countAllResults(),
            'expired' => $subscriptionModel->whereIn('status', ['expired', 'cancelled', 'suspended'])->countAllResults(),
            'pending' => $subscriptionModel->where('status', 'pending')->countAllResults()
        ];

        return $this->response->setJSON([
            'success' => true,
            'data' => [
                'stats' => $currentStats,
                'trends' => $trends,
                'chart_data' => $chartData,
                'subscription_stats' => $subscriptionStats,
                'date_range' => [
                    'start' => $currentStart,
                    'end' => $currentEnd,
                    'days' => $dateRange
                ]
            ]
        ]);
    } catch (\Exception $e) {
        return $this->response->setJSON([
            'success' => false,
            'message' => 'Failed to fetch analytics data: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get statistics for a specific period
 */
private function getStatsForPeriod($startDate, $endDate)
{
    // For analytics, show cumulative totals as of the end date
    $totalUsers = $this->userModel
        ->where('created_at <=', $endDate . ' 23:59:59')
        ->where('is_deleted', 0)
        ->countAllResults();

    $totalSchools = $this->schoolModel
        ->where('created_at <=', $endDate . ' 23:59:59')
        ->countAllResults();

    // Active schools as of the end date
    $activeSchools = $this->schoolModel
        ->where('status', 'active')
        ->where('created_at <=', $endDate . ' 23:59:59')
        ->countAllResults();

    $questionModel = new \App\Models\QuestionModel();
    $totalQuestions = $questionModel
        ->where('created_at <=', $endDate . ' 23:59:59')
        ->where('deleted_at', null)
        ->countAllResults();

    // Revenue for the period (actual period revenue)
    $paymentModel = new \App\Models\PaymentLogModel();
    $result = $paymentModel->select('SUM(amount) as total')
        ->where('status', 'completed')
        ->where('processed_at >=', $startDate . ' 00:00:00')
        ->where('processed_at <=', $endDate . ' 23:59:59')
        ->first();
    $revenue = floatval($result['total'] ?? 0);

    return [
        'total_users' => $totalUsers,
        'total_schools' => $totalSchools,
        'active_schools' => $activeSchools,
        'total_questions' => $totalQuestions,
        'revenue' => $revenue
    ];
}

/**
 * Calculate trends between current and previous periods
 */
private function calculateTrends($current, $previous)
{
    $trends = [];

    foreach ($current as $key => $value) {
        $prevValue = $previous[$key] ?? 0;

        if ($prevValue == 0) {
            $change = $value > 0 ? 100 : 0;
        } else {
            $change = round((($value - $prevValue) / $prevValue) * 100, 1);
        }

        $trends[$key] = [
            'current' => $value,
            'previous' => $prevValue,
            'change' => $change,
            'direction' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'same')
        ];
    }

    return $trends;
}

/**
 * Get chart data for the specified period
 */
private function getChartData($startDate, $endDate, $dateRange)
{
    $days = (int)$dateRange;
    if ($dateRange === 'custom') {
        $days = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24);
    }

    // Determine interval based on date range
    if ($days <= 7) {
        $interval = 'day';
        $format = 'Y-m-d';
        $labelFormat = 'M j';
    } elseif ($days <= 90) {
        $interval = 'week';
        $format = 'Y-W';
        $labelFormat = 'M j';
    } else {
        $interval = 'month';
        $format = 'Y-m';
        $labelFormat = 'M Y';
    }

    return [
        'users' => $this->getTimeSeriesData('users', $startDate, $endDate, $interval, $format, $labelFormat),
        'schools' => $this->getTimeSeriesData('schools', $startDate, $endDate, $interval, $format, $labelFormat),
        'questions' => $this->getTimeSeriesData('questions', $startDate, $endDate, $interval, $format, $labelFormat),
        'revenue' => $this->getTimeSeriesData('revenue', $startDate, $endDate, $interval, $format, $labelFormat)
    ];
}

/**
 * Get time series data for a specific metric
 */
private function getTimeSeriesData($metric, $startDate, $endDate, $interval, $format, $labelFormat)
{
    $data = [];
    $labels = [];

    $current = strtotime($startDate);
    $end = strtotime($endDate);

    while ($current <= $end) {
        $periodStart = date('Y-m-d', $current);

        if ($interval === 'day') {
            $periodEnd = $periodStart;
            $current = strtotime('+1 day', $current);
        } elseif ($interval === 'week') {
            $periodEnd = date('Y-m-d', strtotime('+6 days', $current));
            $current = strtotime('+1 week', $current);
        } else { // month
            $periodEnd = date('Y-m-t', $current);
            $current = strtotime('+1 month', $current);
        }

        $value = $this->getMetricValue($metric, $periodStart, $periodEnd);

        $data[] = $value;
        $labels[] = date($labelFormat, strtotime($periodStart));
    }

    return [
        'labels' => $labels,
        'data' => $data
    ];
}

/**
 * Get metric value for a specific period (new registrations/activity in period)
 */
private function getMetricValue($metric, $startDate, $endDate)
{
    switch ($metric) {
        case 'users':
            return $this->userModel
                ->where('created_at >=', $startDate . ' 00:00:00')
                ->where('created_at <=', $endDate . ' 23:59:59')
                ->where('is_deleted', 0)
                ->countAllResults();

        case 'schools':
            return $this->schoolModel
                ->where('created_at >=', $startDate . ' 00:00:00')
                ->where('created_at <=', $endDate . ' 23:59:59')
                ->countAllResults();

        case 'questions':
            $questionModel = new \App\Models\QuestionModel();
            return $questionModel
                ->where('created_at >=', $startDate . ' 00:00:00')
                ->where('created_at <=', $endDate . ' 23:59:59')
                ->where('deleted_at', null)
                ->countAllResults();

        case 'revenue':
            $paymentModel = new \App\Models\PaymentLogModel();
            $result = $paymentModel->select('SUM(amount) as total')
                ->where('status', 'completed')
                ->where('processed_at >=', $startDate . ' 00:00:00')
                ->where('processed_at <=', $endDate . ' 23:59:59')
                ->first();
            return floatval($result['total'] ?? 0);

        default:
            return 0;
    }
}

    /**
     * Get system settings
     */
    public function getSystemSettings()
    {
        try {
            // Check if user is authorized
            if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Access denied'
                ]);
            }

            // Initialize defaults if not already done
            $this->systemSettingsModel->initializeDefaults();

            $settings = $this->systemSettingsModel->getAllSettings();

            return $this->response->setJSON([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            log_message('error', 'Failed to get system settings: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to load system settings: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update system settings
     */
    public function updateSystemSettings()
    {
        try {
            $settingsData = $this->request->getJSON(true);

            if (empty($settingsData)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'No settings data provided'
                ]);
            }

            // Update settings
            $updated = $this->systemSettingsModel->updateSettings($settingsData);

            if ($updated) {
                // Log the action - Skip audit logging for now to avoid SQL error
                // TODO: Fix audit logging for superadmin
                /*
                $userId = session()->get('user_id');
                $this->auditLogger->logSuperAdminAction(
                    'system_settings_updated',
                    $userId,
                    'Updated system settings',
                    null,
                    null,
                    [],
                    $settingsData,
                    'medium'
                );
                */

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'System settings updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update system settings'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Failed to update system settings: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update individual setting
     */
    public function updateSetting()
    {
        try {
            $data = $this->request->getJSON(true);

            if (!isset($data['key']) || !isset($data['value'])) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Setting key and value are required'
                ]);
            }

            // Get existing setting to check if it's editable
            $existing = $this->systemSettingsModel->where('setting_key', $data['key'])->first();

            if (!$existing) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Setting not found'
                ]);
            }

            if (!$existing['is_editable']) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'This setting cannot be modified'
                ]);
            }

            // Update the setting
            $updated = $this->systemSettingsModel->setSetting(
                $data['key'],
                $data['value'],
                $existing['setting_type'],
                $existing['category']
            );

            if ($updated) {
                // Log the action - Skip audit logging for now to avoid SQL error
                // TODO: Fix audit logging for superadmin
                /*
                $userId = session()->get('user_id');
                $this->auditLogger->logSuperAdminAction(
                    'system_setting_updated',
                    $userId,
                    "Updated setting: {$data['key']}",
                    null,
                    null,
                    ['value' => $existing['setting_value']],
                    ['value' => $data['value']],
                    'low'
                );
                */

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Setting updated successfully'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to update setting'
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Failed to update setting: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test endpoint to check if routes are working
     */
    public function testProfile()
    {
        return $this->response->setJSON([
            'success' => true,
            'message' => 'Test endpoint working',
            'session_data' => [
                'logged_in' => session()->get('logged_in'),
                'role' => session()->get('role'),
                'user_id' => session()->get('user_id')
            ]
        ]);
    }

    /**
     * Get superadmin profile data for popup
     */
    public function getProfile()
    {
        log_message('info', 'getProfile method called');

        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            log_message('error', 'Unauthorized access to getProfile');
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Unauthorized access'
            ]);
        }

        try {
            $userId = session()->get('user_id') ?? 1;
            log_message('info', 'Getting profile for user ID: ' . $userId);

            $profile = $this->superAdminModel->getSuperAdminProfile($userId);
            log_message('info', 'Profile data: ' . json_encode($profile));

            if (!$profile) {
                log_message('error', 'Profile not found for user ID: ' . $userId);
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Profile not found'
                ]);
            }

            // Format the data for the popup
            $formattedProfile = [
                'name' => $profile['name'] ?? 'Super Administrator',
                'email' => $profile['email'] ?? '<EMAIL>',
                'role' => 'Super Administrator',
                'status' => ucfirst($profile['status'] ?? 'active'),
                'account_created' => isset($profile['created_at']) ? date('M j, Y', strtotime($profile['created_at'])) : 'Jan 15, 2024',
                'last_login' => 'Today, ' . date('g:i A'),
                'schools_managed' => $profile['statistics']['total_schools'] ?? 0,
                'total_sessions' => $profile['statistics']['total_sessions'] ?? 0
            ];

            return $this->response->setJSON([
                'success' => true,
                'profile' => $formattedProfile
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error fetching superadmin profile: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to fetch profile data: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Change superadmin password
     */
    public function changePassword()
    {
        if (!session()->get('logged_in') || session()->get('role') !== 'superadmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Unauthorized access'
            ]);
        }

        $currentPassword = $this->request->getPost('current_password');
        $newPassword = $this->request->getPost('new_password');
        $confirmPassword = $this->request->getPost('confirm_password');

        // Validate input
        if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'All fields are required'
            ]);
        }

        if ($newPassword !== $confirmPassword) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'New passwords do not match'
            ]);
        }

        if (strlen($newPassword) < 6) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Password must be at least 6 characters long'
            ]);
        }

        try {
            $userId = session()->get('user_id') ?? 1;
            $result = $this->superAdminModel->changePassword($userId, $currentPassword, $newPassword);

            if ($result['success']) {
                // Log password change
                $this->auditLogger->logSuperAdminAction(
                    'password_change',
                    $userId,
                    'Super admin changed password',
                    null,
                    null,
                    null,
                    null,
                    'medium'
                );
            }

            return $this->response->setJSON($result);

        } catch (\Exception $e) {
            log_message('error', 'Error changing superadmin password: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to change password'
            ]);
        }
    }
}