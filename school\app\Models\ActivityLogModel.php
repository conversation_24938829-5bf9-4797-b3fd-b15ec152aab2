<?php

namespace App\Models;

use CodeIgniter\Model;

class ActivityLogModel extends Model
{
    protected $table = 'activity_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'user_id', 'activity_type', 'activity_description',
        'url', 'ip_address', 'user_agent', 'session_id', 'duration'
    ];

    // Dates
    protected $useTimestamps = false; // We'll handle timestamps manually
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = '';

    // Validation
    protected $validationRules = [
        'activity_type' => 'required|max_length[50]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Log user activity
     */
    public function logActivity($data)
    {
        // Ensure required fields
        if (!isset($data['activity_type'])) {
            return false;
        }

        // Set defaults
        $logData = array_merge([
            'school_id' => null,
            'user_id' => null,
            'activity_description' => null,
            'url' => $this->getCurrentUrl(),
            'ip_address' => $this->getClientIP(),
            'user_agent' => $this->getUserAgent(),
            'session_id' => session_id(),
            'duration' => null,
            'created_at' => date('Y-m-d H:i:s')
        ], $data);

        return $this->insert($logData);
    }

    /**
     * Get user activities with filters
     */
    public function getUserActivities($userId, $schoolId = null, $limit = 50)
    {
        $builder = $this->select('
            activity_logs.*,
            users.name as user_name,
            schools.name as school_name
        ')
        ->join('users', 'users.id = activity_logs.user_id', 'left')
        ->join('schools', 'schools.id = activity_logs.school_id', 'left')
        ->where('activity_logs.user_id', $userId);

        if ($schoolId) {
            $builder->where('activity_logs.school_id', $schoolId);
        }

        return $builder->orderBy('activity_logs.created_at', 'DESC')
                      ->limit($limit)
                      ->findAll();
    }

    /**
     * Get school activities
     */
    public function getSchoolActivities($schoolId, $limit = 100)
    {
        return $this->select('
            activity_logs.*,
            users.name as user_name
        ')
        ->join('users', 'users.id = activity_logs.user_id', 'left')
        ->where('activity_logs.school_id', $schoolId)
        ->orderBy('activity_logs.created_at', 'DESC')
        ->limit($limit)
        ->findAll();
    }

    /**
     * Get activity statistics
     */
    public function getActivityStats($schoolId = null, $dateFrom = null, $dateTo = null)
    {
        $builder = $this->db->table($this->table);

        if ($schoolId) {
            $builder->where('school_id', $schoolId);
        }

        if ($dateFrom) {
            $builder->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $builder->where('created_at <=', $dateTo . ' 23:59:59');
        }

        $stats = [
            'total_activities' => $builder->countAllResults(false),
            'activities_by_type' => $builder->select('activity_type, COUNT(*) as count')
                                          ->groupBy('activity_type')
                                          ->orderBy('count', 'DESC')
                                          ->get()->getResultArray(),
            'most_active_users' => $builder->select('users.name, COUNT(*) as count')
                                         ->join('users', 'users.id = activity_logs.user_id', 'left')
                                         ->where('activity_logs.user_id IS NOT NULL')
                                         ->groupBy('activity_logs.user_id')
                                         ->orderBy('count', 'DESC')
                                         ->limit(10)
                                         ->get()->getResultArray(),
            'daily_activity' => $builder->select('DATE(created_at) as date, COUNT(*) as count')
                                      ->groupBy('DATE(created_at)')
                                      ->orderBy('date', 'DESC')
                                      ->limit(30)
                                      ->get()->getResultArray()
        ];

        return $stats;
    }

    /**
     * Clean old activity logs
     */
    public function cleanOldActivities($daysToKeep = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Get current URL
     */
    private function getCurrentUrl()
    {
        $request = \Config\Services::request();
        return $request->getUri()->getPath();
    }

    /**
     * Get client IP address
     */
    private function getClientIP()
    {
        try {
            if (is_cli()) {
                return '127.0.0.1';
            }
            $request = \Config\Services::request();
            return $request->getIPAddress();
        } catch (\Exception $e) {
            return '127.0.0.1';
        }
    }

    /**
     * Get user agent
     */
    private function getUserAgent()
    {
        try {
            $request = \Config\Services::request();
            if (is_cli()) {
                return 'CLI';
            }
            return $request->getUserAgent()->getAgentString();
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}
