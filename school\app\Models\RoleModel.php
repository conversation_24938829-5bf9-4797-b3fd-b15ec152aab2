<?php

namespace App\Models;

use CodeIgniter\Model;

class RoleModel extends Model
{
    protected $table = 'roles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'name', 'priority', 'created_at', 'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'name' => 'required|max_length[255]',
        'priority' => 'permit_empty|integer'
    ];

    protected $validationMessages = [
        'school_id' => [
            'required' => 'School ID is required',
            'integer' => 'School ID must be a valid number'
        ],
        'name' => [
            'required' => 'Role name is required',
            'max_length' => 'Role name cannot exceed 255 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get roles by school ID
     */
    public function getRolesBySchool($schoolId)
    {
        return $this->where('school_id', $schoolId)
                   ->orderBy('priority', 'ASC')
                   ->orderBy('name', 'ASC')
                   ->findAll();
    }

    /**
     * Get role by name and school
     */
    public function getRoleByName($schoolId, $roleName)
    {
        return $this->where('school_id', $schoolId)
                   ->where('name', $roleName)
                   ->first();
    }

    /**
     * Create default roles for a school
     */
    public function createDefaultRoles($schoolId)
    {
        $defaultRoles = [
            ['name' => 'Admin', 'priority' => 1],
            ['name' => 'Principal', 'priority' => 2],
            ['name' => 'Vice Principal', 'priority' => 3],
            ['name' => 'Head of Department', 'priority' => 4],
            ['name' => 'Senior Teacher', 'priority' => 5],
            ['name' => 'Teacher', 'priority' => 6],
            ['name' => 'Assistant Teacher', 'priority' => 7],
            ['name' => 'Reviewer', 'priority' => 8],
            ['name' => 'Staff', 'priority' => 9]
        ];

        $insertData = [];
        foreach ($defaultRoles as $role) {
            $insertData[] = [
                'school_id' => $schoolId,
                'name' => $role['name'],
                'priority' => $role['priority'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
        }

        return $this->insertBatch($insertData);
    }

    /**
     * Get role hierarchy (roles with higher priority)
     */
    public function getHigherRoles($schoolId, $currentPriority)
    {
        return $this->where('school_id', $schoolId)
                   ->where('priority <', $currentPriority)
                   ->orderBy('priority', 'ASC')
                   ->findAll();
    }

    /**
     * Get role hierarchy (roles with lower priority)
     */
    public function getLowerRoles($schoolId, $currentPriority)
    {
        return $this->where('school_id', $schoolId)
                   ->where('priority >', $currentPriority)
                   ->orderBy('priority', 'ASC')
                   ->findAll();
    }

    /**
     * Check if role exists in school
     */
    public function roleExists($schoolId, $roleName, $excludeId = null)
    {
        $builder = $this->where('school_id', $schoolId)
                       ->where('name', $roleName);
        
        if ($excludeId) {
            $builder->where('id !=', $excludeId);
        }
        
        return $builder->countAllResults() > 0;
    }

    /**
     * Get roles with user count
     */
    public function getRolesWithUserCount($schoolId)
    {
        return $this->select('roles.*, COUNT(user_roles.user_id) as user_count')
                   ->join('user_roles', 'user_roles.role_id = roles.id', 'left')
                   ->where('roles.school_id', $schoolId)
                   ->groupBy('roles.id')
                   ->orderBy('roles.priority', 'ASC')
                   ->findAll();
    }

    /**
     * Delete role and reassign users
     */
    public function deleteRoleAndReassign($roleId, $newRoleId = null)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // If new role specified, update user roles
            if ($newRoleId) {
                $db->table('user_roles')
                   ->where('role_id', $roleId)
                   ->update(['role_id' => $newRoleId, 'updated_at' => date('Y-m-d H:i:s')]);
            } else {
                // Delete user role assignments
                $db->table('user_roles')
                   ->where('role_id', $roleId)
                   ->delete();
            }

            // Delete role permissions
            $db->table('role_permissions')
               ->where('role_id', $roleId)
               ->delete();

            // Delete the role
            $this->delete($roleId);

            $db->transComplete();
            return $db->transStatus();

        } catch (\Exception $e) {
            $db->transRollback();
            log_message('error', 'Failed to delete role: ' . $e->getMessage());
            return false;
        }
    }
}
