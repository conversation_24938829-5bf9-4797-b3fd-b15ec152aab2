<?php

namespace App\Models;

use CodeIgniter\Model;

class UsageTrackerModel extends Model
{
    protected $table = 'usage_tracker';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'feature_key', 'current_usage', 'usage_limit',
        'reset_period', 'last_reset_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'feature_key' => 'required|max_length[100]',
        'current_usage' => 'required|integer',
        'reset_period' => 'required|in_list[daily,weekly,monthly,yearly,never]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Initialize usage tracking for a school based on their plan
     */
    public function initializeSchoolUsage($schoolId, $planId)
    {
        $planFeatureModel = new PlanFeatureModel();
        $features = $planFeatureModel->getPlanFeaturesArray($planId);

        $trackableFeatures = [
            'max_staff' => 'monthly',
            'max_questions' => 'monthly',
            'max_subjects' => 'monthly',
            'max_papers' => 'monthly'
        ];

        foreach ($trackableFeatures as $featureKey => $resetPeriod) {
            if (isset($features[$featureKey])) {
                $feature = $features[$featureKey];
                $limit = $feature['unlimited'] ? null : (int)$feature['value'];

                $this->createOrUpdateUsageTracker($schoolId, $featureKey, 0, $limit, $resetPeriod);
            }
        }

        return true;
    }

    /**
     * Create or update usage tracker
     */
    public function createOrUpdateUsageTracker($schoolId, $featureKey, $currentUsage = 0, $usageLimit = null, $resetPeriod = 'monthly')
    {
        $existing = $this->where('school_id', $schoolId)
                        ->where('feature_key', $featureKey)
                        ->first();

        $data = [
            'school_id' => $schoolId,
            'feature_key' => $featureKey,
            'current_usage' => $currentUsage,
            'usage_limit' => $usageLimit,
            'reset_period' => $resetPeriod,
            'last_reset_at' => date('Y-m-d H:i:s')
        ];

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Check if school can use a feature (within limits)
     */
    public function canUseFeature($schoolId, $featureKey, $requestedAmount = 1)
    {
        $usage = $this->getUsage($schoolId, $featureKey);

        if (!$usage) {
            return true; // No tracking = unlimited
        }

        if ($usage['usage_limit'] === null) {
            return true; // Unlimited
        }

        return ($usage['current_usage'] + $requestedAmount) <= $usage['usage_limit'];
    }

    /**
     * Increment usage for a feature
     */
    public function incrementUsage($schoolId, $featureKey, $amount = 1)
    {
        $usage = $this->getUsage($schoolId, $featureKey);

        if (!$usage) {
            // Create new usage tracker if doesn't exist
            return $this->createOrUpdateUsageTracker($schoolId, $featureKey, $amount);
        }

        // Check if reset is needed
        $this->checkAndResetUsage($usage);

        // Increment usage
        $newUsage = $usage['current_usage'] + $amount;
        
        return $this->update($usage['id'], [
            'current_usage' => $newUsage
        ]);
    }

    /**
     * Decrement usage for a feature (when something is deleted)
     */
    public function decrementUsage($schoolId, $featureKey, $amount = 1)
    {
        $usage = $this->getUsage($schoolId, $featureKey);

        if (!$usage) {
            return true; // Nothing to decrement
        }

        $newUsage = max(0, $usage['current_usage'] - $amount);
        
        return $this->update($usage['id'], [
            'current_usage' => $newUsage
        ]);
    }

    /**
     * Get current usage for a feature
     */
    public function getUsage($schoolId, $featureKey)
    {
        $usage = $this->where('school_id', $schoolId)
                     ->where('feature_key', $featureKey)
                     ->first();

        if ($usage) {
            // Check if reset is needed
            $this->checkAndResetUsage($usage);
            
            // Refresh data after potential reset
            $usage = $this->find($usage['id']);
        }

        return $usage;
    }

    /**
     * Get all usage for a school
     */
    public function getSchoolUsage($schoolId)
    {
        $usages = $this->where('school_id', $schoolId)->findAll();
        $result = [];

        foreach ($usages as $usage) {
            // Check if reset is needed
            $this->checkAndResetUsage($usage);
            
            $result[$usage['feature_key']] = [
                'current' => $usage['current_usage'],
                'limit' => $usage['usage_limit'],
                'unlimited' => $usage['usage_limit'] === null,
                'percentage' => $usage['usage_limit'] ? round(($usage['current_usage'] / $usage['usage_limit']) * 100, 2) : 0,
                'reset_period' => $usage['reset_period'],
                'last_reset' => $usage['last_reset_at']
            ];
        }

        return $result;
    }

    /**
     * Check if usage needs to be reset based on reset period
     */
    private function checkAndResetUsage($usage)
    {
        if ($usage['reset_period'] === 'never' || !$usage['last_reset_at']) {
            return false;
        }

        $lastReset = strtotime($usage['last_reset_at']);
        $now = time();
        $shouldReset = false;

        switch ($usage['reset_period']) {
            case 'daily':
                $shouldReset = date('Y-m-d', $lastReset) < date('Y-m-d', $now);
                break;
            case 'weekly':
                $shouldReset = date('Y-W', $lastReset) < date('Y-W', $now);
                break;
            case 'monthly':
                $shouldReset = date('Y-m', $lastReset) < date('Y-m', $now);
                break;
            case 'yearly':
                $shouldReset = date('Y', $lastReset) < date('Y', $now);
                break;
        }

        if ($shouldReset) {
            $this->update($usage['id'], [
                'current_usage' => 0,
                'last_reset_at' => date('Y-m-d H:i:s')
            ]);
            return true;
        }

        return false;
    }

    /**
     * Get usage statistics for admin dashboard
     */
    public function getUsageStatistics()
    {
        return $this->select('
                feature_key,
                COUNT(*) as schools_count,
                AVG(current_usage) as avg_usage,
                MAX(current_usage) as max_usage,
                SUM(CASE WHEN usage_limit IS NOT NULL AND current_usage >= usage_limit THEN 1 ELSE 0 END) as schools_at_limit
            ')
            ->groupBy('feature_key')
            ->findAll();
    }

    /**
     * Get schools approaching their limits
     */
    public function getSchoolsApproachingLimits($threshold = 80)
    {
        return $this->select('
                usage_tracker.*,
                schools.name as school_name,
                schools.email as school_email
            ')
            ->join('schools', 'schools.id = usage_tracker.school_id')
            ->where('usage_tracker.usage_limit IS NOT NULL')
            ->where('(usage_tracker.current_usage / usage_tracker.usage_limit * 100) >=', $threshold)
            ->findAll();
    }

    /**
     * Reset all usage for a specific period (for cron jobs)
     */
    public function resetUsageByPeriod($resetPeriod)
    {
        $conditions = [];
        $now = time();

        switch ($resetPeriod) {
            case 'daily':
                $conditions[] = "DATE(last_reset_at) < CURDATE()";
                break;
            case 'weekly':
                $conditions[] = "YEARWEEK(last_reset_at) < YEARWEEK(NOW())";
                break;
            case 'monthly':
                $conditions[] = "DATE_FORMAT(last_reset_at, '%Y-%m') < DATE_FORMAT(NOW(), '%Y-%m')";
                break;
            case 'yearly':
                $conditions[] = "YEAR(last_reset_at) < YEAR(NOW())";
                break;
        }

        if (!empty($conditions)) {
            return $this->where('reset_period', $resetPeriod)
                       ->where(implode(' OR ', $conditions))
                       ->set([
                           'current_usage' => 0,
                           'last_reset_at' => date('Y-m-d H:i:s')
                       ])
                       ->update();
        }

        return false;
    }
}
