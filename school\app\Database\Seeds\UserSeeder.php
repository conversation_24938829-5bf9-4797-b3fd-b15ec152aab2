<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class UserSeeder extends Seeder
{
    public function run()
    {
        // Get first active school
        $schoolModel = new \App\Models\SchoolModel();
        $school = $schoolModel->where('status', 'active')->first();

        if (!$school) {
            echo "No active school found! Please create a school first.\n";
            return;
        }

        echo "Creating users for school: " . $school['name'] . " (ID: " . $school['id'] . ")\n";

        // Test users data
        $users = [
            [
                'school_id' => $school['id'],
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'school_id' => $school['id'],
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'school_id' => $school['id'],
                'name' => 'Mike Davis',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'school_id' => $school['id'],
                'name' => 'Emily Wilson',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert users
        $this->db->table('users')->insertBatch($users);

        // Get inserted user IDs
        $insertedUsers = $this->db->table('users')
            ->whereIn('email', array_column($users, 'email'))
            ->get()
            ->getResultArray();

        // Create profiles
        $profiles = [
            [
                'user_id' => $insertedUsers[0]['id'],
                'school_id' => $school['id'],
                'designation' => 'Teacher',
                'phone' => '+1234567890',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'user_id' => $insertedUsers[1]['id'],
                'school_id' => $school['id'],
                'designation' => 'Principal',
                'phone' => '+1234567891',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'user_id' => $insertedUsers[2]['id'],
                'school_id' => $school['id'],
                'designation' => 'Vice Principal',
                'phone' => '+1234567892',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'user_id' => $insertedUsers[3]['id'],
                'school_id' => $school['id'],
                'designation' => 'Teacher',
                'phone' => '+1234567893',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        // Insert profiles
        $this->db->table('user_profiles')->insertBatch($profiles);

        echo "Created " . count($users) . " test users with profiles successfully!\n";
    }
}
