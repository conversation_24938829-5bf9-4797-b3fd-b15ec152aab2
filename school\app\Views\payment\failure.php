<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Failed - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .error-animation {
            animation: errorShake 0.5s ease-in-out;
        }
        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .pulse-red {
            animation: pulseRed 2s ease-in-out infinite;
        }
        @keyframes pulseRed {
            0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(239, 68, 68, 0); }
            100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-red-50 to-orange-50 min-h-screen">

    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            
            <!-- Error Card -->
            <div class="bg-white rounded-2xl shadow-2xl p-8 text-center relative overflow-hidden error-animation">
                
                <!-- Background Pattern -->
                <div class="absolute inset-0 bg-gradient-to-br from-red-400/10 to-orange-400/10"></div>
                
                <!-- Content -->
                <div class="relative z-10">
                    
                    <!-- Error Icon -->
                    <div class="pulse-red mx-auto w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-times text-3xl text-red-600"></i>
                    </div>

                    <!-- Error Message -->
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Failed</h1>
                    <p class="text-gray-600 mb-6">We couldn't process your payment. Please try again.</p>

                    <!-- Error Details -->
                    <?php if (isset($error_details)): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
                        <h3 class="font-semibold text-red-800 mb-3">Error Details</h3>
                        
                        <div class="space-y-2 text-sm">
                            <?php if (isset($error_details['transaction_id'])): ?>
                            <div class="flex justify-between">
                                <span class="text-red-600">Transaction ID:</span>
                                <span class="font-mono text-red-800"><?= $error_details['transaction_id'] ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (isset($error_details['reason'])): ?>
                            <div class="flex justify-between">
                                <span class="text-red-600">Reason:</span>
                                <span class="font-medium text-red-800"><?= $error_details['reason'] ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <?php if (isset($error_details['gateway'])): ?>
                            <div class="flex justify-between">
                                <span class="text-red-600">Gateway:</span>
                                <span class="font-medium text-red-800 capitalize"><?= $error_details['gateway'] ?></span>
                            </div>
                            <?php endif; ?>
                            
                            <div class="flex justify-between">
                                <span class="text-red-600">Time:</span>
                                <span class="font-medium text-red-800"><?= date('M d, Y H:i') ?></span>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Common Reasons -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6 text-left">
                        <h3 class="font-semibold text-yellow-800 mb-3">Common Reasons for Payment Failure</h3>
                        
                        <ul class="text-sm text-yellow-700 space-y-1">
                            <li><i class="fas fa-circle text-xs mr-2"></i>Insufficient funds in account</li>
                            <li><i class="fas fa-circle text-xs mr-2"></i>Card expired or blocked</li>
                            <li><i class="fas fa-circle text-xs mr-2"></i>Incorrect card details</li>
                            <li><i class="fas fa-circle text-xs mr-2"></i>Network connectivity issues</li>
                            <li><i class="fas fa-circle text-xs mr-2"></i>Bank server temporarily down</li>
                            <li><i class="fas fa-circle text-xs mr-2"></i>Transaction limit exceeded</li>
                        </ul>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <a href="<?= base_url('payment/process') . (isset($_GET['plan_id']) ? '?plan_id=' . $_GET['plan_id'] : '') ?>" 
                           class="w-full bg-red-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-red-700 transition duration-300 inline-block">
                            <i class="fas fa-redo mr-2"></i>Try Again
                        </a>
                        
                        <a href="<?= base_url('subscription/plans') ?>" 
                           class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition duration-300 inline-block">
                            <i class="fas fa-list mr-2"></i>Choose Different Plan
                        </a>
                        
                        <a href="<?= base_url('schooladmin/dashboard') ?>" 
                           class="w-full border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition duration-300 inline-block">
                            <i class="fas fa-home mr-2"></i>Back to Dashboard
                        </a>
                    </div>

                    <!-- Help Section -->
                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <h3 class="font-semibold text-gray-800 mb-3">Need Help?</h3>
                        
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <a href="mailto:<EMAIL>" 
                               class="bg-blue-50 text-blue-700 py-2 px-3 rounded-lg hover:bg-blue-100 transition duration-300">
                                <i class="fas fa-envelope mr-1"></i>Email Support
                            </a>
                            
                            <a href="tel:+91-XXXXXXXXXX" 
                               class="bg-green-50 text-green-700 py-2 px-3 rounded-lg hover:bg-green-100 transition duration-300">
                                <i class="fas fa-phone mr-1"></i>Call Support
                            </a>
                        </div>
                        
                        <div class="mt-3">
                            <a href="<?= base_url('help/payment-issues') ?>" 
                               class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-question-circle mr-1"></i>Payment Troubleshooting Guide
                            </a>
                        </div>
                    </div>

                    <!-- Alternative Payment Methods -->
                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <p class="text-sm text-gray-600 mb-3">Try a different payment method:</p>
                        
                        <div class="flex justify-center space-x-4">
                            <div class="text-center">
                                <i class="fas fa-credit-card text-2xl text-gray-400 mb-1"></i>
                                <p class="text-xs text-gray-500">Cards</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-university text-2xl text-gray-400 mb-1"></i>
                                <p class="text-xs text-gray-500">Net Banking</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-mobile-alt text-2xl text-gray-400 mb-1"></i>
                                <p class="text-xs text-gray-500">UPI</p>
                            </div>
                            <div class="text-center">
                                <i class="fas fa-wallet text-2xl text-gray-400 mb-1"></i>
                                <p class="text-xs text-gray-500">Wallets</p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Security Note -->
            <div class="text-center text-sm text-gray-500">
                <p><i class="fas fa-shield-alt mr-1"></i>Your payment information is secure and was not stored.</p>
            </div>

        </div>
    </div>

    <script>
        // Auto-redirect to plans after 60 seconds
        setTimeout(function() {
            if (confirm('Would you like to try selecting a plan again?')) {
                window.location.href = '<?= base_url('subscription/plans') ?>';
            }
        }, 60000);

        // Track retry attempts
        let retryCount = localStorage.getItem('payment_retry_count') || 0;
        retryCount++;
        localStorage.setItem('payment_retry_count', retryCount);

        // Show additional help after multiple failures
        if (retryCount >= 3) {
            setTimeout(function() {
                document.querySelector('.help-section').classList.add('bg-yellow-50', 'border', 'border-yellow-200');
                document.querySelector('.help-section h3').textContent = 'Multiple Payment Failures Detected - We\'re Here to Help!';
            }, 2000);
        }
    </script>
</body>
</html>
