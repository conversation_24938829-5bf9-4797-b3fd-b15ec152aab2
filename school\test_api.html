<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>Test User Management API</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
        .user-item { padding: 10px; margin: 5px 0; border: 1px solid #ccc; border-radius: 3px; }
        .user-actions button { padding: 4px 8px; margin: 2px; font-size: 12px; }
        .active { background-color: #d4edda; }
        .inactive { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 User Management API Test</h1>
        
        <div class="test-section">
            <h3>1. Load Users</h3>
            <button onclick="loadUsers()">Load Users</button>
            <div id="users-result" class="result"></div>
            <div id="users-list"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Test User Actions</h3>
            <p>First load users above, then use the toggle/delete buttons on each user.</p>
        </div>
        
        <div class="test-section">
            <h3>3. Manual Tests</h3>
            <div>
                <label>User ID: <input type="number" id="manual-user-id" placeholder="Enter user ID"></label>
                <button onclick="toggleUserStatus()">Toggle Status</button>
                <button onclick="deleteUser()">Delete User</button>
                <button onclick="getUserDetails()">Get Details</button>
            </div>
            <div id="manual-result" class="result"></div>
        </div>
    </div>

    <script>
        // Base URL for API calls
        const baseUrl = 'http://localhost:3000/m-dev.php/superadmin';
        
        // Show notification
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.innerHTML = message;
        }
        
        // Load users
        async function loadUsers() {
            try {
                showResult('users-result', 'Loading users...', true);
                
                const response = await fetch(`${baseUrl}/getUsers`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': 'test-token'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult('users-result', `✅ Loaded ${data.users.length} users successfully!`, true);
                    displayUsers(data.users);
                } else {
                    showResult('users-result', `❌ Failed to load users: ${data.message}`, false);
                }
            } catch (error) {
                showResult('users-result', `❌ Error: ${error.message}`, false);
            }
        }
        
        // Display users
        function displayUsers(users) {
            const container = document.getElementById('users-list');
            
            if (users.length === 0) {
                container.innerHTML = '<p>No users found.</p>';
                return;
            }
            
            const usersHTML = users.map(user => {
                const statusClass = user.status === 'active' ? 'active' : 'inactive';
                return `
                    <div class="user-item ${statusClass}">
                        <strong>${user.name}</strong> (ID: ${user.id})<br>
                        📧 ${user.email}<br>
                        🏫 ${user.school_name || 'No School'}<br>
                        📊 Status: <strong>${user.status}</strong>
                        ${user.designation ? `<br>👔 ${user.designation}` : ''}
                        <div class="user-actions">
                            <button onclick="toggleUserStatusById(${user.id})">
                                Toggle Status (${user.status === 'active' ? 'Deactivate' : 'Activate'})
                            </button>
                            <button onclick="deleteUserById(${user.id})" style="background-color: #dc3545; color: white;">
                                Delete User
                            </button>
                            <button onclick="getUserDetailsById(${user.id})">
                                View Details
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
            
            container.innerHTML = usersHTML;
        }
        
        // Toggle user status by ID
        async function toggleUserStatusById(userId) {
            if (!confirm(`Are you sure you want to toggle status for user ID ${userId}?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${baseUrl}/toggleUserStatus/${userId}`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': 'test-token'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`✅ ${data.message}`);
                    loadUsers(); // Reload users
                } else {
                    alert(`❌ ${data.message}`);
                }
            } catch (error) {
                alert(`❌ Error: ${error.message}`);
            }
        }
        
        // Delete user by ID
        async function deleteUserById(userId) {
            if (!confirm(`Are you sure you want to DELETE user ID ${userId}? This action cannot be undone.`)) {
                return;
            }
            
            try {
                const response = await fetch(`${baseUrl}/deleteUser/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': 'test-token'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert(`✅ ${data.message}`);
                    loadUsers(); // Reload users
                } else {
                    alert(`❌ ${data.message}`);
                }
            } catch (error) {
                alert(`❌ Error: ${error.message}`);
            }
        }
        
        // Get user details by ID
        async function getUserDetailsById(userId) {
            try {
                const response = await fetch(`${baseUrl}/getUserDetails/${userId}`, {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': 'test-token'
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const user = data.user;
                    alert(`👤 User Details:\n\nName: ${user.name}\nEmail: ${user.email}\nStatus: ${user.status}\nSchool: ${user.school_name || 'No School'}\nDesignation: ${user.designation || 'Not specified'}\nPhone: ${user.phone || 'Not specified'}\nCreated: ${user.created_at}`);
                } else {
                    alert(`❌ ${data.message}`);
                }
            } catch (error) {
                alert(`❌ Error: ${error.message}`);
            }
        }
        
        // Manual toggle status
        function toggleUserStatus() {
            const userId = document.getElementById('manual-user-id').value;
            if (!userId) {
                showResult('manual-result', '❌ Please enter a user ID', false);
                return;
            }
            toggleUserStatusById(parseInt(userId));
        }
        
        // Manual delete user
        function deleteUser() {
            const userId = document.getElementById('manual-user-id').value;
            if (!userId) {
                showResult('manual-result', '❌ Please enter a user ID', false);
                return;
            }
            deleteUserById(parseInt(userId));
        }
        
        // Manual get user details
        function getUserDetails() {
            const userId = document.getElementById('manual-user-id').value;
            if (!userId) {
                showResult('manual-result', '❌ Please enter a user ID', false);
                return;
            }
            getUserDetailsById(parseInt(userId));
        }
        
        // Auto-load users when page loads
        window.addEventListener('load', function() {
            console.log('🚀 User Management API Test loaded');
            console.log('📍 Base URL:', baseUrl);
        });
    </script>
</body>
</html>
