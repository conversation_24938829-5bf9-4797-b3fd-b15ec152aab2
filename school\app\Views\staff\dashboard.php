<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= esc($school['name']) ?> - Staff Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar-active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-right: 4px solid #667eea;
        }
        .sidebar-active i {
            color: white;
        }

        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* Layout System */
        body {
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        .layout-container {
            display: flex;
            min-height: 100vh;
        }

        #sidebar {
            width: 16rem;
            flex-shrink: 0;
            position: fixed;
            height: 100vh;
            z-index: 40;
            transition: transform 0.3s ease-in-out;
        }

        #main-content {
            flex: 1;
            margin-left: 16rem;
            min-height: 100vh;
            width: calc(100% - 16rem);
            overflow-x: auto;
            transition: margin-left 0.3s ease-in-out;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            #main-content {
                margin-left: 0;
                width: 100%;
            }
            #sidebar {
                transform: translateX(-100%);
                z-index: 50;
            }
            #sidebar.show {
                transform: translateX(0);
            }
        }

        .mobile-backdrop {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 40;
        }

        @media (max-width: 768px) {
            .mobile-backdrop.show {
                display: block;
            }
        }

        .section {
            max-width: 100%;
            overflow-x: auto;
        }

        .section.hidden {
            display: none;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Mobile Backdrop -->
    <div id="mobile-backdrop" class="mobile-backdrop" onclick="toggleSidebar()"></div>

    <div class="layout-container">
        <!-- Sidebar -->
        <div id="sidebar" class="bg-white shadow-lg sidebar-transition">
            <div class="p-6 border-b">
                <div class="flex items-center">
                    <i class="fas fa-user-tie text-2xl text-green-600 mr-3"></i>
                    <div>
                        <h2 class="font-bold text-gray-800"><?= esc($school['name']) ?></h2>
                        <p class="text-sm text-gray-600">Staff Portal</p>
                    </div>
                </div>
            </div>

            <nav class="mt-6">
                <a href="#" onclick="showSection('dashboard')"
                    class="nav-item active sidebar-active flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-200">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a href="#" onclick="showSection('questions')"
                    class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-200">
                    <i class="fas fa-question-circle mr-3"></i>
                    My Questions
                </a>
                <a href="#" onclick="showSection('create-question')"
                    class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-200">
                    <i class="fas fa-plus-circle mr-3"></i>
                    Create Question
                </a>
                <a href="#" onclick="showSection('question-papers')"
                    class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-200">
                    <i class="fas fa-file-alt mr-3"></i>
                    Question Papers
                </a>
                <a href="#" onclick="showSection('subjects')"
                    class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-200">
                    <i class="fas fa-book mr-3"></i>
                    Subjects
                </a>
                <a href="#" onclick="showSection('reports')"
                    class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-200">
                    <i class="fas fa-chart-bar mr-3"></i>
                    Reports
                </a>
                <a href="#" onclick="showSection('profile')"
                    class="nav-item flex items-center px-6 py-3 text-gray-700 hover:bg-green-50 hover:text-green-600 transition duration-200">
                    <i class="fas fa-user mr-3"></i>
                    Profile
                </a>
            </nav>

            <div class="absolute bottom-0 w-full p-6 border-t">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center text-white font-semibold">
                        <?= strtoupper(substr($user['name'], 0, 2)) ?>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-gray-800"><?= esc($user['name']) ?></p>
                        <p class="text-xs text-gray-600">Staff Member</p>
                    </div>
                </div>
                <a href="<?= site_url('staff/logout') ?>" class="w-full bg-red-600 text-white py-2 rounded-lg hover:bg-red-700 transition duration-200 inline-block text-center">
                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div id="main-content">
            <!-- Top Header -->
            <header class="bg-white shadow-sm border-b px-6 py-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <!-- Mobile Menu Toggle -->
                        <button id="mobile-menu-toggle" class="md:hidden p-2 text-gray-600 hover:text-gray-900 mr-3" onclick="toggleSidebar()">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-800" id="pageTitle">Staff Dashboard</h1>
                            <p class="text-sm text-gray-600 mt-1">Welcome back, <?= esc($user['name']) ?>!</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <button id="notification-btn" class="relative p-2 text-gray-600 hover:text-gray-900" aria-label="Notifications" onclick="toggleNotifications()">
                                <i class="fas fa-bell text-xl"></i>
                                <span id="notification-count" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" style="display: none;">0</span>
                            </button>

                            <!-- Notifications Dropdown -->
                            <div id="notifications-dropdown" class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50" style="display: none;">
                                <div class="p-4 border-b border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-800">Notifications</h3>
                                </div>
                                <div id="notifications-list" class="max-h-96 overflow-y-auto">
                                    <div class="p-4 text-center text-gray-500">
                                        <i class="fas fa-bell-slash text-2xl mb-2"></i>
                                        <p>No new notifications</p>
                                    </div>
                                </div>
                                <div class="p-3 border-t border-gray-200 text-center">
                                    <button onclick="markAllAsRead()" class="text-blue-600 hover:text-blue-800 text-sm">Mark all as read</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Content -->
            <main class="p-6">
                <!-- Dashboard Section -->
                <div id="dashboard-section" class="section">
                    <!-- Stats Cards -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Questions Created</p>
                                    <p class="text-3xl font-bold text-gray-900"><?= $stats['total_questions'] ?? $stats['questions_created'] ?? 0 ?></p>
                                    <p class="text-sm text-blue-600 mt-1">
                                        <i class="fas fa-arrow-up mr-1"></i>Questions created
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-question-circle text-blue-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Pending Reviews</p>
                                    <p class="text-3xl font-bold text-gray-900"><?= $stats['pending_reviews'] ?? 0 ?></p>
                                    <p class="text-sm text-yellow-600 mt-1">
                                        <i class="fas fa-clock mr-1"></i>Awaiting approval
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Approved Questions</p>
                                    <p class="text-3xl font-bold text-gray-900"><?= $stats['approved_questions'] ?? 0 ?></p>
                                    <p class="text-sm text-green-600 mt-1">
                                        <i class="fas fa-check mr-1"></i>Ready for use
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-600">Total Subjects</p>
                                    <p class="text-3xl font-bold text-gray-900"><?= $stats['total_subjects'] ?? 0 ?></p>
                                    <p class="text-sm text-purple-600 mt-1">
                                        <i class="fas fa-book mr-1"></i>Subjects assigned
                                    </p>
                                </div>
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-book text-purple-600 text-xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Recent Activities</h3>
                            <button onclick="loadRecentActivities()" class="text-blue-600 hover:text-blue-800 text-sm">
                                <i class="fas fa-refresh mr-1"></i>Refresh
                            </button>
                        </div>
                        <div id="recent-activities-list">
                            <div class="text-center py-4 text-gray-500">
                                <i class="fas fa-spinner fa-spin mr-2"></i>Loading activities...
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-plus-circle text-green-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">Create Question</h4>
                                <p class="text-sm text-gray-600 mb-4">Add new questions following Tamil Nadu SSLC/HSC format</p>
                                <button onclick="showSection('create-question')" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-200">
                                    Start Creating
                                </button>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-list text-blue-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">My Questions</h4>
                                <p class="text-sm text-gray-600 mb-4">View and manage your created questions</p>
                                <button onclick="showSection('questions')" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                    View Questions
                                </button>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-file-alt text-orange-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">Question Papers</h4>
                                <p class="text-sm text-gray-600 mb-4">Create and manage question papers</p>
                                <button onclick="showSection('question-papers')" class="w-full bg-orange-600 text-white py-2 rounded-lg hover:bg-orange-700 transition duration-200">
                                    Manage Papers
                                </button>
                            </div>
                        </div>
                        <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-chart-bar text-purple-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-800 mb-2">View Reports</h4>
                                <p class="text-sm text-gray-600 mb-4">Check your question creation statistics</p>
                                <button onclick="showSection('reports')" class="w-full bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition duration-200">
                                    View Reports
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Question Section (Hidden by default) -->
                <div id="create-question-section" class="section hidden">
                    <!-- Create Question Form -->
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200 mb-8">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">Create New Question - Tamil Nadu SSLC/HSC Format</h3>
                            <button onclick="showSection('dashboard')" class="text-gray-600 hover:text-gray-800">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <form class="space-y-6" id="questionForm">
                            <!-- Standard and Subject Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Standard *</label>
                                    <select id="standard" name="standard" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                                        <option value="">Select Standard</option>
                                        <option value="1">Standard 1</option>
                                        <option value="2">Standard 2</option>
                                        <option value="3">Standard 3</option>
                                        <option value="4">Standard 4</option>
                                        <option value="5">Standard 5</option>
                                        <option value="6">Standard 6</option>
                                        <option value="7">Standard 7</option>
                                        <option value="8">Standard 8</option>
                                        <option value="9">Standard 9</option>
                                        <option value="10">Standard 10 (SSLC)</option>
                                        <option value="11">Standard 11 (HSC)</option>
                                        <option value="12">Standard 12 (HSC)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                                    <div class="space-y-2">
                                        <select id="subject" name="subject" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" onchange="toggleCustomSubject()" required>
                                            <option value="">Select Subject</option>
                                            <!-- Options will be populated based on standard selection -->
                                            <option value="custom">Other (Enter Custom Subject)</option>
                                        </select>
                                        <input type="text" id="custom_subject" name="custom_subject" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 hidden" placeholder="Enter custom subject name">
                                    </div>
                                    <button type="button" onclick="testSubjects()" class="mt-1 text-xs text-blue-600">Test Subjects</button>
                                </div>
                            </div>

                            <!-- Chapter and Topic Selection -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Chapter</label>
                                    <div class="space-y-2">
                                        <select id="chapter" name="chapter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" onchange="toggleCustomChapter()">
                                            <option value="">Select Chapter</option>
                                            <!-- Options will be populated based on subject selection -->
                                            <option value="custom">Other (Enter Custom Chapter)</option>
                                        </select>
                                        <input type="text" id="custom_chapter" name="custom_chapter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 hidden" placeholder="Enter custom chapter name">
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1">Select from dropdown or enter custom chapter</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Chapter Name</label>
                                    <input type="text" id="chapter_name" name="chapter_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Enter chapter name">
                                    <p class="text-xs text-gray-500 mt-1">Display name for the chapter</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Topic Name</label>
                                    <input type="text" id="topic_name" name="topic_name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Enter topic name">
                                    <p class="text-xs text-gray-500 mt-1">Specific topic within the chapter</p>
                                </div>
                            </div>

                            <!-- Question Type and Difficulty -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Question Type *</label>
                                    <select id="question_type" name="question_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                                        <option value="">Select Type</option>
                                        <option value="multiple_choice">Multiple Choice (MCQ)</option>
                                        <option value="short_answer">Short Answer (2-3 marks)</option>
                                        <option value="long_answer">Long Answer (5-8 marks)</option>
                                        <option value="essay">Essay Type (10+ marks)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level *</label>
                                    <select id="difficulty" name="difficulty" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" required>
                                        <option value="">Select Difficulty</option>
                                        <option value="easy">Easy</option>
                                        <option value="medium">Medium</option>
                                        <option value="hard">Hard</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Marks *</label>
                                    <input type="number" id="marks" name="marks" min="1" max="20" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Enter marks" required>
                                </div>
                            </div>

                            <!-- Question Text -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Question Text *</label>
                                <textarea id="question_text" name="question_text" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Enter the question text following Tamil Nadu board format..." required></textarea>
                                <p class="text-xs text-gray-500 mt-1">Follow Tamil Nadu SSLC/HSC question paper format and language standards</p>
                            </div>

                            <!-- Multiple Choice Options (shown only for MCQ) -->
                            <div id="mcq-options" class="hidden">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Answer Choices</label>
                                <div class="space-y-3">
                                    <div class="flex items-center space-x-3">
                                        <span class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">A</span>
                                        <input type="text" name="option_a" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Option A">
                                        <input type="radio" name="correct_answer" value="A" class="text-green-600">
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <span class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">B</span>
                                        <input type="text" name="option_b" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Option B">
                                        <input type="radio" name="correct_answer" value="B" class="text-green-600">
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <span class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">C</span>
                                        <input type="text" name="option_c" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Option C">
                                        <input type="radio" name="correct_answer" value="C" class="text-green-600">
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <span class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-sm font-medium">D</span>
                                        <input type="text" name="option_d" class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Option D">
                                        <input type="radio" name="correct_answer" value="D" class="text-green-600">
                                    </div>
                                </div>
                                <p class="text-xs text-gray-500 mt-2">Select the correct answer by clicking the radio button</p>
                            </div>

                            <!-- Answer/Solution (for non-MCQ questions) -->
                            <div id="answer-section">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Model Answer/Solution *</label>
                                <textarea id="answer" name="answer" rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" placeholder="Provide the complete solution or model answer..." required></textarea>
                                <p class="text-xs text-gray-500 mt-1">Include step-by-step solution for better understanding</p>
                            </div>

                            <!-- Form Actions -->
                            <div class="flex justify-end space-x-4 pt-6 border-t">
                                <button type="button" onclick="cancelEdit()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-200">
                                    Cancel
                                </button>
                                <button type="button" onclick="saveAsDraft()" class="px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition duration-200">
                                    Save as Draft
                                </button>
                                <button type="submit" class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                                    Submit for Review
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- My Questions Section -->
                <div id="questions-section" class="section hidden">
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">My Questions</h3>
                            <button onclick="loadQuestions()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                        </div>

                        <!-- Filters -->
                        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                    <select id="filter-status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="applyFilters()">
                                        <option value="">All Status</option>
                                        <option value="draft">Draft</option>
                                        <option value="pending">Pending Review</option>
                                        <option value="approved">Approved</option>
                                        <option value="rejected">Rejected</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                                    <select id="filter-subject" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="applyFilters()">
                                        <option value="">All Subjects</option>
                                        <!-- Will be populated dynamically -->
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Chapter</label>
                                    <select id="filter-chapter" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="applyFilters()">
                                        <option value="">All Chapters</option>
                                        <!-- Will be populated dynamically -->
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Question Type</label>
                                    <select id="filter-type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onchange="applyFilters()">
                                        <option value="">All Types</option>
                                        <option value="multiple_choice">Multiple Choice</option>
                                        <option value="short_answer">Short Answer</option>
                                        <option value="long_answer">Long Answer</option>
                                        <option value="essay">Essay</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Topic</label>
                                    <input type="text" id="filter-topic" placeholder="Filter by topic..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onkeyup="applyFilters()">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                                    <input type="text" id="filter-search" placeholder="Search questions..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500" onkeyup="applyFilters()">
                                </div>
                            </div>
                            <div class="mt-4 flex items-center justify-between">
                                <div class="text-sm text-gray-600">
                                    <span id="filter-results-count">0</span> questions found
                                    <span id="filter-active-indicators" class="ml-2"></span>
                                </div>
                                <button onclick="clearFilters()" class="text-blue-600 hover:text-blue-800 text-sm">
                                    <i class="fas fa-times mr-1"></i>Clear Filters
                                </button>
                            </div>
                        </div>

                        <!-- Questions Table -->
                        <div class="overflow-x-auto">
                            <table class="w-full text-sm text-left">
                                <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3">Question</th>
                                        <th class="px-6 py-3">Subject</th>
                                        <th class="px-6 py-3">Chapter</th>
                                        <th class="px-6 py-3">Topic</th>
                                        <th class="px-6 py-3">Standard</th>
                                        <th class="px-6 py-3">Type</th>
                                        <th class="px-6 py-3">Marks</th>
                                        <th class="px-6 py-3">Status</th>
                                        <th class="px-6 py-3">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="questions-table-body">
                                    <!-- Questions will be loaded dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Subjects Section -->
                <div id="subjects-section" class="section hidden">
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">My Subjects</h3>
                            <button onclick="loadSubjectsWithCounts()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                        </div>

                        <div id="subjects-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div class="col-span-full text-center py-8 text-gray-500">
                                <i class="fas fa-spinner fa-spin mr-2"></i>Loading subjects...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reports Section -->
                <div id="reports-section" class="section hidden">
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-lg font-semibold text-gray-800">Question Creation Reports</h3>
                            <button onclick="loadDetailedReports()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                        </div>

                        <div id="reports-content">
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-spinner fa-spin mr-2"></i>Loading reports...
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Papers Section -->
                <div id="question-papers-section" class="section hidden">
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-800">Question Papers</h3>
                                    <p class="text-sm text-gray-600">Create and manage question papers using your approved questions</p>
                                </div>
                                <button onclick="showCreateQuestionPaperModal()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition duration-200">
                                    <i class="fas fa-plus mr-2"></i>Create Question Paper
                                </button>
                            </div>
                        </div>

                        <!-- Question Papers List -->
                        <div class="p-6">
                            <div id="question-papers-loading" class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-gray-400 text-2xl mb-2"></i>
                                <p class="text-gray-500">Loading question papers...</p>
                            </div>

                            <div id="question-papers-content" class="hidden">
                                <!-- Filter and Search -->
                                <div class="mb-6 flex flex-col sm:flex-row gap-4">
                                    <div class="flex-1">
                                        <input type="text" id="paper-search" placeholder="Search question papers..."
                                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    </div>
                                    <div class="flex gap-2">
                                        <select id="paper-status-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500">
                                            <option value="">All Status</option>
                                            <option value="draft">Draft</option>
                                            <option value="published">Published</option>
                                        </select>
                                        <select id="paper-subject-filter" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500">
                                            <option value="">All Subjects</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Papers Grid -->
                                <div id="question-papers-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    <!-- Papers will be loaded here -->
                                </div>

                                <!-- Empty State -->
                                <div id="no-papers-message" class="text-center py-12 hidden">
                                    <i class="fas fa-file-alt text-gray-300 text-6xl mb-4"></i>
                                    <h4 class="text-lg font-medium text-gray-600 mb-2">No Question Papers Yet</h4>
                                    <p class="text-gray-500 mb-6">Create your first question paper to get started</p>
                                    <button onclick="showCreateQuestionPaperModal()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition duration-200">
                                        <i class="fas fa-plus mr-2"></i>Create Question Paper
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Section -->
                <div id="profile-section" class="section hidden">
                    <div class="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-6">My Profile</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                                <input type="text" value="<?= esc($user['name']) ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                                <input type="email" value="<?= esc($user['email']) ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Employee ID</label>
                                <input type="text" value="<?= esc($user['employee_id'] ?? 'EMP001') ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" readonly>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                                <input type="text" value="<?= esc($user['department'] ?? 'Academic') ?>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500" readonly>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Question View Modal -->
    <div id="questionViewModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Question Details</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="hideQuestionViewModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                        <p id="view-subject" class="text-gray-800 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Standard</label>
                        <p id="view-standard" class="text-gray-800 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Chapter</label>
                        <p id="view-chapter" class="text-gray-800 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Topic</label>
                        <p id="view-topic" class="text-gray-800 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Type</label>
                        <p id="view-type" class="text-gray-800 bg-gray-50 p-2 rounded">-</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Marks</label>
                        <p id="view-marks" class="text-gray-800 bg-gray-50 p-2 rounded">-</p>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Question</label>
                    <div id="view-question-text" class="text-gray-800 bg-gray-50 p-4 rounded min-h-[80px]">-</div>
                </div>

                <div id="view-options" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Options</label>
                    <div id="view-options-list" class="space-y-2">
                        <!-- Options will be populated -->
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Answer/Solution</label>
                    <div id="view-answer" class="text-gray-800 bg-gray-50 p-4 rounded min-h-[80px]">-</div>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <div id="view-status">-</div>
                </div>

                <div id="view-feedback" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Admin Feedback</label>
                    <div id="view-feedback-text" class="text-gray-800 bg-yellow-50 p-4 rounded border-l-4 border-yellow-400">-</div>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-end">
                <button class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700" onclick="hideQuestionViewModal()">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Subject Questions Modal -->
    <div id="subjectQuestionsModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 id="subject-modal-title" class="text-lg font-semibold text-gray-800">Subject Questions</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="hideSubjectQuestionsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <!-- Subject Statistics -->
                <div id="subject-stats" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <!-- Stats will be populated -->
                </div>

                <!-- Questions Table -->
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                            <tr>
                                <th class="px-6 py-3">Question</th>
                                <th class="px-6 py-3">Chapter</th>
                                <th class="px-6 py-3">Topic</th>
                                <th class="px-6 py-3">Standard</th>
                                <th class="px-6 py-3">Type</th>
                                <th class="px-6 py-3">Marks</th>
                                <th class="px-6 py-3">Status</th>
                                <th class="px-6 py-3">Created</th>
                                <th class="px-6 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="subject-questions-table">
                            <tr>
                                <td colspan="9" class="text-center py-4">Loading questions...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex justify-between">
                <button onclick="showSection('create-question')" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    <i class="fas fa-plus mr-2"></i>Create New Question
                </button>
                <button class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700" onclick="hideSubjectQuestionsModal()">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Template Selection Modal -->
    <div id="templateSelectionModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800">Create Question Paper</h3>
                    <p class="text-sm text-gray-600">Choose a template to get started</p>
                </div>
                <button class="text-gray-500 hover:text-gray-700" onclick="hideTemplateSelectionModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Tamil Nadu SSLC Template -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-indigo-400 hover:bg-indigo-50 transition-all cursor-pointer template-card" onclick="selectTemplate('tn-sslc')">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-graduation-cap text-indigo-600 text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">Tamil Nadu SSLC</h4>
                            <p class="text-sm text-gray-600 mb-4">Official TN Board Class 10 format</p>
                            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                <span><i class="fas fa-clock mr-1"></i>2.5 Hours</span>
                                <span><i class="fas fa-star mr-1"></i>100 Marks</span>
                            </div>
                            <button class="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                                Use SSLC Template
                            </button>
                        </div>
                    </div>

                    <!-- Tamil Nadu HSC 1 Template -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-green-400 hover:bg-green-50 transition-all cursor-pointer template-card" onclick="selectTemplate('tn-hsc1')">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-university text-green-600 text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">Tamil Nadu HSC 1</h4>
                            <p class="text-sm text-gray-600 mb-4">Plus One (Standard 11) format</p>
                            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                <span><i class="fas fa-clock mr-1"></i>3 Hours</span>
                                <span><i class="fas fa-star mr-1"></i>100 Marks</span>
                            </div>
                            <button class="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                Use HSC 1 Template
                            </button>
                        </div>
                    </div>

                    <!-- Tamil Nadu HSC 2 Template -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-emerald-400 hover:bg-emerald-50 transition-all cursor-pointer template-card" onclick="selectTemplate('tn-hsc2')">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-university text-emerald-600 text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">Tamil Nadu HSC 2</h4>
                            <p class="text-sm text-gray-600 mb-4">Plus Two (Standard 12) format</p>
                            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                <span><i class="fas fa-clock mr-1"></i>3 Hours</span>
                                <span><i class="fas fa-star mr-1"></i>100 Marks</span>
                            </div>
                            <button class="w-full bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors">
                                Use HSC 2 Template
                            </button>
                        </div>
                    </div>

                    <!-- Custom Template -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-purple-400 hover:bg-purple-50 transition-all cursor-pointer template-card" onclick="selectTemplate('custom')">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cogs text-purple-600 text-2xl"></i>
                            </div>
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">Custom Template</h4>
                            <p class="text-sm text-gray-600 mb-4">Design your own format</p>
                            <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                <span><i class="fas fa-clock mr-1"></i>Flexible</span>
                                <span><i class="fas fa-star mr-1"></i>Custom</span>
                            </div>
                            <button class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                                Create Custom
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
// Dynamic subject loading from server

// Navigation functionality
function showSection(sectionName) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
        section.classList.add('hidden');
    });

    // Show selected section
    const targetSection = document.getElementById(sectionName + '-section');
    if (targetSection) {
        targetSection.classList.remove('hidden');
    }

    // Update navigation active state
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active', 'sidebar-active');
    });

    // Find and activate the clicked nav item
    const clickedItem = event?.target?.closest('.nav-item') ||
                       document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (clickedItem) {
        clickedItem.classList.add('active', 'sidebar-active');
    }

    // Update page title
    const titles = {
        'dashboard': 'Staff Dashboard',
        'questions': 'My Questions',
        'create-question': 'Create Question',
        'question-papers': 'Question Papers',
        'subjects': 'Subjects',
        'reports': 'Reports',
        'profile': 'Profile'
    };

    const pageTitle = document.getElementById('pageTitle');
    if (pageTitle) {
        pageTitle.textContent = titles[sectionName] || 'Staff Dashboard';
    }

    // Load data for specific sections
    if (sectionName === 'dashboard') {
        setTimeout(() => {
            loadRecentActivities();
        }, 100);
    } else if (sectionName === 'questions') {
        loadQuestions();
    } else if (sectionName === 'create-question') {
        // Initialize form when create question section is shown
        setTimeout(() => {
            initializeCreateQuestionForm();
        }, 100);
    } else if (sectionName === 'subjects') {
        setTimeout(() => {
            loadSubjectsWithCounts();
        }, 100);
    } else if (sectionName === 'question-papers') {
        setTimeout(() => {
            loadQuestionPapers();
        }, 100);
    } else if (sectionName === 'reports') {
        setTimeout(() => {
            loadDetailedReports();
        }, 100);
    }
}

// Sidebar toggle for mobile
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const backdrop = document.getElementById('mobile-backdrop');
    sidebar.classList.toggle('show');
    backdrop.classList.toggle('show');
}

// Subject data fallback (Tamil Nadu curriculum)
const subjectsByStandard = {
    '1': ['Tamil', 'English', 'Mathematics', 'Environmental Science'],
    '2': ['Tamil', 'English', 'Mathematics', 'Environmental Science'],
    '3': ['Tamil', 'English', 'Mathematics', 'Environmental Science'],
    '4': ['Tamil', 'English', 'Mathematics', 'Environmental Science'],
    '5': ['Tamil', 'English', 'Mathematics', 'Environmental Science'],
    '6': ['Tamil', 'English', 'Mathematics', 'Science', 'Social Science'],
    '7': ['Tamil', 'English', 'Mathematics', 'Science', 'Social Science'],
    '8': ['Tamil', 'English', 'Mathematics', 'Science', 'Social Science'],
    '9': ['Tamil', 'English', 'Mathematics', 'Science', 'Social Science'],
    '10': ['Tamil', 'English', 'Mathematics', 'Science', 'Social Science'],
    '11': ['Tamil', 'English', 'Mathematics', 'Physics', 'Chemistry', 'Biology', 'Computer Science', 'Accountancy', 'Business Studies', 'Economics'],
    '12': ['Tamil', 'English', 'Mathematics', 'Physics', 'Chemistry', 'Biology', 'Computer Science', 'Accountancy', 'Business Studies', 'Economics']
};

// Chapter data by subject (Tamil Nadu curriculum)
const chaptersBySubject = {
    'Tamil': {
        '1': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '2': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '3': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '4': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '5': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '6': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '7': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '8': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '9': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '10': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'English': {
        '1': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '2': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '3': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '4': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '5': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '6': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '7': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '8': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '9': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '10': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Mathematics': {
        '1': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '2': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '3': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '4': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '5': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '6': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '7': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '8': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '9': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '10': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Science': {
        '6': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '7': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '8': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '9': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '10': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Social Science': {
        '6': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '7': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '8': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '9': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '10': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Physics': {
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Chemistry': {
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Biology': {
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Computer Science': {
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Accountancy': {
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Business Studies': {
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Economics': {
        '11': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '12': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    },
    'Environmental Science': {
        '1': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '2': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '3': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '4': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15'],
        '5': ['Chapter 1', 'Chapter 2', 'Chapter 3', 'Chapter 4', 'Chapter 5', 'Chapter 6', 'Chapter 7', 'Chapter 8', 'Chapter 9', 'Chapter 10', 'Chapter 11', 'Chapter 12', 'Chapter 13', 'Chapter 14', 'Chapter 15']
    }
};

// Subject dropdown population
function populateSubjects() {
    console.log('populateSubjects called');
    const standardSelect = document.getElementById('standard');
    const subjectSelect = document.getElementById('subject');
    const chapterSelect = document.getElementById('chapter');

    console.log('standardSelect:', standardSelect);
    console.log('subjectSelect:', subjectSelect);

    if (!standardSelect || !subjectSelect) {
        console.log('Elements not found');
        return;
    }

    const selectedStandard = standardSelect.value;
    console.log('selectedStandard:', selectedStandard);

    // Clear existing options
    subjectSelect.innerHTML = '<option value="">Select Subject</option>';
    if (chapterSelect) {
        chapterSelect.innerHTML = '<option value="">Select Chapter</option>';
    }

    if (selectedStandard) {
        console.log('Using fallback data for standard:', selectedStandard);
        // Use fallback data directly for now
        populateSubjectsFallback(selectedStandard);

        // Try to fetch subjects from server (but don't wait for it)
        fetch(`<?= site_url('staff/getSubjects') ?>?standard=${selectedStandard}`)
            .then(response => response.json())
            .then(data => {
                console.log('Server response:', data);
                if (data.success && data.subjects.length > 0) {
                    // Clear and repopulate with server data
                    subjectSelect.innerHTML = '<option value="">Select Subject</option>';
                    data.subjects.forEach(subject => {
                        const option = document.createElement('option');
                        option.value = subject.name;
                        option.textContent = subject.name;
                        subjectSelect.appendChild(option);
                    });

                    // Always add the custom option at the end
                    const customOption = document.createElement('option');
                    customOption.value = 'custom';
                    customOption.textContent = 'Other (Enter Custom Subject)';
                    subjectSelect.appendChild(customOption);
                }
            })
            .catch(error => {
                console.error('Error fetching subjects:', error);
            });
    }
}

// Fallback subject population
function populateSubjectsFallback(selectedStandard) {
    console.log('populateSubjectsFallback called with:', selectedStandard);
    const subjectSelect = document.getElementById('subject');

    if (selectedStandard && subjectsByStandard[selectedStandard]) {
        console.log('Available subjects:', subjectsByStandard[selectedStandard]);
        subjectsByStandard[selectedStandard].forEach(subject => {
            const option = document.createElement('option');
            option.value = subject;
            option.textContent = subject;
            subjectSelect.appendChild(option);
            console.log('Added subject:', subject);
        });
    } else {
        console.log('No subjects found for standard:', selectedStandard);
    }

    // Always add the custom option at the end
    const customOption = document.createElement('option');
    customOption.value = 'custom';
    customOption.textContent = 'Other (Enter Custom Subject)';
    subjectSelect.appendChild(customOption);
}

// Chapter dropdown population
function populateChapters() {
    console.log('populateChapters called');
    const standardSelect = document.getElementById('standard');
    const subjectSelect = document.getElementById('subject');
    const chapterSelect = document.getElementById('chapter');

    if (!standardSelect || !subjectSelect || !chapterSelect) {
        console.log('Elements not found');
        return;
    }

    const selectedStandard = standardSelect.value;
    const selectedSubject = subjectSelect.value;

    console.log('selectedStandard:', selectedStandard);
    console.log('selectedSubject:', selectedSubject);

    // Clear existing options
    chapterSelect.innerHTML = '<option value="">Select Chapter</option>';

    if (selectedStandard && selectedSubject) {
        // Check if we have chapter data for this subject and standard
        if (chaptersBySubject[selectedSubject] && chaptersBySubject[selectedSubject][selectedStandard]) {
            const chapters = chaptersBySubject[selectedSubject][selectedStandard];
            console.log('Available chapters:', chapters);

            chapters.forEach(chapter => {
                const option = document.createElement('option');
                option.value = chapter;
                option.textContent = chapter;
                chapterSelect.appendChild(option);
                console.log('Added chapter:', chapter);
            });
        } else {
            console.log('No chapters found for subject:', selectedSubject, 'standard:', selectedStandard);
            // Add a generic option to indicate no specific chapters available
            const option = document.createElement('option');
            option.value = 'general';
            option.textContent = 'General Topics';
            chapterSelect.appendChild(option);
        }

        // Always add the custom option at the end
        const customOption = document.createElement('option');
        customOption.value = 'custom';
        customOption.textContent = 'Other (Enter Custom Chapter)';
        chapterSelect.appendChild(customOption);
    }
}

// Question type change handler
function handleQuestionTypeChange() {
    const questionType = document.getElementById('question_type');
    const mcqOptions = document.getElementById('mcq-options');
    const answerSection = document.getElementById('answer-section');

    if (!questionType || !mcqOptions || !answerSection) return;

    if (questionType.value === 'multiple_choice') {
        mcqOptions.classList.remove('hidden');
        answerSection.classList.add('hidden');
        document.getElementById('answer').required = false;
    } else {
        mcqOptions.classList.add('hidden');
        answerSection.classList.remove('hidden');
        document.getElementById('answer').required = true;
    }
}

// Form submission
function submitQuestion() {
    const form = document.getElementById('questionForm');
    const formData = new FormData(form);
    const editId = form.getAttribute('data-edit-id');

    console.log('submitQuestion called');
    console.log('editId:', editId);
    console.log('Form data:', Object.fromEntries(formData));

    // Handle custom subject input
    const subjectSelect = document.getElementById('subject');
    const customSubjectInput = document.getElementById('custom_subject');
    if (subjectSelect && subjectSelect.value === 'custom' && customSubjectInput && customSubjectInput.value.trim()) {
        formData.set('subject', customSubjectInput.value.trim());
        console.log('Using custom subject:', customSubjectInput.value.trim());
    }

    // Handle custom chapter input
    const chapterSelect = document.getElementById('chapter');
    const customChapterInput = document.getElementById('custom_chapter');
    if (chapterSelect && chapterSelect.value === 'custom' && customChapterInput && customChapterInput.value.trim()) {
        formData.set('chapter', customChapterInput.value.trim());
        console.log('Using custom chapter:', customChapterInput.value.trim());
    }

    // Debug chapter and topic fields specifically
    console.log('Chapter:', formData.get('chapter'));
    console.log('Chapter Name:', formData.get('chapter_name'));
    console.log('Topic Name:', formData.get('topic_name'));

    // Additional debugging for form fields
    const chapterNameInput = document.getElementById('chapter_name');
    const topicNameInput = document.getElementById('topic_name');

    console.log('Chapter select element:', chapterSelect);
    console.log('Chapter select value:', chapterSelect ? chapterSelect.value : 'null');
    console.log('Chapter name input:', chapterNameInput);
    console.log('Chapter name value:', chapterNameInput ? chapterNameInput.value : 'null');
    console.log('Topic name input:', topicNameInput);
    console.log('Topic name value:', topicNameInput ? topicNameInput.value : 'null');

    // Add validation here
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }

    // Determine if this is create or update
    const isEdit = editId && editId !== 'null' && editId !== '';

    console.log('isEdit:', isEdit);

    let url, method;
    if (isEdit) {
        url = `<?= site_url('staff/updateQuestion') ?>/${editId}`;
        method = 'POST';
        formData.append('_method', 'PUT');
        console.log('Update URL:', url);
    } else {
        url = '<?= site_url('staff/createQuestion') ?>';
        method = 'POST';
        console.log('Create URL:', url);
    }

    // Submit to server
    fetch(url, {
        method: method,
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const message = isEdit ? 'Question updated successfully!' : 'Question submitted successfully for review!';
            alert(message);

            // Reset form and clear edit mode
            form.reset();
            form.removeAttribute('data-edit-id');

            // Reset form title
            const formTitle = document.querySelector('#create-question-section h3');
            if (formTitle) {
                formTitle.textContent = 'Create New Question - Tamil Nadu SSLC/HSC Format';
            }

            // Reset button texts
            const submitBtn = document.querySelector('#questionForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = 'Submit for Review';
            }

            const draftBtn = document.querySelector('button[onclick="saveAsDraft()"]');
            if (draftBtn) {
                draftBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save as Draft';
            }

            showSection('dashboard');
            // Refresh stats
            location.reload();
        } else {
            if (data.errors) {
                let errorMsg = 'Validation errors:\n';
                for (let field in data.errors) {
                    errorMsg += `- ${data.errors[field]}\n`;
                }
                alert(errorMsg);
            } else {
                alert('Error: ' + data.message);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while submitting the question.');
    });
}

// Save as draft
function saveAsDraft() {
    const form = document.getElementById('questionForm');
    const formData = new FormData(form);
    const editId = form.getAttribute('data-edit-id');

    // Handle custom subject input
    const subjectSelect = document.getElementById('subject');
    const customSubjectInput = document.getElementById('custom_subject');
    if (subjectSelect && subjectSelect.value === 'custom' && customSubjectInput && customSubjectInput.value.trim()) {
        formData.set('subject', customSubjectInput.value.trim());
    }

    // Handle custom chapter input
    const chapterSelect = document.getElementById('chapter');
    const customChapterInput = document.getElementById('custom_chapter');
    if (chapterSelect && chapterSelect.value === 'custom' && customChapterInput && customChapterInput.value.trim()) {
        formData.set('chapter', customChapterInput.value.trim());
    }

    // Determine if this is create or update
    const isEdit = editId && editId !== 'null';
    const url = isEdit ?
        `<?= site_url('staff/updateQuestion') ?>/${editId}` :
        '<?= site_url('staff/saveDraft') ?>';

    // For edit mode, add method and ensure status is draft
    if (isEdit) {
        formData.append('_method', 'PUT');
    }
    // Always set status to draft for this function
    formData.append('status', 'draft');

    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const message = isEdit ? 'Question changes saved!' : 'Question saved as draft!';
            alert(message);

            // If this was a new draft, we might get an ID back
            if (!isEdit && data.question_id) {
                form.setAttribute('data-edit-id', data.question_id);
            }
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving the draft.');
    });
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing dashboard');

    // Show dashboard section by default
    showSection('dashboard');

    // Add event listeners
    const standardSelect = document.getElementById('standard');
    console.log('Standard select element:', standardSelect);
    if (standardSelect) {
        standardSelect.addEventListener('change', populateSubjects);
        console.log('Event listener added to standard select');
    } else {
        console.log('Standard select element not found');
    }

    const questionTypeSelect = document.getElementById('question_type');
    if (questionTypeSelect) {
        questionTypeSelect.addEventListener('change', handleQuestionTypeChange);
    }

    const questionForm = document.getElementById('questionForm');
    if (questionForm) {
        questionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitQuestion();
        });
    }
});

// Load subjects with question counts
function loadSubjectsWithCounts() {
    const subjectsGrid = document.getElementById('subjects-grid');

    // Show loading
    subjectsGrid.innerHTML = `
        <div class="col-span-full text-center py-8 text-gray-500">
            <i class="fas fa-spinner fa-spin mr-2"></i>Loading subjects...
        </div>
    `;

    fetch('<?= site_url('staff/getSubjectsWithCounts') ?>', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.subjects.length === 0) {
                subjectsGrid.innerHTML = `
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-400 mb-4">
                            <i class="fas fa-book text-6xl"></i>
                        </div>
                        <h4 class="text-lg font-medium text-gray-600 mb-2">No Questions Created Yet</h4>
                        <p class="text-gray-500 mb-4">Start creating questions to see your subjects here</p>
                        <button onclick="showSection('create-question')" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition duration-200">
                            <i class="fas fa-plus mr-2"></i>Create Your First Question
                        </button>
                    </div>
                `;
                return;
            }

            let html = '';
            data.subjects.forEach(subject => {
                const subjectIcon = getSubjectIcon(subject.subject);
                const subjectColor = getSubjectColor(subject.subject);

                html += `
                    <div class="border border-gray-200 rounded-lg p-6 hover:shadow-md transition duration-200">
                        <div class="flex items-center mb-4">
                            <i class="${subjectIcon} ${subjectColor} text-2xl mr-3"></i>
                            <h4 class="font-semibold text-gray-800 text-lg">${subject.subject}</h4>
                        </div>

                        <div class="grid grid-cols-2 gap-3 mb-4">
                            <div class="text-center p-2 bg-blue-50 rounded">
                                <div class="text-2xl font-bold text-blue-600">${subject.question_count}</div>
                                <div class="text-xs text-blue-600">Total</div>
                            </div>
                            <div class="text-center p-2 bg-green-50 rounded">
                                <div class="text-2xl font-bold text-green-600">${subject.approved_count}</div>
                                <div class="text-xs text-green-600">Approved</div>
                            </div>
                            <div class="text-center p-2 bg-yellow-50 rounded">
                                <div class="text-2xl font-bold text-yellow-600">${subject.pending_count}</div>
                                <div class="text-xs text-yellow-600">Pending</div>
                            </div>
                            <div class="text-center p-2 bg-red-50 rounded">
                                <div class="text-2xl font-bold text-red-600">${subject.rejected_count}</div>
                                <div class="text-xs text-red-600">Rejected</div>
                            </div>
                        </div>

                        <div class="space-y-2">
                            <button onclick="showSubjectQuestions('${subject.subject}')" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                <i class="fas fa-eye mr-2"></i>View Questions
                            </button>
                            <button onclick="createQuestionForSubject('${subject.subject}')" class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition duration-200">
                                <i class="fas fa-plus mr-2"></i>Create Question
                            </button>
                        </div>
                    </div>
                `;
            });

            subjectsGrid.innerHTML = html;
        } else {
            subjectsGrid.innerHTML = `
                <div class="col-span-full text-center py-8 text-red-500">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Error loading subjects
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        subjectsGrid.innerHTML = `
            <div class="col-span-full text-center py-8 text-red-500">
                <i class="fas fa-exclamation-triangle mr-2"></i>Error loading subjects
            </div>
        `;
    });
}

// Helper functions for subject display
function getSubjectIcon(subject) {
    const icons = {
        'Mathematics': 'fas fa-calculator',
        'Physics': 'fas fa-atom',
        'Chemistry': 'fas fa-flask',
        'Biology': 'fas fa-dna',
        'Tamil': 'fas fa-language',
        'English': 'fas fa-book-open',
        'Science': 'fas fa-microscope',
        'Social Science': 'fas fa-globe',
        'Computer Science': 'fas fa-laptop-code',
        'Accountancy': 'fas fa-chart-line',
        'Business Studies': 'fas fa-briefcase',
        'Economics': 'fas fa-coins'
    };
    return icons[subject] || 'fas fa-book';
}

function getSubjectColor(subject) {
    const colors = {
        'Mathematics': 'text-blue-600',
        'Physics': 'text-purple-600',
        'Chemistry': 'text-green-600',
        'Biology': 'text-emerald-600',
        'Tamil': 'text-red-600',
        'English': 'text-indigo-600',
        'Science': 'text-teal-600',
        'Social Science': 'text-orange-600',
        'Computer Science': 'text-gray-600',
        'Accountancy': 'text-yellow-600',
        'Business Studies': 'text-pink-600',
        'Economics': 'text-cyan-600'
    };
    return colors[subject] || 'text-gray-600';
}

// Load questions for My Questions section
function loadQuestions() {
    const tableBody = document.getElementById('questions-table-body');
    if (!tableBody) return;

    // Show loading
    tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4">Loading questions...</td></tr>';

    fetch('<?= site_url('staff/getQuestions') ?>', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Store all questions globally for filtering
            allQuestions = data.questions;
            filteredQuestions = [...allQuestions];

            // Populate subject filter dropdown
            populateSubjectFilter(allQuestions);

            // Update filter count
            updateFilterResultsCount();

            if (data.questions.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4 text-gray-500">No questions found</td></tr>';
                return;
            }

            displayQuestions(data.questions);
        } else {
            tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4 text-red-500">Error loading questions</td></tr>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4 text-red-500">Error loading questions</td></tr>';
    });
}

// Display questions in table
function displayQuestions(questions) {
    const tableBody = document.getElementById('questions-table-body');
    if (!tableBody) return;

    if (questions.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-4 text-gray-500">No questions found</td></tr>';
        return;
    }

    let html = '';
    questions.forEach(question => {
        const statusClass = getStatusClass(question.status);
        const statusText = getStatusText(question.status);
        const questionText = question.question_text.length > 50 ?
                           question.question_text.substring(0, 50) + '...' :
                           question.question_text;

        // Determine available actions based on status
        let actionsHtml = '';

        // Edit button - only for draft and rejected questions
        if (question.status === 'draft' || question.status === 'rejected') {
            actionsHtml += `<button class="text-blue-600 hover:underline mr-2" onclick="editQuestion(${question.id})" title="Edit Question">
                <i class="fas fa-edit mr-1"></i>Edit
            </button>`;
        }

        // Delete button - only for draft and rejected questions
        if (question.status === 'draft' || question.status === 'rejected') {
            actionsHtml += `<button class="text-red-600 hover:underline mr-2" onclick="deleteQuestion(${question.id})" title="Delete Question">
                <i class="fas fa-trash mr-1"></i>Delete
            </button>`;
        }

        // View button - for all questions
        actionsHtml += `<button class="text-gray-600 hover:underline" onclick="viewQuestion(${question.id})" title="View Details">
            <i class="fas fa-eye mr-1"></i>View
        </button>`;

        // If no actions available, show status message
        if (!actionsHtml.trim()) {
            if (question.status === 'pending') {
                actionsHtml = '<span class="text-gray-500 text-sm">Under Review</span>';
            } else if (question.status === 'approved') {
                actionsHtml = '<span class="text-green-600 text-sm"><i class="fas fa-check mr-1"></i>Approved</span>';
            }
        }

        // Format chapter display
        let chapterDisplay = '-';
        if (question.chapter || question.chapter_name) {
            if (question.chapter && question.chapter !== 'general') {
                chapterDisplay = question.chapter;
            } else if (question.chapter_name) {
                chapterDisplay = question.chapter_name;
            }
        }

        // Format topic display
        let topicDisplay = question.topic_name || '-';

        html += `
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="px-6 py-4" title="${question.question_text}">${questionText}</td>
                <td class="px-6 py-4">
                    <span class="font-medium">${question.subject}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm ${chapterDisplay === '-' ? 'text-gray-400' : 'text-gray-700'}" title="${question.chapter_name || question.chapter || 'No chapter specified'}">${chapterDisplay}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm ${topicDisplay === '-' ? 'text-gray-400' : 'text-gray-700'}" title="${question.topic_name || 'No topic specified'}">${topicDisplay}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm font-medium text-blue-600">Std ${question.standard}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm">${getQuestionTypeText(question.question_type)}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="font-medium text-green-600">${question.marks}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="${statusClass}">${statusText}</span>
                    ${question.admin_feedback ? `<br><small class="text-gray-500 italic">"${question.admin_feedback}"</small>` : ''}
                </td>
                <td class="px-6 py-4">
                    ${actionsHtml}
                </td>
            </tr>
        `;
    });
    tableBody.innerHTML = html;
}

// Helper functions for question display
function getStatusClass(status) {
    switch(status) {
        case 'approved': return 'bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded';
        case 'pending': return 'bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded';
        case 'rejected': return 'bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded';
        case 'draft': return 'bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded';
        default: return 'bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded';
    }
}

function getStatusText(status) {
    switch(status) {
        case 'approved': return 'Approved';
        case 'pending': return 'Pending';
        case 'rejected': return 'Rejected';
        case 'draft': return 'Draft';
        default: return status;
    }
}

function getQuestionTypeText(type) {
    switch(type) {
        case 'multiple_choice': return 'MCQ';
        case 'short_answer': return 'Short Answer';
        case 'long_answer': return 'Long Answer';
        case 'essay': return 'Essay';
        default: return type;
    }
}

// Edit question function
function editQuestion(id) {
    // Fetch question details and populate the form
    fetch(`<?= site_url('staff/getQuestionDetails') ?>/${id}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const question = data.question;

            // Switch to create question section
            showSection('create-question');

            // Wait for section to be visible, then populate form
            setTimeout(() => {
                populateEditForm(question);
            }, 200);
        } else {
            alert('Error loading question: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading question for editing');
    });
}

// Populate form with question data for editing
function populateEditForm(question) {
    // Set form to edit mode
    const form = document.getElementById('questionForm');
    form.setAttribute('data-edit-id', question.id);

    // Update form title
    const formTitle = document.querySelector('#create-question-section h3');
    if (formTitle) {
        formTitle.textContent = 'Edit Question - Tamil Nadu SSLC/HSC Format';
    }

    // Populate form fields
    document.getElementById('standard').value = question.standard;

    // Trigger subject population and then set subject
    populateSubjects();
    setTimeout(() => {
        document.getElementById('subject').value = question.subject;

        // Trigger chapter population and then set chapter
        populateChapters();
        setTimeout(() => {
            if (question.chapter) {
                document.getElementById('chapter').value = question.chapter;
            }
            if (question.chapter_name) {
                document.getElementById('chapter_name').value = question.chapter_name;
            }
            if (question.topic_name) {
                document.getElementById('topic_name').value = question.topic_name;
            }
        }, 300);
    }, 500);

    document.getElementById('question_type').value = question.question_type;
    document.getElementById('difficulty').value = question.difficulty;
    document.getElementById('marks').value = question.marks;
    document.getElementById('question_text').value = question.question_text;
    document.getElementById('answer').value = question.answer || '';

    // Handle MCQ options if applicable
    if (question.question_type === 'multiple_choice' && question.options) {
        const options = JSON.parse(question.options);
        document.querySelector('input[name="option_a"]').value = options.option_a || '';
        document.querySelector('input[name="option_b"]').value = options.option_b || '';
        document.querySelector('input[name="option_c"]').value = options.option_c || '';
        document.querySelector('input[name="option_d"]').value = options.option_d || '';

        // Set correct answer
        if (question.correct_answer) {
            document.querySelector(`input[name="correct_answer"][value="${question.correct_answer}"]`).checked = true;
        }
    }

    // Trigger question type change to show/hide appropriate sections
    handleQuestionTypeChange();

    // Update submit button text
    const submitBtn = document.querySelector('#questionForm button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Update Question';
    }

    // Update draft button text
    const draftBtn = document.querySelector('button[onclick="saveAsDraft()"]');
    if (draftBtn) {
        draftBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save Changes';
    }
}

// View question function
function viewQuestion(id) {
    fetch(`<?= site_url('staff/getQuestionDetails') ?>/${id}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showQuestionViewModal(data.question);
        } else {
            alert('Error loading question: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading question details');
    });
}

// Delete question function
function deleteQuestion(id) {
    if (confirm('Are you sure you want to delete this question? This action cannot be undone.')) {
        fetch(`<?= site_url('staff/deleteQuestion') ?>/${id}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Question deleted successfully!');
                loadQuestions(); // Refresh the questions list
            } else {
                alert('Error deleting question: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting question');
        });
    }
}

// Initialize create question form
function initializeCreateQuestionForm() {
    console.log('Initializing create question form');

    const standardSelect = document.getElementById('standard');
    const subjectSelect = document.getElementById('subject');
    const questionTypeSelect = document.getElementById('question_type');
    const questionForm = document.getElementById('questionForm');

    console.log('Form elements:', {standardSelect, subjectSelect, questionTypeSelect, questionForm});

    // Add event listeners if not already added
    if (standardSelect && !standardSelect.hasAttribute('data-listener-added')) {
        standardSelect.addEventListener('change', populateSubjects);
        standardSelect.setAttribute('data-listener-added', 'true');
        console.log('Standard select listener added');
    }

    if (subjectSelect && !subjectSelect.hasAttribute('data-listener-added')) {
        subjectSelect.addEventListener('change', populateChapters);
        subjectSelect.setAttribute('data-listener-added', 'true');
        console.log('Subject select listener added');
    }

    if (questionTypeSelect && !questionTypeSelect.hasAttribute('data-listener-added')) {
        questionTypeSelect.addEventListener('change', handleQuestionTypeChange);
        questionTypeSelect.setAttribute('data-listener-added', 'true');
        console.log('Question type listener added');
    }

    if (questionForm && !questionForm.hasAttribute('data-listener-added')) {
        questionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitQuestion();
        });
        questionForm.setAttribute('data-listener-added', 'true');
        console.log('Form submit listener added');
    }
}

// Show question view modal
function showQuestionViewModal(question) {
    document.getElementById('view-subject').textContent = question.subject;
    document.getElementById('view-standard').textContent = question.standard;
    document.getElementById('view-chapter').textContent = question.chapter || 'Not specified';
    document.getElementById('view-topic').textContent = question.topic_name || 'Not specified';
    document.getElementById('view-type').textContent = getQuestionTypeText(question.question_type);
    document.getElementById('view-marks').textContent = question.marks;
    document.getElementById('view-question-text').textContent = question.question_text;
    document.getElementById('view-answer').textContent = question.answer || 'No answer provided';

    // Handle status
    const statusClass = getStatusClass(question.status);
    const statusText = getStatusText(question.status);
    document.getElementById('view-status').innerHTML = `<span class="${statusClass}">${statusText}</span>`;

    // Handle MCQ options
    const optionsDiv = document.getElementById('view-options');
    const optionsList = document.getElementById('view-options-list');

    if (question.question_type === 'multiple_choice' && question.options) {
        const options = JSON.parse(question.options);
        let optionsHtml = '';

        ['A', 'B', 'C', 'D'].forEach(letter => {
            if (options[`option_${letter.toLowerCase()}`]) {
                const isCorrect = question.correct_answer === letter;
                optionsHtml += `
                    <div class="flex items-center ${isCorrect ? 'bg-green-50 border border-green-200 rounded p-2' : 'p-2'}">
                        <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs mr-3">${letter}</span>
                        <span class="text-gray-800">${options[`option_${letter.toLowerCase()}`]}</span>
                        ${isCorrect ? '<i class="fas fa-check text-green-600 ml-auto"></i>' : ''}
                    </div>
                `;
            }
        });

        optionsList.innerHTML = optionsHtml;
        optionsDiv.classList.remove('hidden');
    } else {
        optionsDiv.classList.add('hidden');
    }

    // Handle admin feedback
    const feedbackDiv = document.getElementById('view-feedback');
    const feedbackText = document.getElementById('view-feedback-text');

    if (question.admin_feedback) {
        feedbackText.textContent = question.admin_feedback;
        feedbackDiv.classList.remove('hidden');
    } else {
        feedbackDiv.classList.add('hidden');
    }

    // Show modal
    document.getElementById('questionViewModal').classList.remove('hidden');
}

// Hide question view modal
function hideQuestionViewModal() {
    document.getElementById('questionViewModal').classList.add('hidden');
}

// Cancel edit and reset form
function cancelEdit() {
    const form = document.getElementById('questionForm');

    // Reset form
    form.reset();
    form.removeAttribute('data-edit-id');

    // Reset form title
    const formTitle = document.querySelector('#create-question-section h3');
    if (formTitle) {
        formTitle.textContent = 'Create New Question - Tamil Nadu SSLC/HSC Format';
    }

    // Reset button texts
    const submitBtn = document.querySelector('#questionForm button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = 'Submit for Review';
    }

    const draftBtn = document.querySelector('button[onclick="saveAsDraft()"]');
    if (draftBtn) {
        draftBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save as Draft';
    }

    // Hide MCQ options and show answer section
    const mcqOptions = document.getElementById('mcq-options');
    const answerSection = document.getElementById('answer-section');
    if (mcqOptions) mcqOptions.classList.add('hidden');
    if (answerSection) answerSection.classList.remove('hidden');

    // Go back to dashboard
    showSection('dashboard');
}

// Show subject questions modal
function showSubjectQuestions(subject) {
    document.getElementById('subject-modal-title').textContent = `${subject} Questions`;
    document.getElementById('subjectQuestionsModal').classList.remove('hidden');

    // Load questions for this subject
    fetch(`<?= site_url('staff/getQuestionsBySubject') ?>/${encodeURIComponent(subject)}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update statistics
            const stats = calculateSubjectStats(data.questions);
            updateSubjectStats(stats);

            // Update questions table
            updateSubjectQuestionsTable(data.questions);
        } else {
            alert('Error loading questions: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading questions');
    });
}

// Hide subject questions modal
function hideSubjectQuestionsModal() {
    document.getElementById('subjectQuestionsModal').classList.add('hidden');
}

// Calculate subject statistics
function calculateSubjectStats(questions) {
    return {
        total: questions.length,
        approved: questions.filter(q => q.status === 'approved').length,
        pending: questions.filter(q => q.status === 'pending').length,
        rejected: questions.filter(q => q.status === 'rejected').length,
        draft: questions.filter(q => q.status === 'draft').length
    };
}

// Update subject statistics display
function updateSubjectStats(stats) {
    const statsContainer = document.getElementById('subject-stats');
    statsContainer.innerHTML = `
        <div class="text-center p-4 bg-blue-50 rounded-lg">
            <div class="text-3xl font-bold text-blue-600">${stats.total}</div>
            <div class="text-sm text-blue-600">Total Questions</div>
        </div>
        <div class="text-center p-4 bg-green-50 rounded-lg">
            <div class="text-3xl font-bold text-green-600">${stats.approved}</div>
            <div class="text-sm text-green-600">Approved</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 rounded-lg">
            <div class="text-3xl font-bold text-yellow-600">${stats.pending}</div>
            <div class="text-sm text-yellow-600">Pending</div>
        </div>
        <div class="text-center p-4 bg-red-50 rounded-lg">
            <div class="text-3xl font-bold text-red-600">${stats.rejected}</div>
            <div class="text-sm text-red-600">Rejected</div>
        </div>
    `;
}

// Update subject questions table
function updateSubjectQuestionsTable(questions) {
    const tableBody = document.getElementById('subject-questions-table');

    if (questions.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center py-8 text-gray-500">No questions found</td></tr>';
        return;
    }

    let html = '';
    questions.forEach(question => {
        const statusClass = getStatusClass(question.status);
        const statusText = getStatusText(question.status);
        const questionText = question.question_text.length > 50 ?
                           question.question_text.substring(0, 50) + '...' :
                           question.question_text;

        // Format chapter display
        let chapterDisplay = '-';
        if (question.chapter || question.chapter_name) {
            if (question.chapter && question.chapter !== 'general') {
                chapterDisplay = question.chapter;
            } else if (question.chapter_name) {
                chapterDisplay = question.chapter_name;
            }
        }

        // Format topic display
        let topicDisplay = question.topic_name || '-';

        // Determine available actions
        let actionsHtml = '';
        if (question.status === 'draft' || question.status === 'rejected') {
            actionsHtml += `<button class="text-blue-600 hover:underline mr-2" onclick="editQuestion(${question.id}); hideSubjectQuestionsModal();" title="Edit">
                <i class="fas fa-edit"></i>
            </button>`;
        }
        actionsHtml += `<button class="text-gray-600 hover:underline" onclick="viewQuestion(${question.id}); hideSubjectQuestionsModal();" title="View">
            <i class="fas fa-eye"></i>
        </button>`;

        html += `
            <tr class="bg-white border-b hover:bg-gray-50">
                <td class="px-6 py-4" title="${question.question_text}">${questionText}</td>
                <td class="px-6 py-4">
                    <span class="text-sm ${chapterDisplay === '-' ? 'text-gray-400' : 'text-gray-700'}" title="${question.chapter_name || question.chapter || 'No chapter specified'}">${chapterDisplay}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm ${topicDisplay === '-' ? 'text-gray-400' : 'text-gray-700'}" title="${question.topic_name || 'No topic specified'}">${topicDisplay}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm font-medium text-blue-600">Std ${question.standard}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm">${getQuestionTypeText(question.question_type)}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="font-medium text-green-600">${question.marks}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="${statusClass}">${statusText}</span>
                </td>
                <td class="px-6 py-4">
                    <span class="text-sm text-gray-500">${formatDate(question.created_at)}</span>
                </td>
                <td class="px-6 py-4">${actionsHtml}</td>
            </tr>
        `;
    });

    tableBody.innerHTML = html;
}

// Create question for specific subject
function createQuestionForSubject(subject) {
    showSection('create-question');

    // Pre-select the subject when form is ready
    setTimeout(() => {
        const subjectSelect = document.getElementById('subject');
        if (subjectSelect) {
            // First populate subjects, then select
            const standardSelect = document.getElementById('standard');
            if (standardSelect.value) {
                populateSubjects();
                setTimeout(() => {
                    subjectSelect.value = subject;
                }, 500);
            }
        }
    }, 200);
}

// Format date helper
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
    });
}

// Test function
function testSubjects() {
    console.log('Test button clicked');
    const standardSelect = document.getElementById('standard');
    if (standardSelect) {
        standardSelect.value = '10';
        populateSubjects();
    }
}

// Toggle custom subject input
function toggleCustomSubject() {
    const subjectSelect = document.getElementById('subject');
    const customSubjectInput = document.getElementById('custom_subject');

    if (subjectSelect && customSubjectInput) {
        if (subjectSelect.value === 'custom') {
            customSubjectInput.classList.remove('hidden');
            customSubjectInput.required = true;
            customSubjectInput.focus();
        } else {
            customSubjectInput.classList.add('hidden');
            customSubjectInput.required = false;
            customSubjectInput.value = '';
        }
    }
}

// Toggle custom chapter input
function toggleCustomChapter() {
    const chapterSelect = document.getElementById('chapter');
    const customChapterInput = document.getElementById('custom_chapter');

    if (chapterSelect && customChapterInput) {
        if (chapterSelect.value === 'custom') {
            customChapterInput.classList.remove('hidden');
            customChapterInput.focus();
        } else {
            customChapterInput.classList.add('hidden');
            customChapterInput.value = '';
        }
    }
}

// Load detailed reports
function loadDetailedReports() {
    const reportsContent = document.getElementById('reports-content');

    // Show loading
    reportsContent.innerHTML = `
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-spinner fa-spin mr-2"></i>Loading reports...
        </div>
    `;

    fetch('<?= site_url('staff/getDetailedReports') ?>', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayDetailedReports(data.reports);
        } else {
            reportsContent.innerHTML = `
                <div class="text-center py-8 text-red-500">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Error loading reports
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        reportsContent.innerHTML = `
            <div class="text-center py-8 text-red-500">
                <i class="fas fa-exclamation-triangle mr-2"></i>Error loading reports
            </div>
        `;
    });
}

// Display detailed reports
function displayDetailedReports(reports) {
    const reportsContent = document.getElementById('reports-content');

    let html = `
        <!-- Overall Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-blue-600">${reports.overall.total_questions || 0}</div>
                <div class="text-sm text-blue-600">Total Questions</div>
            </div>
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-green-600">${reports.overall.approved_questions || 0}</div>
                <div class="text-sm text-green-600">Approved</div>
            </div>
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-yellow-600">${reports.overall.pending_questions || 0}</div>
                <div class="text-sm text-yellow-600">Pending</div>
            </div>
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
                <div class="text-3xl font-bold text-red-600">${reports.overall.rejected_questions || 0}</div>
                <div class="text-sm text-red-600">Rejected</div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Subject-wise Statistics -->
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Subject-wise Breakdown</h4>
                <div class="space-y-3">
    `;

    if (reports.subjects && reports.subjects.length > 0) {
        reports.subjects.forEach(subject => {
            const approvalRate = subject.total_questions > 0 ?
                Math.round((subject.approved / subject.total_questions) * 100) : 0;

            html += `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div class="flex items-center">
                        <i class="${getSubjectIcon(subject.subject)} ${getSubjectColor(subject.subject)} mr-3"></i>
                        <span class="font-medium">${subject.subject}</span>
                    </div>
                    <div class="text-right">
                        <div class="text-sm font-medium">${subject.total_questions} questions</div>
                        <div class="text-xs text-gray-500">${approvalRate}% approved</div>
                    </div>
                </div>
            `;
        });
    } else {
        html += '<div class="text-center text-gray-500 py-4">No subjects data available</div>';
    }

    html += `
                </div>
            </div>

            <!-- Question Types Distribution -->
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <h4 class="text-lg font-semibold text-gray-800 mb-4">Question Types</h4>
                <div class="space-y-3">
    `;

    if (reports.question_types && reports.question_types.length > 0) {
        const totalQuestions = reports.question_types.reduce((sum, type) => sum + parseInt(type.count), 0);

        reports.question_types.forEach(type => {
            const percentage = totalQuestions > 0 ? Math.round((type.count / totalQuestions) * 100) : 0;
            const typeText = getQuestionTypeText(type.question_type);

            html += `
                <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <span class="font-medium">${typeText}</span>
                    <div class="text-right">
                        <div class="text-sm font-medium">${type.count}</div>
                        <div class="text-xs text-gray-500">${percentage}%</div>
                    </div>
                </div>
            `;
        });
    } else {
        html += '<div class="text-center text-gray-500 py-4">No question types data available</div>';
    }

    html += `
                </div>
            </div>
        </div>

        <!-- Monthly Statistics -->
        <div class="mt-8 bg-white border border-gray-200 rounded-lg p-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">Monthly Activity (Last 6 Months)</h4>
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left">Month</th>
                            <th class="px-4 py-3 text-center">Questions Created</th>
                            <th class="px-4 py-3 text-center">Approved</th>
                            <th class="px-4 py-3 text-center">Approval Rate</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    if (reports.monthly && reports.monthly.length > 0) {
        reports.monthly.forEach(month => {
            const approvalRate = month.questions_created > 0 ?
                Math.round((month.approved / month.questions_created) * 100) : 0;
            const monthName = new Date(month.month + '-01').toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long'
            });

            html += `
                <tr class="border-b hover:bg-gray-50">
                    <td class="px-4 py-3 font-medium">${monthName}</td>
                    <td class="px-4 py-3 text-center">${month.questions_created}</td>
                    <td class="px-4 py-3 text-center text-green-600">${month.approved}</td>
                    <td class="px-4 py-3 text-center">
                        <span class="px-2 py-1 rounded-full text-xs ${approvalRate >= 80 ? 'bg-green-100 text-green-800' : approvalRate >= 60 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}">
                            ${approvalRate}%
                        </span>
                    </td>
                </tr>
            `;
        });
    } else {
        html += '<tr><td colspan="4" class="text-center py-4 text-gray-500">No monthly data available</td></tr>';
    }

    html += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    reportsContent.innerHTML = html;
}

// Load recent activities
function loadRecentActivities() {
    const activitiesList = document.getElementById('recent-activities-list');

    activitiesList.innerHTML = `
        <div class="text-center py-4 text-gray-500">
            <i class="fas fa-spinner fa-spin mr-2"></i>Loading activities...
        </div>
    `;

    fetch('<?= site_url('staff/getRecentActivities') ?>')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRecentActivities(data.activities);
            } else {
                activitiesList.innerHTML = `
                    <div class="text-center py-4 text-red-500">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Failed to load activities
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading activities:', error);
            activitiesList.innerHTML = `
                <div class="text-center py-4 text-red-500">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Error loading activities
                </div>
            `;
        });
}

// Display recent activities
function displayRecentActivities(activities) {
    const activitiesList = document.getElementById('recent-activities-list');

    if (activities.length === 0) {
        activitiesList.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-history text-4xl mb-4"></i>
                <p class="text-lg font-medium">No recent activities</p>
                <p class="text-sm">Start creating questions to see your activities here</p>
            </div>
        `;
        return;
    }

    const activitiesHtml = activities.map(activity => {
        const colorClasses = {
            'gray': 'bg-gray-50 border-gray-200',
            'yellow': 'bg-yellow-50 border-yellow-200',
            'green': 'bg-green-50 border-green-200',
            'red': 'bg-red-50 border-red-200',
            'blue': 'bg-blue-50 border-blue-200'
        };

        const iconColorClasses = {
            'gray': 'text-gray-600 bg-gray-100',
            'yellow': 'text-yellow-600 bg-yellow-100',
            'green': 'text-green-600 bg-green-100',
            'red': 'text-red-600 bg-red-100',
            'blue': 'text-blue-600 bg-blue-100'
        };

        return `
            <div class="flex items-start p-3 ${colorClasses[activity.color]} rounded-lg border">
                <div class="w-8 h-8 ${iconColorClasses[activity.color]} rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                    <i class="${activity.icon} text-sm"></i>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-800">${activity.message}</p>
                    <p class="text-xs text-gray-500 mt-1">${activity.time_ago}</p>
                    ${activity.feedback ? `<p class="text-xs text-red-600 mt-1 italic">"${activity.feedback}"</p>` : ''}
                    <p class="text-xs text-gray-400 mt-1 truncate">${activity.question_text}</p>
                </div>
            </div>
        `;
    }).join('');

    activitiesList.innerHTML = `<div class="space-y-3">${activitiesHtml}</div>`;
}

// Global variables for filtering
let allQuestions = [];
let filteredQuestions = [];

// Apply filters to questions
function applyFilters() {
    const statusFilter = document.getElementById('filter-status').value;
    const subjectFilter = document.getElementById('filter-subject').value;
    const chapterFilter = document.getElementById('filter-chapter').value;
    const typeFilter = document.getElementById('filter-type').value;
    const topicFilter = document.getElementById('filter-topic').value.toLowerCase();
    const searchFilter = document.getElementById('filter-search').value.toLowerCase();

    filteredQuestions = allQuestions.filter(question => {
        const matchesStatus = !statusFilter || question.status === statusFilter;
        const matchesSubject = !subjectFilter || question.subject === subjectFilter;
        const matchesType = !typeFilter || question.question_type === typeFilter;

        // Chapter filter
        const matchesChapter = !chapterFilter ||
            question.chapter === chapterFilter ||
            question.chapter_name === chapterFilter;

        // Topic filter
        const matchesTopic = !topicFilter ||
            (question.topic_name && question.topic_name.toLowerCase().includes(topicFilter));

        // Search filter (searches across question text, subject, chapter, and topic)
        const matchesSearch = !searchFilter ||
            question.question_text.toLowerCase().includes(searchFilter) ||
            question.subject.toLowerCase().includes(searchFilter) ||
            (question.chapter && question.chapter.toLowerCase().includes(searchFilter)) ||
            (question.chapter_name && question.chapter_name.toLowerCase().includes(searchFilter)) ||
            (question.topic_name && question.topic_name.toLowerCase().includes(searchFilter));

        return matchesStatus && matchesSubject && matchesChapter && matchesType && matchesTopic && matchesSearch;
    });

    displayFilteredQuestions();
    updateFilterResultsCount();
    updateFilterIndicators();
}

// Clear all filters
function clearFilters() {
    document.getElementById('filter-status').value = '';
    document.getElementById('filter-subject').value = '';
    document.getElementById('filter-chapter').value = '';
    document.getElementById('filter-type').value = '';
    document.getElementById('filter-topic').value = '';
    document.getElementById('filter-search').value = '';

    filteredQuestions = [...allQuestions];
    displayFilteredQuestions();
    updateFilterResultsCount();
    updateFilterIndicators();
}

// Update filter results count
function updateFilterResultsCount() {
    const countElement = document.getElementById('filter-results-count');
    if (countElement) {
        countElement.textContent = filteredQuestions.length;
    }
}

// Display filtered questions
function displayFilteredQuestions() {
    const questionsContainer = document.getElementById('questions-table-body');
    if (!questionsContainer) return;

    if (filteredQuestions.length === 0) {
        questionsContainer.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-8 text-gray-500">
                    <i class="fas fa-search text-2xl mb-2"></i>
                    <p class="font-medium">No questions found</p>
                    <p class="text-sm">Try adjusting your filters or search terms</p>
                </td>
            </tr>
        `;
        return;
    }

    // Use the existing displayQuestions function but with filtered data
    displayQuestions(filteredQuestions);
}

// Populate subject filter dropdown
function populateSubjectFilter(questions) {
    const subjectFilter = document.getElementById('filter-subject');
    if (!subjectFilter) return;

    // Filter out test subjects and get unique subjects
    const subjects = [...new Set(questions
        .filter(q => q.subject && !q.subject.toLowerCase().includes('test'))
        .map(q => q.subject))]
        .sort();

    // Clear existing options except "All Subjects"
    subjectFilter.innerHTML = '<option value="">All Subjects</option>';

    subjects.forEach(subject => {
        const option = document.createElement('option');
        option.value = subject;
        option.textContent = subject;
        subjectFilter.appendChild(option);
    });

    // Also populate chapter filter
    populateChapterFilter(questions);
}

// Populate chapter filter dropdown
function populateChapterFilter(questions) {
    const chapterFilter = document.getElementById('filter-chapter');
    if (!chapterFilter) return;

    // Get unique chapters (both from chapter and chapter_name fields)
    const chapters = new Set();
    questions.forEach(q => {
        if (q.chapter && q.chapter !== 'general') {
            chapters.add(q.chapter);
        }
        if (q.chapter_name) {
            chapters.add(q.chapter_name);
        }
    });

    const sortedChapters = [...chapters].sort();

    // Clear existing options except "All Chapters"
    chapterFilter.innerHTML = '<option value="">All Chapters</option>';

    sortedChapters.forEach(chapter => {
        const option = document.createElement('option');
        option.value = chapter;
        option.textContent = chapter;
        chapterFilter.appendChild(option);
    });
}

// Update filter indicators
function updateFilterIndicators() {
    const indicators = document.getElementById('filter-active-indicators');
    if (!indicators) return;

    const activeFilters = [];

    if (document.getElementById('filter-status').value) activeFilters.push('Status');
    if (document.getElementById('filter-subject').value) activeFilters.push('Subject');
    if (document.getElementById('filter-chapter').value) activeFilters.push('Chapter');
    if (document.getElementById('filter-type').value) activeFilters.push('Type');
    if (document.getElementById('filter-topic').value) activeFilters.push('Topic');
    if (document.getElementById('filter-search').value) activeFilters.push('Search');

    if (activeFilters.length > 0) {
        indicators.innerHTML = `<span class="text-blue-600 text-xs">Filtered by: ${activeFilters.join(', ')}</span>`;
    } else {
        indicators.innerHTML = '';
    }
}

// Notification System
let notifications = [];

// Toggle notifications dropdown
function toggleNotifications() {
    const dropdown = document.getElementById('notifications-dropdown');
    const isVisible = dropdown.style.display !== 'none';

    if (isVisible) {
        dropdown.style.display = 'none';
    } else {
        dropdown.style.display = 'block';
        loadNotifications();
    }

    // Close dropdown when clicking outside
    if (!isVisible) {
        document.addEventListener('click', closeNotificationsOnOutsideClick);
    }
}

// Close notifications when clicking outside
function closeNotificationsOnOutsideClick(event) {
    const dropdown = document.getElementById('notifications-dropdown');
    const button = document.getElementById('notification-btn');

    if (!dropdown.contains(event.target) && !button.contains(event.target)) {
        dropdown.style.display = 'none';
        document.removeEventListener('click', closeNotificationsOnOutsideClick);
    }
}

// Load notifications
function loadNotifications() {
    // Simulate notifications based on question status changes
    const recentNotifications = [
        {
            id: 1,
            type: 'approval',
            message: 'Your Mathematics question has been approved',
            time: '2 minutes ago',
            read: false
        },
        {
            id: 2,
            type: 'rejection',
            message: 'Your Science question needs revision',
            time: '1 hour ago',
            read: false
        },
        {
            id: 3,
            type: 'info',
            message: 'New question submission guidelines available',
            time: '2 hours ago',
            read: true
        }
    ];

    notifications = recentNotifications;
    updateNotificationDisplay();
}

// Update notification display
function updateNotificationDisplay() {
    const countElement = document.getElementById('notification-count');
    const listElement = document.getElementById('notifications-list');

    const unreadCount = notifications.filter(n => !n.read).length;

    // Update count badge
    if (unreadCount > 0) {
        countElement.textContent = unreadCount;
        countElement.style.display = 'flex';
    } else {
        countElement.style.display = 'none';
    }

    // Update notifications list
    if (notifications.length === 0) {
        listElement.innerHTML = `
            <div class="p-4 text-center text-gray-500">
                <i class="fas fa-bell-slash text-2xl mb-2"></i>
                <p>No new notifications</p>
            </div>
        `;
    } else {
        listElement.innerHTML = notifications.map(notification => `
            <div class="p-3 border-b border-gray-100 hover:bg-gray-50 ${notification.read ? 'opacity-60' : ''}" onclick="markAsRead(${notification.id})">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas ${getNotificationIcon(notification.type)} text-${getNotificationColor(notification.type)}-500"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm text-gray-800 ${notification.read ? '' : 'font-medium'}">${notification.message}</p>
                        <p class="text-xs text-gray-500 mt-1">${notification.time}</p>
                    </div>
                    ${!notification.read ? '<div class="w-2 h-2 bg-blue-500 rounded-full"></div>' : ''}
                </div>
            </div>
        `).join('');
    }
}

// Get notification icon based on type
function getNotificationIcon(type) {
    switch (type) {
        case 'approval': return 'fa-check-circle';
        case 'rejection': return 'fa-times-circle';
        case 'info': return 'fa-info-circle';
        default: return 'fa-bell';
    }
}

// Get notification color based on type
function getNotificationColor(type) {
    switch (type) {
        case 'approval': return 'green';
        case 'rejection': return 'red';
        case 'info': return 'blue';
        default: return 'gray';
    }
}

// Mark notification as read
function markAsRead(notificationId) {
    const notification = notifications.find(n => n.id === notificationId);
    if (notification) {
        notification.read = true;
        updateNotificationDisplay();
    }
}

// Mark all notifications as read
function markAllAsRead() {
    notifications.forEach(n => n.read = true);
    updateNotificationDisplay();
}

// Question Papers Management Functions
function loadQuestionPapers() {
    const loadingDiv = document.getElementById('question-papers-loading');
    const contentDiv = document.getElementById('question-papers-content');
    const gridDiv = document.getElementById('question-papers-grid');
    const noDataDiv = document.getElementById('no-papers-message');

    loadingDiv.classList.remove('hidden');
    contentDiv.classList.add('hidden');

    fetch('/staff/getQuestionPapers', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        loadingDiv.classList.add('hidden');
        contentDiv.classList.remove('hidden');

        if (data.success && data.data && data.data.length > 0) {
            displayQuestionPapers(data.data);
            noDataDiv.classList.add('hidden');
            gridDiv.classList.remove('hidden');
        } else {
            gridDiv.classList.add('hidden');
            noDataDiv.classList.remove('hidden');
        }
    })
    .catch(error => {
        console.error('Error loading question papers:', error);
        loadingDiv.classList.add('hidden');
        contentDiv.classList.remove('hidden');
        gridDiv.classList.add('hidden');
        noDataDiv.classList.remove('hidden');
    });
}

function displayQuestionPapers(papers) {
    const gridDiv = document.getElementById('question-papers-grid');

    let html = '';
    papers.forEach(paper => {
        const statusColor = paper.status === 'published' ? 'green' : 'yellow';
        const statusText = paper.status === 'published' ? 'Published' : 'Draft';

        html += `
            <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition duration-200">
                <div class="flex items-start justify-between mb-4">
                    <div class="flex-1">
                        <h4 class="text-lg font-semibold text-gray-800 mb-1">${paper.title}</h4>
                        <p class="text-sm text-gray-600">${paper.subject} - Standard ${paper.standard}</p>
                    </div>
                    <span class="px-2 py-1 text-xs font-medium rounded-full bg-${statusColor}-100 text-${statusColor}-800">
                        ${statusText}
                    </span>
                </div>

                <div class="space-y-2 mb-4">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Duration:</span>
                        <span class="font-medium">${paper.duration} hours</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Total Marks:</span>
                        <span class="font-medium">${paper.total_marks}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Questions:</span>
                        <span class="font-medium">${paper.question_count || 0}</span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Created:</span>
                        <span class="font-medium">${formatDate(paper.created_at)}</span>
                    </div>
                </div>

                <div class="flex gap-2">
                    <button onclick="viewQuestionPaper(${paper.id})"
                            class="flex-1 bg-blue-600 text-white py-2 px-3 rounded text-sm hover:bg-blue-700 transition duration-200">
                        <i class="fas fa-eye mr-1"></i>View
                    </button>
                    ${paper.status === 'draft' ? `
                        <button onclick="editQuestionPaper(${paper.id})"
                                class="flex-1 bg-green-600 text-white py-2 px-3 rounded text-sm hover:bg-green-700 transition duration-200">
                            <i class="fas fa-edit mr-1"></i>Edit
                        </button>
                    ` : ''}
                    <button onclick="downloadQuestionPaper(${paper.id})"
                            class="bg-gray-600 text-white py-2 px-3 rounded text-sm hover:bg-gray-700 transition duration-200">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
        `;
    });

    gridDiv.innerHTML = html;
}

function showCreateQuestionPaperModal() {
    document.getElementById('templateSelectionModal').classList.remove('hidden');
}

function hideTemplateSelectionModal() {
    document.getElementById('templateSelectionModal').classList.add('hidden');
}

// Template Selection and Question Paper Creation
function selectTemplate(templateType) {
    hideTemplateSelectionModal();

    // Show notification based on template type
    switch(templateType) {
        case 'tn-sslc':
            showNotification('Tamil Nadu SSLC template selected! Ready for question paper creation.', 'success');
            loadSSLCTemplate();
            break;
        case 'tn-hsc1':
            showNotification('Tamil Nadu HSC 1 (Plus One) template selected! Ready for question paper creation.', 'success');
            loadHSC1Template();
            break;
        case 'tn-hsc2':
            showNotification('Tamil Nadu HSC 2 (Plus Two) template selected! Ready for question paper creation.', 'success');
            loadHSC2Template();
            break;
        case 'custom':
            showNotification('Custom template selected! You can design your own format.', 'info');
            loadCustomTemplate();
            break;
        default:
            showNotification('Please select a valid template.', 'error');
    }
}

function loadSSLCTemplate() {
    showSubjectModal('sslc');
}

function loadHSC1Template() {
    showSubjectModal('hsc1');
}

function loadHSC2Template() {
    showSubjectModal('hsc2');
}

function loadCustomTemplate() {
    // For now, show a simple custom form
    showQuestionPaperForm('custom', 'custom');
}

function showSubjectModal(templateType) {
    const subjects = getSubjectsForTemplate(templateType);

    let modalHTML = `
        <div id="subjectSelectionModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">Select Subject</h3>
                        <p class="text-sm text-gray-600 mt-1">Choose the subject for your ${templateType.toUpperCase()} question paper</p>
                    </div>
                    <button class="text-gray-500 hover:text-gray-700" onclick="closeSubjectModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    `;

    subjects.forEach(subject => {
        modalHTML += `
            <div class="border border-gray-200 rounded-lg p-4 hover:border-green-500 hover:bg-green-50 cursor-pointer transition-all"
                 onclick="selectSubjectTemplate('${subject.key}', '${templateType}')">
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                        <i class="fas ${subject.icon} text-green-600"></i>
                    </div>
                    <h4 class="font-medium text-gray-800">${subject.name}</h4>
                    <p class="text-sm text-gray-600 mt-1">${subject.description}</p>
                </div>
            </div>
        `;
    });

    modalHTML += `
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function getSubjectsForTemplate(templateType) {
    const subjects = {
        'sslc': [
            { key: 'mathematics', name: 'Mathematics', icon: 'fas fa-calculator', description: 'Algebra, Geometry, Statistics' },
            { key: 'science', name: 'Science', icon: 'fas fa-flask', description: 'Physics, Chemistry, Biology' },
            { key: 'socialScience', name: 'Social Science', icon: 'fas fa-globe', description: 'History, Geography, Civics' },
            { key: 'tamil', name: 'Tamil', icon: 'fas fa-language', description: 'Literature and Grammar' },
            { key: 'english', name: 'English', icon: 'fas fa-book', description: 'Literature and Grammar' }
        ],
        'hsc1': [
            // Common Subjects
            { key: 'tamil', name: 'Tamil', icon: 'fas fa-language', description: 'Literature and Grammar' },
            { key: 'english', name: 'English', icon: 'fas fa-book', description: 'Literature and Grammar' },

            // Science Group
            { key: 'mathematics', name: 'Mathematics', icon: 'fas fa-calculator', description: 'Advanced Mathematics' },
            { key: 'physics', name: 'Physics', icon: 'fas fa-atom', description: 'Mechanics, Waves, Heat' },
            { key: 'chemistry', name: 'Chemistry', icon: 'fas fa-flask', description: 'Organic, Inorganic' },
            { key: 'biology', name: 'Biology', icon: 'fas fa-dna', description: 'Botany, Zoology' },
            { key: 'computerScience', name: 'Computer Science', icon: 'fas fa-laptop-code', description: 'Programming, Data Structures' },

            // Commerce Group
            { key: 'accountancy', name: 'Accountancy', icon: 'fas fa-calculator', description: 'Financial Accounting, Cost Accounting' },
            { key: 'commerce', name: 'Commerce', icon: 'fas fa-briefcase', description: 'Business Studies, Trade' },
            { key: 'economics', name: 'Economics', icon: 'fas fa-chart-line', description: 'Micro & Macro Economics' },
            { key: 'businessMaths', name: 'Business Mathematics', icon: 'fas fa-chart-bar', description: 'Statistics, Business Maths' },

            // Arts/Humanities Group
            { key: 'history', name: 'History', icon: 'fas fa-landmark', description: 'Ancient, Medieval, Modern History' },
            { key: 'geography', name: 'Geography', icon: 'fas fa-globe-americas', description: 'Physical, Human Geography' },
            { key: 'politicalScience', name: 'Political Science', icon: 'fas fa-university', description: 'Indian Government, Politics' },
            { key: 'sociology', name: 'Sociology', icon: 'fas fa-users', description: 'Social Studies, Society' },
            { key: 'psychology', name: 'Psychology', icon: 'fas fa-brain', description: 'Human Behavior, Mental Processes' },
            { key: 'philosophy', name: 'Philosophy', icon: 'fas fa-lightbulb', description: 'Logic, Ethics, Metaphysics' }
        ],
        'hsc2': [
            // Common Subjects
            { key: 'tamil', name: 'Tamil', icon: 'fas fa-language', description: 'Literature and Grammar' },
            { key: 'english', name: 'English', icon: 'fas fa-book', description: 'Literature and Grammar' },

            // Science Group
            { key: 'mathematics', name: 'Mathematics', icon: 'fas fa-calculator', description: 'Calculus, Algebra, Differential Equations' },
            { key: 'physics', name: 'Physics', icon: 'fas fa-atom', description: 'Electromagnetism, Modern Physics, Optics' },
            { key: 'chemistry', name: 'Chemistry', icon: 'fas fa-flask', description: 'Physical, Organic, Inorganic Chemistry' },
            { key: 'biology', name: 'Biology', icon: 'fas fa-dna', description: 'Genetics, Ecology, Biotechnology' },
            { key: 'computerScience', name: 'Computer Science', icon: 'fas fa-laptop-code', description: 'Advanced Programming, Data Structures' },

            // Commerce Group
            { key: 'accountancy', name: 'Accountancy', icon: 'fas fa-calculator', description: 'Company Accounts, Analysis of Financial Statements' },
            { key: 'commerce', name: 'Commerce', icon: 'fas fa-briefcase', description: 'Business Studies, Entrepreneurship' },
            { key: 'economics', name: 'Economics', icon: 'fas fa-chart-line', description: 'Indian Economic Development' },
            { key: 'businessMaths', name: 'Business Mathematics', icon: 'fas fa-chart-bar', description: 'Applied Statistics, Operations Research' },

            // Arts/Humanities Group
            { key: 'history', name: 'History', icon: 'fas fa-landmark', description: 'Modern World History, Indian History' },
            { key: 'geography', name: 'Geography', icon: 'fas fa-globe-americas', description: 'India - Physical Features, Resources' },
            { key: 'politicalScience', name: 'Political Science', icon: 'fas fa-university', description: 'Contemporary World Politics' },
            { key: 'sociology', name: 'Sociology', icon: 'fas fa-users', description: 'Social Change and Development in India' },
            { key: 'psychology', name: 'Psychology', icon: 'fas fa-brain', description: 'Psychology and Life' },
            { key: 'philosophy', name: 'Philosophy', icon: 'fas fa-lightbulb', description: 'Ethics and Indian Philosophy' },

            // Additional Subjects
            { key: 'homeScience', name: 'Home Science', icon: 'fas fa-home', description: 'Nutrition, Child Development' },
            { key: 'publicAdministration', name: 'Public Administration', icon: 'fas fa-building', description: 'Administrative Theory, Indian Administration' },
            { key: 'anthropology', name: 'Anthropology', icon: 'fas fa-user-friends', description: 'Cultural and Social Anthropology' },
            { key: 'communicativeEnglish', name: 'Communicative English', icon: 'fas fa-comments', description: 'Advanced English Communication' }
        ]
    };

    return subjects[templateType] || [];
}

function closeSubjectModal() {
    const modal = document.getElementById('subjectSelectionModal');
    if (modal) {
        modal.remove();
    }
}

function selectSubjectTemplate(subject, templateType) {
    console.log(`Selected ${subject} template for ${templateType}`);
    closeSubjectModal();
    showNotification(`${subject.charAt(0).toUpperCase() + subject.slice(1)} template selected! Ready to create question paper.`, 'success');

    // Store selected template data
    const template = getTemplateData(templateType, subject);

    window.selectedTemplate = {
        subject: subject,
        type: templateType,
        template: template
    };

    // Show question paper creation form
    showQuestionPaperForm(subject, templateType);
}

function getTemplateData(templateType, subject) {
    const templates = {
        'sslc': {
            'mathematics': {
                name: 'SSLC Mathematics',
                duration: '2.5 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'science': {
                name: 'SSLC Science',
                duration: '2.5 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            }
        },
        'hsc1': {
            // Common Subjects
            'tamil': {
                name: 'HSC 1 Tamil',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'english': {
                name: 'HSC 1 English',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },

            // Science Group
            'mathematics': {
                name: 'HSC 1 Mathematics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'physics': {
                name: 'HSC 1 Physics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },
            'chemistry': {
                name: 'HSC 1 Chemistry',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },
            'biology': {
                name: 'HSC 1 Biology',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },
            'computerScience': {
                name: 'HSC 1 Computer Science',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },

            // Commerce Group
            'accountancy': {
                name: 'HSC 1 Accountancy',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'commerce': {
                name: 'HSC 1 Commerce',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'economics': {
                name: 'HSC 1 Economics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'businessMaths': {
                name: 'HSC 1 Business Mathematics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },

            // Arts/Humanities Group
            'history': {
                name: 'HSC 1 History',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'geography': {
                name: 'HSC 1 Geography',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'politicalScience': {
                name: 'HSC 1 Political Science',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'sociology': {
                name: 'HSC 1 Sociology',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'psychology': {
                name: 'HSC 1 Psychology',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'philosophy': {
                name: 'HSC 1 Philosophy',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            }
        },
        'hsc2': {
            // Common Subjects
            'tamil': {
                name: 'HSC 2 Tamil',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'english': {
                name: 'HSC 2 English',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },

            // Science Group
            'mathematics': {
                name: 'HSC 2 Mathematics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },
            'physics': {
                name: 'HSC 2 Physics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },
            'chemistry': {
                name: 'HSC 2 Chemistry',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },
            'biology': {
                name: 'HSC 2 Biology',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },
            'computerScience': {
                name: 'HSC 2 Computer Science',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },

            // Commerce Group
            'accountancy': {
                name: 'HSC 2 Accountancy',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'commerce': {
                name: 'HSC 2 Commerce',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'economics': {
                name: 'HSC 2 Economics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'businessMaths': {
                name: 'HSC 2 Business Mathematics',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
                ]
            },

            // Arts/Humanities Group
            'history': {
                name: 'HSC 2 History',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'geography': {
                name: 'HSC 2 Geography',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'politicalScience': {
                name: 'HSC 2 Political Science',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'sociology': {
                name: 'HSC 2 Sociology',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'psychology': {
                name: 'HSC 2 Psychology',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'philosophy': {
                name: 'HSC 2 Philosophy',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },

            // Additional Subjects
            'homeScience': {
                name: 'HSC 2 Home Science',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'publicAdministration': {
                name: 'HSC 2 Public Administration',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'anthropology': {
                name: 'HSC 2 Anthropology',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            },
            'communicativeEnglish': {
                name: 'HSC 2 Communicative English',
                duration: '3 Hours',
                totalMarks: 100,
                sections: [
                    { name: 'Section A', type: 'multiple_choice', totalMarks: 20, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 20 },
                    { name: 'Section B', type: 'short_answer', totalMarks: 30, marksEach: 2, instructions: 'Answer briefly', questionCount: 15 },
                    { name: 'Section C', type: 'long_answer', totalMarks: 50, marksEach: 5, instructions: 'Answer in detail', questionCount: 10 }
                ]
            }
        }
    };

    return templates[templateType]?.[subject] || {
        name: 'Custom Template',
        duration: '3 Hours',
        totalMarks: 100,
        sections: [
            { name: 'Section A', type: 'multiple_choice', totalMarks: 25, marksEach: 1, instructions: 'Choose the correct answer', questionCount: 25 },
            { name: 'Section B', type: 'short_answer', totalMarks: 35, marksEach: 2, instructions: 'Answer briefly', questionCount: 17 },
            { name: 'Section C', type: 'long_answer', totalMarks: 40, marksEach: 5, instructions: 'Answer in detail', questionCount: 8 }
        ]
    };
}

function showQuestionPaperForm(subject, templateType) {
    const template = window.selectedTemplate.template;
    let subjectName, displayTitle;

    if (templateType === 'custom') {
        subjectName = template.name || 'Custom Template';
        displayTitle = `Create Question Paper - ${subjectName}`;
    } else {
        subjectName = subject ? (subject.charAt(0).toUpperCase() + subject.slice(1).replace(/([A-Z])/g, ' $1')) : 'Unknown Subject';
        displayTitle = `Create ${templateType.toUpperCase()} Question Paper - ${subjectName}`;
    }

    // Create the question paper form modal
    const formHTML = `
        <div id="questionPaperFormModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-7xl max-h-[95vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">${displayTitle}</h3>
                        <p class="text-sm text-gray-600 mt-1">Fill in the details and select questions for your paper</p>
                    </div>
                    <button class="text-gray-500 hover:text-gray-700" onclick="closeQuestionPaperForm()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="p-6">
                    <!-- Paper Details Form -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">Paper Details</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Paper Title *</label>
                                <input type="text" id="paperTitle" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                       placeholder="e.g., Mid-term Examination - ${subjectName}">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Standard</label>
                                <select id="paperStandard" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="10" ${templateType === 'sslc' ? 'selected' : ''}>10th Standard</option>
                                    <option value="11" ${templateType === 'hsc1' ? 'selected' : ''}>11th Standard</option>
                                    <option value="12" ${templateType === 'hsc2' ? 'selected' : ''}>12th Standard</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                                <input type="text" id="paperSubject" value="${subjectName}" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                                <input type="text" id="examDuration" value="${template.duration}" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Total Marks</label>
                                <input type="number" id="totalMarks" value="${template.totalMarks}" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                                <input type="text" id="academicYear" value="${new Date().getFullYear()}-${new Date().getFullYear() + 1}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Exam Type</label>
                                <select id="examType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="Mid-term">Mid-term Examination</option>
                                    <option value="Final">Final Examination</option>
                                    <option value="Unit Test">Unit Test</option>
                                    <option value="Practice">Practice Test</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Exam Date</label>
                                <input type="date" id="examDate"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Instructions</label>
                            <textarea id="instructions" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                      placeholder="Enter exam instructions...">1. Read all questions carefully before answering.
2. Answer all questions.
3. Write clearly and legibly.
4. Time allowed: ${template.duration}.</textarea>
                        </div>
                    </div>

                    <!-- Question Selection Sections -->
                    <div id="questionSections">
                        <!-- Sections will be loaded here -->
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end gap-3 mt-6 pt-6 border-t border-gray-200">
                        <button type="button" onclick="closeQuestionPaperForm()"
                                class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition duration-200">
                            Cancel
                        </button>
                        <button type="button" onclick="previewQuestionPaper()"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200">
                            <i class="fas fa-eye mr-2"></i>Preview
                        </button>
                        <button type="button" onclick="saveQuestionPaper()"
                                class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                            <i class="fas fa-save mr-2"></i>Create Paper
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', formHTML);

    // Load question sections
    loadQuestionSections(template);
}

function closeQuestionPaperForm() {
    const modal = document.getElementById('questionPaperFormModal');
    if (modal) {
        modal.remove();
    }
}

function loadQuestionSections(template) {
    const sectionsContainer = document.getElementById('questionSections');
    let sectionsHTML = '';

    template.sections.forEach((section, index) => {
        sectionsHTML += `
            <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-800">${section.name}</h4>
                        <p class="text-sm text-gray-600">${section.instructions} (${section.marksEach} mark${section.marksEach > 1 ? 's' : ''} each)</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-600">
                            Selected: <span id="selected-count-${index}" class="font-semibold text-green-600">0</span> / ${section.questionCount}
                        </div>
                        <div class="text-sm text-gray-600">
                            Marks: <span id="selected-marks-${index}" class="font-semibold text-blue-600">0</span> / ${section.totalMarks}
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="flex gap-2">
                        <button onclick="selectAllQuestions(${index})"
                                class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200">
                            Select All
                        </button>
                        <button onclick="clearAllQuestions(${index})"
                                class="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200">
                            Clear All
                        </button>
                        <button onclick="loadQuestionsForSection(${index}, '${section.type}')"
                                class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200">
                            <i class="fas fa-refresh mr-1"></i>Refresh Questions
                        </button>
                    </div>
                </div>

                <div id="questions-section-${index}" class="space-y-3">
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
                        <p>Loading questions...</p>
                    </div>
                </div>
            </div>
        `;
    });

    sectionsContainer.innerHTML = sectionsHTML;

    // Load questions for each section
    template.sections.forEach((section, index) => {
        loadQuestionsForSection(index, section.type);
    });
}

function loadQuestionsForSection(sectionIndex, questionType) {
    const container = document.getElementById(`questions-section-${sectionIndex}`);
    const subject = window.selectedTemplate.subject;

    // Show loading state
    container.innerHTML = `
        <div class="text-center py-8 text-gray-500">
            <i class="fas fa-spinner fa-spin text-2xl mb-2"></i>
            <p>Loading ${questionType} questions...</p>
        </div>
    `;

    // Fetch questions from the staff's approved questions
    fetch(`/staff/getQuestionsBySubject/${encodeURIComponent(subject)}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.questions) {
            // Filter questions by type
            const filteredQuestions = data.questions.filter(q => {
                if (questionType === 'multiple_choice') return q.question_type === 'mcq';
                if (questionType === 'short_answer') return q.question_type === 'short_answer';
                if (questionType === 'long_answer') return q.question_type === 'long_answer';
                return true;
            });

            displayQuestionsForSection(sectionIndex, filteredQuestions, questionType);
        } else {
            displayNoQuestionsMessage(sectionIndex, questionType, subject, 'No approved questions found for this section.');
        }
    })
    .catch(error => {
        console.error('Error loading questions:', error);
        displayNoQuestionsMessage(sectionIndex, questionType, subject, 'Error loading questions. Please try again.');
    });
}

function displayQuestionsForSection(sectionIndex, questions, questionType) {
    const container = document.getElementById(`questions-section-${sectionIndex}`);

    if (!questions || questions.length === 0) {
        displayNoQuestionsMessage(sectionIndex, questionType, window.selectedTemplate.subject, 'No questions available for this section.');
        return;
    }

    let questionsHTML = '';
    questions.forEach((question, qIndex) => {
        const questionText = question.question_text.length > 100
            ? question.question_text.substring(0, 100) + '...'
            : question.question_text;

        const difficulty = question.difficulty || 'Medium';
        const marks = question.marks || 1;
        const chapter = question.chapter_name || question.chapter || 'General';

        questionsHTML += `
            <div class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                <input type="checkbox" id="question-${sectionIndex}-${qIndex}"
                       class="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                       data-question-id="${question.id}"
                       data-question-text="${questionText.replace(/"/g, '&quot;')}"
                       data-difficulty="${difficulty}"
                       data-marks="${marks}"
                       data-chapter="${chapter}"
                       onchange="updateSelectedQuestions(${sectionIndex})">
                <div class="flex-1">
                    <label for="question-${sectionIndex}-${qIndex}" class="text-sm text-gray-800 cursor-pointer">
                        <strong>Q${qIndex + 1}:</strong> ${questionText}
                    </label>
                    <div class="mt-1 text-xs text-gray-500 space-x-4">
                        <span><i class="fas fa-book mr-1"></i>${chapter}</span>
                        <span><i class="fas fa-signal mr-1"></i>${difficulty}</span>
                        <span><i class="fas fa-star mr-1"></i>${marks} mark${marks > 1 ? 's' : ''}</span>
                        <span><i class="fas fa-tag mr-1"></i>${question.question_type}</span>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = questionsHTML;
    updateSelectedQuestions(sectionIndex);
}

function displayNoQuestionsMessage(sectionIndex, questionType, subject, message) {
    const container = document.getElementById(`questions-section-${sectionIndex}`);
    container.innerHTML = `
        <div class="text-center py-8">
            <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-3"></i>
            <p class="text-gray-600 mb-2">${message}</p>
            <p class="text-sm text-gray-500 mb-4">You need approved ${questionType.replace('_', ' ')} questions for this section.</p>
            <button type="button" onclick="redirectToQuestionCreation('${subject}', '${questionType}')"
                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                Create Questions First
            </button>
        </div>
    `;
}

function selectAllQuestions(sectionIndex) {
    const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedQuestions(sectionIndex);
}

function clearAllQuestions(sectionIndex) {
    const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedQuestions(sectionIndex);
}

function updateSelectedQuestions(sectionIndex) {
    const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]:checked`);
    const countElement = document.getElementById(`selected-count-${sectionIndex}`);
    const marksElement = document.getElementById(`selected-marks-${sectionIndex}`);

    // Calculate total marks for this section
    let sectionMarks = 0;
    checkboxes.forEach(checkbox => {
        const marks = parseInt(checkbox.getAttribute('data-marks')) || 1;
        sectionMarks += marks;
    });

    // Update section display
    if (countElement) {
        countElement.textContent = checkboxes.length;
    }
    if (marksElement) {
        marksElement.textContent = sectionMarks;
    }

    // Update overall total marks
    updateTotalMarks();
}

function updateTotalMarks() {
    let totalMarks = 0;
    const template = window.selectedTemplate.template;

    template.sections.forEach((section, index) => {
        const marksElement = document.getElementById(`selected-marks-${index}`);
        if (marksElement) {
            totalMarks += parseInt(marksElement.textContent) || 0;
        }
    });

    const totalMarksElement = document.getElementById('totalMarks');
    if (totalMarksElement) {
        totalMarksElement.value = totalMarks;
    }
}

function redirectToQuestionCreation(subject, questionType) {
    closeQuestionPaperForm();
    showNotification(`Redirecting to create ${questionType.replace('_', ' ')} questions for ${subject}`, 'info');

    // Switch to create question section and pre-fill subject
    setTimeout(() => {
        showSection('create-question');
        // Pre-select subject if possible
        const subjectSelect = document.getElementById('subject');
        if (subjectSelect) {
            subjectSelect.value = subject;
        }
    }, 1500);
}

function previewQuestionPaper() {
    const formData = collectFormData();
    if (!validateFormData(formData, true)) { // true = isPreview
        return;
    }

    // Generate preview HTML
    const previewHTML = generatePreviewHTML(formData);

    // Show preview modal
    const previewModal = `
        <div id="previewModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-gray-800">Question Paper Preview</h3>
                    <button class="text-gray-500 hover:text-gray-700" onclick="closePreviewModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-6">
                    ${previewHTML}
                </div>
                <div class="px-6 py-4 border-t border-gray-200 flex justify-end gap-3">
                    <button onclick="closePreviewModal()" class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                        Close Preview
                    </button>
                    <button onclick="closePreviewModal(); saveQuestionPaper();" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        <i class="fas fa-save mr-2"></i>Save Paper
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', previewModal);
}

function closePreviewModal() {
    const modal = document.getElementById('previewModal');
    if (modal) {
        modal.remove();
    }
}

function saveQuestionPaper() {
    const formData = collectFormData();

    if (!validateFormData(formData, false)) { // false = not preview
        return;
    }

    // Show loading state
    showNotification('Creating question paper...', 'info');

    // Prepare data for API
    const apiData = {
        title: formData.title,
        standard: formData.standard,
        subject: formData.subject,
        duration: formData.duration,
        totalMarks: formData.totalMarks,
        academicYear: formData.academicYear,
        examType: formData.examType,
        examDate: formData.examDate,
        instructions: formData.instructions,
        status: 'draft',
        selectedQuestions: formData.selectedQuestions
    };

    fetch('/staff/createQuestionPaper', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(apiData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Question paper created successfully!', 'success');
            closeQuestionPaperForm();

            // Reload question papers if we're on that section
            if (document.getElementById('question-papers-section') && !document.getElementById('question-papers-section').classList.contains('hidden')) {
                loadQuestionPapers();
            }
        } else {
            showNotification(data.message || 'Failed to create question paper', 'error');
        }
    })
    .catch(error => {
        console.error('Error creating question paper:', error);
        showNotification('Error creating question paper', 'error');
    });
}

function collectFormData() {
    const template = window.selectedTemplate.template;
    const selectedQuestions = [];

    // Collect selected questions from all sections
    template.sections.forEach((section, sectionIndex) => {
        const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]:checked`);

        checkboxes.forEach(checkbox => {
            const questionId = checkbox.getAttribute('data-question-id');
            const questionText = checkbox.getAttribute('data-question-text');
            const difficulty = checkbox.getAttribute('data-difficulty');
            const marks = checkbox.getAttribute('data-marks') || section.marksEach;
            const chapter = checkbox.getAttribute('data-chapter');

            if (questionId) {
                selectedQuestions.push({
                    question_id: parseInt(questionId),
                    text: questionText,
                    difficulty: difficulty,
                    marks: parseInt(marks),
                    chapter: chapter,
                    section_name: section.name,
                    section_type: section.type
                });
            }
        });
    });

    // Get total marks from selected questions
    const totalMarks = selectedQuestions.reduce((sum, q) => sum + q.marks, 0);

    return {
        title: document.getElementById('paperTitle').value,
        standard: document.getElementById('paperStandard').value,
        subject: document.getElementById('paperSubject').value,
        duration: parseFloat(document.getElementById('examDuration').value.replace(' Hours', '')) || 3.0,
        totalMarks: totalMarks,
        selectedQuestions: selectedQuestions,
        academicYear: document.getElementById('academicYear').value,
        examType: document.getElementById('examType').value,
        examDate: document.getElementById('examDate').value,
        instructions: document.getElementById('instructions').value,
        template: template
    };
}

function validateFormData(formData, isPreview) {
    if (!formData.title.trim()) {
        showNotification('Please enter a paper title', 'error');
        return false;
    }

    if (formData.selectedQuestions.length === 0) {
        showNotification('Please select at least one question', 'error');
        return false;
    }

    if (!isPreview && formData.totalMarks === 0) {
        showNotification('Total marks cannot be zero', 'error');
        return false;
    }

    return true;
}

function generatePreviewHTML(formData) {
    let html = `
        <div class="max-w-4xl mx-auto bg-white">
            <div class="text-center mb-6 pb-4 border-b-2 border-gray-300">
                <h1 class="text-2xl font-bold mb-2">${formData.title}</h1>
                <div class="text-sm text-gray-600 space-x-4">
                    <span>Subject: ${formData.subject}</span>
                    <span>Standard: ${formData.standard}</span>
                    <span>Duration: ${formData.duration} Hours</span>
                    <span>Total Marks: ${formData.totalMarks}</span>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="font-semibold mb-2">Instructions:</h3>
                <div class="text-sm text-gray-700 whitespace-pre-line">${formData.instructions}</div>
            </div>

            <div class="space-y-6">
    `;

    // Group questions by section
    const questionsBySection = {};
    formData.selectedQuestions.forEach(q => {
        if (!questionsBySection[q.section_name]) {
            questionsBySection[q.section_name] = [];
        }
        questionsBySection[q.section_name].push(q);
    });

    // Display questions by section
    Object.keys(questionsBySection).forEach(sectionName => {
        const questions = questionsBySection[sectionName];
        const sectionMarks = questions.reduce((sum, q) => sum + q.marks, 0);

        html += `
            <div class="mb-6">
                <h3 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">
                    ${sectionName} (${sectionMarks} marks)
                </h3>
                <div class="space-y-4">
        `;

        questions.forEach((question, index) => {
            html += `
                <div class="flex">
                    <div class="w-8 text-right mr-3 font-medium">${index + 1}.</div>
                    <div class="flex-1">
                        <div class="mb-1">${question.text} <span class="text-sm text-gray-500">(${question.marks} mark${question.marks > 1 ? 's' : ''})</span></div>
                        <div class="text-xs text-gray-500">Chapter: ${question.chapter} | Difficulty: ${question.difficulty}</div>
                    </div>
                </div>
            `;
        });

        html += `
                </div>
            </div>
        `;
    });

    html += `
            </div>
        </div>
    `;

    return html;
}

// Template selection function
function selectTemplate(templateType) {
    hideTemplateSelectionModal();

    // Show notification based on template type
    switch(templateType) {
        case 'tn-sslc':
            showNotification('Tamil Nadu SSLC template selected! Ready for question paper creation.', 'success');
            loadSSLCTemplate();
            break;
        case 'tn-hsc1':
            showNotification('Tamil Nadu HSC 1 (Plus One) template selected! Ready for question paper creation.', 'success');
            loadHSC1Template();
            break;
        case 'tn-hsc2':
            showNotification('Tamil Nadu HSC 2 (Plus Two) template selected! Ready for question paper creation.', 'success');
            loadHSC2Template();
            break;
        case 'custom':
            showNotification('Custom template selected! You can design your own format.', 'info');
            loadCustomTemplate();
            break;
        default:
            showNotification('Please select a valid template.', 'error');
    }
}






function showQuestionPaperForm(subject, templateType) {
    const template = window.selectedTemplate.template;
    const subjectName = subject ? (subject.charAt(0).toUpperCase() + subject.slice(1).replace(/([A-Z])/g, ' $1')) : 'Unknown Subject';
    const displayTitle = `Create ${templateType.toUpperCase()} Question Paper - ${subjectName}`;

    const modalHTML = `
        <div id="questionPaperFormModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white rounded-lg shadow-xl w-full max-w-7xl max-h-[95vh] overflow-y-auto">
                <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">${displayTitle}</h3>
                        <p class="text-sm text-gray-600">Fill in the details and select questions for your paper</p>
                    </div>
                    <button class="text-gray-500 hover:text-gray-700" onclick="closeQuestionPaperForm()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="p-6">
                    <!-- Paper Details Form -->
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4">Paper Details</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Paper Title *</label>
                                <input type="text" id="paperTitle" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                       placeholder="e.g., Mid-term Examination - ${subjectName}">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                                <input type="text" id="academicYear" value="${new Date().getFullYear()}-${new Date().getFullYear() + 1}"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Exam Type</label>
                                <select id="examType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                                    <option value="Mid-term">Mid-term Examination</option>
                                    <option value="Final">Final Examination</option>
                                    <option value="Unit Test">Unit Test</option>
                                    <option value="Practice">Practice Test</option>
                                    <option value="Mock">Mock Examination</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Exam Date</label>
                                <input type="date" id="examDate"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                                <input type="text" id="examDuration" value="${template.duration}" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Total Marks</label>
                                <input type="number" id="totalMarks" value="${template.totalMarks}" readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100">
                            </div>
                        </div>

                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Instructions</label>
                            <textarea id="instructions" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                                      placeholder="Enter exam instructions...">1. Read all questions carefully before answering.
2. Answer all questions.
3. Write clearly and legibly.
4. Time allowed: ${template.duration}.</textarea>
                        </div>
                    </div>

                    <!-- Question Selection Sections -->
                    <div id="questionSections">
                        ${template.sections.map((section, index) => `
                            <div class="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-lg font-semibold text-gray-800">${section.name}</h4>
                                        <p class="text-sm text-gray-600">${section.instructions} (${section.marksEach} mark${section.marksEach > 1 ? 's' : ''} each)</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-sm text-gray-600">Selected: <span id="selected-count-${index}" class="font-semibold">0</span>/${section.questionCount}</div>
                                        <div class="text-sm text-gray-600">Marks: <span id="selected-marks-${index}" class="font-semibold">0</span>/${section.totalMarks}</div>
                                    </div>
                                </div>

                                <div class="mb-4 flex gap-2">
                                    <button type="button" onclick="loadQuestionsForSection(${index}, '${subject}', '${section.type}')"
                                            class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition duration-200">
                                        <i class="fas fa-search mr-2"></i>Load Questions
                                    </button>
                                    <button type="button" onclick="selectAllQuestions(${index})"
                                            class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition duration-200">
                                        <i class="fas fa-check-double mr-2"></i>Select All
                                    </button>
                                    <button type="button" onclick="clearAllQuestions(${index})"
                                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition duration-200">
                                        <i class="fas fa-times mr-2"></i>Clear All
                                    </button>
                                </div>

                                <div id="questions-section-${index}" class="space-y-3">
                                    <div class="text-center py-8 text-gray-500">
                                        <i class="fas fa-search text-3xl mb-2"></i>
                                        <p>Click "Load Questions" to see available questions for this section</p>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end gap-3 pt-6 border-t border-gray-200">
                        <button type="button" onclick="closeQuestionPaperForm()"
                                class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition duration-200">
                            Cancel
                        </button>
                        <button type="button" onclick="previewQuestionPaper()"
                                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200">
                            <i class="fas fa-eye mr-2"></i>Preview
                        </button>
                        <button type="button" onclick="saveQuestionPaper()"
                                class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                            <i class="fas fa-save mr-2"></i>Create Paper
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function closeQuestionPaperForm() {
    const modal = document.getElementById('questionPaperFormModal');
    if (modal) {
        modal.remove();
    }
}

// Question loading and selection functions
function loadQuestionsForSection(sectionIndex, subject, questionType) {
    const container = document.getElementById(`questions-section-${sectionIndex}`);
    container.innerHTML = '<div class="text-center py-4"><i class="fas fa-spinner fa-spin mr-2"></i>Loading questions...</div>';

    // Get staff's approved questions for this subject and type
    fetch(`/staff/getQuestionsBySubject/${encodeURIComponent(subject)}?type=${questionType}&status=approved`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.questions && data.questions.length > 0) {
            displayQuestionsForSelection(sectionIndex, data.questions);
        } else {
            container.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-3"></i>
                    <p class="text-gray-600 mb-2">No approved ${questionType.replace('_', ' ')} questions found for ${subject}</p>
                    <p class="text-sm text-gray-500 mb-4">You need approved questions for this section to create a question paper.</p>
                    <button type="button" onclick="redirectToQuestionCreation('${subject}', '${questionType}')"
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        Create Questions First
                    </button>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error loading questions:', error);
        container.innerHTML = `
            <div class="text-center py-8 text-red-500">
                <i class="fas fa-exclamation-circle text-3xl mb-3"></i>
                <p>Error loading questions. Please try again.</p>
            </div>
        `;
    });
}

function displayQuestionsForSelection(sectionIndex, questions) {
    const container = document.getElementById(`questions-section-${sectionIndex}`);

    let questionsHTML = '';
    questions.forEach((question, qIndex) => {
        const questionText = question.question_text.length > 100
            ? question.question_text.substring(0, 100) + '...'
            : question.question_text;

        const difficulty = question.difficulty || 'Medium';
        const marks = question.marks || 1;
        const chapter = question.chapter || 'General';

        questionsHTML += `
            <div class="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                <input type="checkbox" id="question-${sectionIndex}-${qIndex}"
                       class="mt-1 h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                       data-question-id="${question.id}"
                       data-question-text="${questionText.replace(/"/g, '&quot;')}"
                       data-difficulty="${difficulty}"
                       data-marks="${marks}"
                       data-chapter="${chapter}"
                       onchange="updateSelectedQuestions(${sectionIndex})">
                <div class="flex-1">
                    <label for="question-${sectionIndex}-${qIndex}" class="text-sm text-gray-800 cursor-pointer">
                        <strong>Q${qIndex + 1}:</strong> ${questionText}
                    </label>
                    <div class="mt-1 text-xs text-gray-500 space-x-4">
                        <span><i class="fas fa-layer-group mr-1"></i>${difficulty}</span>
                        <span><i class="fas fa-star mr-1"></i>${marks} mark${marks > 1 ? 's' : ''}</span>
                        <span><i class="fas fa-book mr-1"></i>${chapter}</span>
                        <span><i class="fas fa-question-circle mr-1"></i>${question.question_type}</span>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = questionsHTML;
    updateSelectedQuestions(sectionIndex);
}

function selectAllQuestions(sectionIndex) {
    const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedQuestions(sectionIndex);
}

function clearAllQuestions(sectionIndex) {
    const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedQuestions(sectionIndex);
}

function updateSelectedQuestions(sectionIndex) {
    const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]:checked`);
    const countElement = document.getElementById(`selected-count-${sectionIndex}`);
    const marksElement = document.getElementById(`selected-marks-${sectionIndex}`);

    // Calculate total marks for this section
    let sectionMarks = 0;
    checkboxes.forEach(checkbox => {
        const marks = parseInt(checkbox.getAttribute('data-marks')) || 0;
        sectionMarks += marks;
    });

    // Update section display
    if (countElement) {
        countElement.textContent = checkboxes.length;
    }
    if (marksElement) {
        marksElement.textContent = sectionMarks;
    }

    // Update overall total marks
    updateTotalMarks();
}

function updateTotalMarks() {
    const allMarksElements = document.querySelectorAll('[id^="selected-marks-"]');
    let totalMarks = 0;

    allMarksElements.forEach(element => {
        totalMarks += parseInt(element.textContent) || 0;
    });

    const totalMarksInput = document.getElementById('totalMarks');
    if (totalMarksInput) {
        totalMarksInput.value = totalMarks;
    }
}

function redirectToQuestionCreation(subject, questionType) {
    showNotification(`Redirecting to create questions for ${subject} - ${questionType}`, 'info');
    closeQuestionPaperForm();
    setTimeout(() => {
        showSection('create-question');
    }, 1000);
}

function previewQuestionPaper() {
    const formData = collectFormData();

    if (!validateFormData(formData, true)) {
        return;
    }

    showNotification('Preview functionality will be implemented soon!', 'info');
}

function saveQuestionPaper() {
    const formData = collectFormData();

    if (!validateFormData(formData, false)) {
        return;
    }

    // Show loading state
    showNotification('Creating question paper...', 'info');

    // Prepare data for API
    const apiData = {
        title: formData.title,
        standard: getStandardFromTemplate(),
        subject: getSubjectFromTemplate(),
        duration: parseFloat(formData.duration.replace(' Hours', '')),
        totalMarks: formData.totalMarks,
        academicYear: formData.academicYear,
        examType: formData.examType,
        examDate: formData.examDate,
        instructions: formData.instructions,
        status: 'draft',
        selectedQuestions: formData.selectedQuestions
    };

    fetch('/staff/createQuestionPaper', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
        },
        body: JSON.stringify(apiData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Question paper created successfully!', 'success');
            closeQuestionPaperForm();

            // Reload question papers if we're on that section
            if (document.getElementById('question-papers-section') && !document.getElementById('question-papers-section').classList.contains('hidden')) {
                setTimeout(() => {
                    loadQuestionPapers();
                }, 1000);
            }
        } else {
            showNotification(data.message || 'Failed to create question paper', 'error');
        }
    })
    .catch(error => {
        console.error('Error creating question paper:', error);
        showNotification('Error creating question paper', 'error');
    });
}

function collectFormData() {
    const template = window.selectedTemplate.template;
    const selectedQuestions = [];

    // Collect selected questions from all sections
    template.sections.forEach((section, sectionIndex) => {
        const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]:checked`);

        checkboxes.forEach(checkbox => {
            const questionId = checkbox.getAttribute('data-question-id');
            const questionText = checkbox.getAttribute('data-question-text');
            const difficulty = checkbox.getAttribute('data-difficulty');
            const marks = checkbox.getAttribute('data-marks') || section.marksEach;
            const chapter = checkbox.getAttribute('data-chapter');

            if (questionId) {
                selectedQuestions.push({
                    question_id: parseInt(questionId),
                    text: questionText,
                    difficulty: difficulty,
                    marks: parseInt(marks),
                    chapter: chapter,
                    section_name: section.name,
                    section_type: section.type
                });
            }
        });
    });

    return {
        title: document.getElementById('paperTitle').value,
        duration: document.getElementById('examDuration').value,
        totalMarks: parseInt(document.getElementById('totalMarks').value),
        selectedQuestions: selectedQuestions,
        academicYear: document.getElementById('academicYear').value,
        examType: document.getElementById('examType').value,
        examDate: document.getElementById('examDate').value,
        instructions: document.getElementById('instructions').value,
        template: template
    };
}

function validateFormData(formData, isPreview = false) {
    if (!formData.title.trim()) {
        showNotification('Please enter a paper title', 'error');
        return false;
    }

    if (formData.selectedQuestions.length === 0) {
        showNotification('Please select at least one question', 'error');
        return false;
    }

    return true;
}

function getStandardFromTemplate() {
    const templateType = window.selectedTemplate.type;
    switch(templateType) {
        case 'sslc': return '10';
        case 'hsc1': return '11';
        case 'hsc2': return '12';
        default: return '10';
    }
}

function getSubjectFromTemplate() {
    const subject = window.selectedTemplate.subject;
    const subjectMap = {
        'mathematics': 'Mathematics',
        'science': 'Science',
        'physics': 'Physics',
        'chemistry': 'Chemistry',
        'biology': 'Biology',
        'computerScience': 'Computer Science',
        'tamil': 'Tamil',
        'english': 'English',
        'socialScience': 'Social Science'
    };

    return subjectMap[subject] || 'General';
}

function viewQuestionPaper(paperId) {
    // Implement view functionality
    console.log('Viewing question paper:', paperId);
}

function editQuestionPaper(paperId) {
    // Implement edit functionality
    console.log('Editing question paper:', paperId);
}

function downloadQuestionPaper(paperId) {
    // Implement download functionality
    window.open(`/staff/downloadQuestionPaper/${paperId}`, '_blank');
}

// Utility functions for notifications
function showSuccess(message) {
    showNotification(message, 'success');
}

function showError(message) {
    showNotification(message, 'error');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

    // Set colors based on type
    const colors = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        info: 'bg-blue-500 text-white',
        warning: 'bg-yellow-500 text-black'
    };

    notification.className += ` ${colors[type] || colors.info}`;

    // Set content
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} mr-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}

// Template card hover effects
document.addEventListener('DOMContentLoaded', function() {
    // Add template card styling
    const style = document.createElement('style');
    style.textContent = `
        .template-card {
            transition: all 0.3s ease;
        }
        .template-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .template-card:hover .template-icon {
            transform: scale(1.1);
        }
        .template-icon {
            transition: transform 0.3s ease;
        }
    `;
    document.head.appendChild(style);
});

// Initialize dashboard on page load
document.addEventListener('DOMContentLoaded', function() {
    showSection('dashboard');

    // Load initial notifications
    setTimeout(() => {
        loadNotifications();
    }, 1000);
});
</script>

</body>
</html>