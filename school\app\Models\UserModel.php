<?php

namespace App\Models;

use CodeIgniter\Model;

class UserModel extends Model
{
    protected $table = 'users';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false; // Using is_deleted field instead

    protected $allowedFields = [
        'school_id', 'name', 'email', 'password', 'status', 'is_deleted', 'deleted_at'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules based on your schema
    protected $validationRules = [
        'school_id' => 'required|integer',
        'name' => 'required|min_length[3]|max_length[255]',
        'email' => 'required|valid_email|is_unique[users.email,id,{id}]',
        'password' => 'required|min_length[6]',
        'status' => 'in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'email' => [
            'is_unique' => 'This email is already registered.'
        ]
    ];

    /**
     * Get users by school ID (excluding deleted)
     */
    public function getUsersBySchool($schoolId)
    {
        return $this->where('school_id', $schoolId)
                   ->where('is_deleted', false)
                   ->findAll();
    }

    /**
     * Get active users by school ID
     */
    public function getActiveUsersBySchool($schoolId)
    {
        return $this->where('school_id', $schoolId)
                   ->where('status', 'active')
                   ->where('is_deleted', false)
                   ->findAll();
    }

    /**
     * Create user with hashed password
     */
    public function createUser($data)
    {
        if (isset($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        return $this->insert($data);
    }

    /**
     * Soft delete user (set is_deleted = true and update deleted_at)
     */
    public function softDelete($userId)
    {
        // Get current timestamp
        $now = date('Y-m-d H:i:s');

        // Update the user with soft delete fields
        $result = $this->update($userId, [
            'is_deleted' => 1,  // Use 1 instead of true for better database compatibility
            'deleted_at' => $now
        ]);

        // Log for debugging
        if (!$result) {
            log_message('error', "Failed to soft delete user ID: {$userId}");
        } else {
            log_message('info', "Successfully soft deleted user ID: {$userId} at {$now}");
        }

        return $result;
    }

    /**
     * Restore soft deleted user
     */
    public function restoreUser($userId)
    {
        $result = $this->update($userId, [
            'is_deleted' => 0,
            'deleted_at' => null
        ]);

        if ($result) {
            log_message('info', "Successfully restored user ID: {$userId}");
        } else {
            log_message('error', "Failed to restore user ID: {$userId}");
        }

        return $result;
    }

    /**
     * Get deleted users by school ID
     */
    public function getDeletedUsersBySchool($schoolId)
    {
        return $this->where('school_id', $schoolId)
                   ->where('is_deleted', 1)
                   ->findAll();
    }

    /**
     * Permanently delete user (hard delete)
     */
    public function hardDelete($userId)
    {
        return $this->delete($userId, true); // true for hard delete
    }

    /**
     * Get users with profiles
     */
    public function getUsersWithProfiles($schoolId)
    {
        return $this->select('users.*, user_profiles.designation, user_profiles.phone')
                   ->join('user_profiles', 'user_profiles.user_id = users.id', 'left')
                   ->where('users.school_id', $schoolId)
                   ->where('users.is_deleted', false)
                   ->findAll();
    }

    /**
     * Verify user login
     */
    public function verifyLogin($email, $password)
    {
        $user = $this->where('email', $email)
                    ->where('status', 'active')
                    ->where('is_deleted', false)
                    ->first();

        if ($user && password_verify($password, $user['password'])) {
            return $user;
        }

        return false;
    }
}
