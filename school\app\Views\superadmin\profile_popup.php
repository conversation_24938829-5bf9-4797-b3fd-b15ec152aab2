<!-- Superadmin Profile Popup -->
<div id="superadmin-profile-popup" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen p-4">
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onclick="closeProfilePopup()"></div>
        
        <!-- Profile Card -->
        <div class="relative bg-white rounded-xl shadow-2xl max-w-md w-full mx-auto transform transition-all">
            <!-- Header with gradient background -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-t-xl p-6 text-white">
                <div class="flex items-center space-x-4">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-2xl">SA</span>
                    </div>
                    <div>
                        <h2 id="profile-name" class="text-xl font-bold">Super Admin</h2>
                        <p id="profile-role" class="text-indigo-100">System Administrator</p>
                    </div>
                </div>
                <button onclick="closeProfilePopup()" class="absolute top-4 right-4 text-white hover:text-indigo-200 transition-colors">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
            
            <!-- Profile Content -->
            <div class="p-6">
                <div class="space-y-6">
                    <!-- Personal Information -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Personal Information</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Email</span>
                                <span id="profile-email" class="font-medium">Loading...</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Role</span>
                                <span id="profile-role-detail" class="font-medium text-indigo-600">Super Administrator</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-600">Status</span>
                                <span id="profile-status" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                                    <span id="profile-status-text">Active</span>
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Account Statistics -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Account Statistics</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-sm text-gray-500">Last Login</div>
                                <div id="profile-last-login" class="font-medium">Loading...</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-sm text-gray-500">Account Created</div>
                                <div id="profile-created" class="font-medium">Loading...</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-sm text-gray-500">Schools Managed</div>
                                <div id="profile-schools" class="font-medium">Loading...</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-sm text-gray-500">Total Sessions</div>
                                <div id="profile-sessions" class="font-medium">Loading...</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Quick Actions</h3>
                        <div class="flex flex-wrap gap-2">
                            <button onclick="closeProfilePopup(); showSection('system')" class="px-3 py-2 bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors">
                                <i class="fas fa-cog mr-1"></i> System Settings
                            </button>
                            <button onclick="openChangePasswordModal()" class="px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors">
                                <i class="fas fa-key mr-1"></i> Change Password
                            </button>
                            <a href="<?= site_url('logout') ?>" class="px-3 py-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors inline-flex items-center">
                                <i class="fas fa-sign-out-alt mr-1"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div id="change-password-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen p-4">
        <!-- Backdrop -->
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onclick="closeChangePasswordModal()"></div>
        
        <!-- Modal Content -->
        <div class="relative bg-white rounded-xl shadow-2xl max-w-md w-full mx-auto transform transition-all">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Change Password</h3>
                <button onclick="closeChangePasswordModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div class="p-6">
                <form id="change-password-form" class="space-y-4">
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700 mb-1">Current Password</label>
                        <input type="password" id="current_password" name="current_password" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="new_password" class="block text-sm font-medium text-gray-700 mb-1">New Password</label>
                        <input type="password" id="new_password" name="new_password" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1">Confirm New Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                    </div>
                    
                    <div id="password-change-message" class="hidden"></div>
                    
                    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                        <button type="button" onclick="closeChangePasswordModal()" 
                                class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                            Update Password
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
