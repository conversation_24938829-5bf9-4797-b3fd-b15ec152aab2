<?php

namespace App\Models;

use CodeIgniter\Model;

class UserRoleModel extends Model
{
    protected $table = 'user_roles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id', 'school_id', 'role_id', 'created_at', 'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'school_id' => 'required|integer',
        'role_id' => 'required|integer'
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer' => 'User ID must be a valid number'
        ],
        'school_id' => [
            'required' => 'School ID is required',
            'integer' => 'School ID must be a valid number'
        ],
        'role_id' => [
            'required' => 'Role ID is required',
            'integer' => 'Role ID must be a valid number'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get user roles with role details
     */
    public function getUserRoles($userId)
    {
        return $this->select('user_roles.*, roles.name, roles.priority')
                   ->join('roles', 'roles.id = user_roles.role_id')
                   ->where('user_roles.user_id', $userId)
                   ->orderBy('roles.priority', 'ASC')
                   ->findAll();
    }

    /**
     * Get users by role
     */
    public function getUsersByRole($roleId, $schoolId = null)
    {
        $builder = $this->select('user_roles.*, users.name, users.email, users.status')
                       ->join('users', 'users.id = user_roles.user_id')
                       ->where('user_roles.role_id', $roleId)
                       ->where('users.is_deleted', 0);

        if ($schoolId) {
            $builder->where('user_roles.school_id', $schoolId);
        }

        return $builder->findAll();
    }

    /**
     * Assign role to user
     */
    public function assignRole($userId, $schoolId, $roleId)
    {
        // Check if user already has this role
        $existing = $this->where('user_id', $userId)
                        ->where('role_id', $roleId)
                        ->first();

        if ($existing) {
            return $existing['id']; // Already assigned
        }

        return $this->insert([
            'user_id' => $userId,
            'school_id' => $schoolId,
            'role_id' => $roleId
        ]);
    }

    /**
     * Remove role from user
     */
    public function removeRole($userId, $roleId)
    {
        return $this->where('user_id', $userId)
                   ->where('role_id', $roleId)
                   ->delete();
    }

    /**
     * Remove all roles from user
     */
    public function removeAllUserRoles($userId)
    {
        return $this->where('user_id', $userId)->delete();
    }

    /**
     * Update user roles (replace all existing roles)
     */
    public function updateUserRoles($userId, $schoolId, $roleIds)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Remove existing roles
            $this->where('user_id', $userId)->delete();

            // Add new roles
            if (!empty($roleIds)) {
                $insertData = [];
                foreach ($roleIds as $roleId) {
                    $insertData[] = [
                        'user_id' => $userId,
                        'school_id' => $schoolId,
                        'role_id' => $roleId,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
                $this->insertBatch($insertData);
            }

            $db->transComplete();
            return $db->transStatus();

        } catch (\Exception $e) {
            $db->transRollback();
            log_message('error', 'Failed to update user roles: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if user has role
     */
    public function userHasRole($userId, $roleId)
    {
        return $this->where('user_id', $userId)
                   ->where('role_id', $roleId)
                   ->countAllResults() > 0;
    }

    /**
     * Check if user has any of the specified roles
     */
    public function userHasAnyRole($userId, $roleIds)
    {
        if (empty($roleIds)) {
            return false;
        }

        return $this->where('user_id', $userId)
                   ->whereIn('role_id', $roleIds)
                   ->countAllResults() > 0;
    }

    /**
     * Get user's highest priority role
     */
    public function getUserHighestRole($userId)
    {
        return $this->select('user_roles.*, roles.name, roles.priority')
                   ->join('roles', 'roles.id = user_roles.role_id')
                   ->where('user_roles.user_id', $userId)
                   ->orderBy('roles.priority', 'ASC')
                   ->first();
    }

    /**
     * Get users with specific role name
     */
    public function getUsersByRoleName($schoolId, $roleName)
    {
        return $this->select('user_roles.*, users.name, users.email, users.status, roles.name as role_name')
                   ->join('users', 'users.id = user_roles.user_id')
                   ->join('roles', 'roles.id = user_roles.role_id')
                   ->where('user_roles.school_id', $schoolId)
                   ->where('roles.name', $roleName)
                   ->where('users.is_deleted', 0)
                   ->findAll();
    }

    /**
     * Get role statistics for school
     */
    public function getRoleStats($schoolId)
    {
        return $this->select('roles.name, roles.priority, COUNT(user_roles.user_id) as user_count')
                   ->join('roles', 'roles.id = user_roles.role_id')
                   ->join('users', 'users.id = user_roles.user_id')
                   ->where('user_roles.school_id', $schoolId)
                   ->where('users.is_deleted', 0)
                   ->groupBy('roles.id')
                   ->orderBy('roles.priority', 'ASC')
                   ->findAll();
    }

    /**
     * Assign default role to new user
     */
    public function assignDefaultRole($userId, $schoolId, $defaultRoleName = 'Staff')
    {
        // Get the default role
        $roleModel = new RoleModel();
        $role = $roleModel->getRoleByName($schoolId, $defaultRoleName);

        if (!$role) {
            // Create default role if it doesn't exist
            $roleId = $roleModel->insert([
                'school_id' => $schoolId,
                'name' => $defaultRoleName,
                'priority' => 9
            ]);
        } else {
            $roleId = $role['id'];
        }

        return $this->assignRole($userId, $schoolId, $roleId);
    }
}
