<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Login - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="login-card rounded-2xl shadow-2xl p-8 w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-user-tie text-3xl text-indigo-600"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-800">Staff Login</h1>
            <p class="text-gray-600 mt-2">Access your School Question Bank account</p>
        </div>

        <!-- Error/Success Messages -->
        <?php if (session()->getFlashdata('error')): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <?= session()->getFlashdata('error') ?>
            </div>
        <?php endif; ?>

        <?php if (session()->getFlashdata('success')): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <?= session()->getFlashdata('success') ?>
            </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form id="staffLoginForm" method="post" action="<?= site_url('staff/authenticate') ?>" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                <div class="relative">
                    <input type="email" name="email" required
                           class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Enter your email">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                        <i class="fas fa-envelope text-gray-400"></i>
                    </div>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                <div class="relative">
                    <input type="password" name="password" required
                           class="w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                           placeholder="Enter your password">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                        <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <button type="button" onclick="togglePassword()" 
                            class="absolute inset-y-0 right-0 flex items-center pr-4 text-gray-400 hover:text-gray-600">
                        <i id="passwordToggleIcon" class="fas fa-eye"></i>
                    </button>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                    <span class="ml-2 text-sm text-gray-600">Remember me</span>
                </label>
                <a href="#" onclick="openForgotPasswordModal('user')" class="text-sm text-indigo-600 hover:text-indigo-500 hover:underline">Forgot password?</a>
            </div>

            <button type="submit" id="loginBtn"
                    class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300 flex items-center justify-center">
                <i class="fas fa-sign-in-alt mr-2"></i>
                Sign In
            </button>
        </form>

        <!-- Footer -->
        <div class="mt-8 text-center">
            <p class="text-sm text-gray-600">
                Need help? Contact your school administrator
            </p>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <a href="<?= site_url('/') ?>" class="inline-flex items-center text-indigo-600 hover:text-indigo-500 text-sm font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Home
                </a>
            </div>
            <div class="mt-2">
                <p class="text-xs text-gray-500">
                    Access your School Question Bank account as a staff member
                </p>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div id="forgotPasswordModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal Header -->
            <div class="flex justify-between items-center p-6 border-b">
                <h2 class="text-xl font-semibold text-gray-800">
                    <i class="fas fa-key mr-2 text-indigo-600"></i>Staff Password Reset
                </h2>
                <button id="closeForgotPasswordModalBtn" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                    &times;
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-6">
                <!-- Success/Error Messages -->
                <div id="forgotPasswordMessages" class="hidden mb-4"></div>

                <!-- Instructions -->
                <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                        <div class="text-sm text-blue-700">
                            <p class="font-medium mb-1">Password Reset Instructions:</p>
                            <ul class="list-disc list-inside space-y-1 text-xs">
                                <li>Enter your registered email address</li>
                                <li>Check your email for the reset link</li>
                                <li>The link will expire in 1 hour</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Forgot Password Form -->
                <form id="forgotPasswordForm" class="space-y-5">
                    <?= csrf_field() ?>
                    <input type="hidden" name="user_type" value="user">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="email" name="email" required
                                   class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                                   placeholder="Enter your registered email">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <button type="submit" id="forgotPasswordSubmitBtn"
                            class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300 flex items-center justify-center">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Reset Link
                    </button>
                </form>

                <!-- Back to Login -->
                <div class="mt-6 text-center">
                    <button id="backToLoginBtn" class="text-sm text-indigo-600 hover:text-indigo-500 hover:underline">
                        <i class="fas fa-arrow-left mr-1"></i>Back to Login
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="password"]');
            const toggleIcon = document.getElementById('passwordToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Handle form submission with AJAX
        document.getElementById('staffLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const form = this;
            const formData = new FormData(form);
            const loginBtn = document.getElementById('loginBtn');
            const originalText = loginBtn.innerHTML;
            
            // Show loading state
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Signing in...';
            
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showMessage('Login successful! Redirecting...', 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect;
                    }, 1000);
                } else {
                    // Show error message
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('An error occurred. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                loginBtn.disabled = false;
                loginBtn.innerHTML = originalText;
            });
        });

        // Show message function
        function showMessage(message, type) {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.alert-message');
            existingMessages.forEach(msg => msg.remove());
            
            // Create new message
            const alertClass = type === 'error' ? 'bg-red-100 border-red-400 text-red-700' : 'bg-green-100 border-green-400 text-green-700';
            const messageDiv = document.createElement('div');
            messageDiv.className = `alert-message ${alertClass} px-4 py-3 rounded mb-4 border`;
            messageDiv.textContent = message;
            
            // Insert message before form
            const form = document.getElementById('staffLoginForm');
            form.parentNode.insertBefore(messageDiv, form);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }

        // Auto-focus email field
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector('input[name="email"]').focus();
        });

        function openForgotPasswordModal(userType) {
            document.getElementById('forgotPasswordModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            document.getElementById('forgotPasswordForm').reset();
            document.getElementById('forgotPasswordMessages').classList.add('hidden');
        }

        // Close modal functionality
        document.getElementById('closeForgotPasswordModalBtn').addEventListener('click', function() {
            document.getElementById('forgotPasswordModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        });

        document.getElementById('backToLoginBtn').addEventListener('click', function() {
            document.getElementById('forgotPasswordModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
        });

        // Close modal when clicking outside
        document.getElementById('forgotPasswordModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
                document.body.style.overflow = 'auto';
            }
        });

        // Handle form submission
        document.getElementById('forgotPasswordForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = document.getElementById('forgotPasswordSubmitBtn');
            const originalText = submitBtn.innerHTML;

            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';

            fetch('<?= base_url('forgot-password/send') ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                const messagesDiv = document.getElementById('forgotPasswordMessages');
                const isSuccess = data.success;
                const bgColor = isSuccess ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
                const icon = isSuccess ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

                messagesDiv.innerHTML = `
                    <div class="p-4 ${bgColor} border rounded-lg">
                        <div class="flex items-center">
                            <i class="${icon} mr-2"></i>
                            ${data.message}
                        </div>
                    </div>
                `;
                messagesDiv.classList.remove('hidden');

                if (data.success) {
                    this.reset();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const messagesDiv = document.getElementById('forgotPasswordMessages');
                messagesDiv.innerHTML = `
                    <div class="p-4 bg-red-100 border-red-400 text-red-700 border rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            An error occurred. Please try again.
                        </div>
                    </div>
                `;
                messagesDiv.classList.remove('hidden');
            })
            .finally(() => {
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    </script>
</body>
</html>
