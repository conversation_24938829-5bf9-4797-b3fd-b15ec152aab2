-- Create superadmin_users table
CREATE TABLE IF NOT EXISTS `superadmin_users` (
    `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL COMMENT 'Super admin full name',
    `email` varchar(255) NOT NULL COMMENT 'Super admin email address',
    `password` varchar(255) NOT NULL COMMENT 'Hashed password',
    `status` enum('active','inactive') NOT NULL DEFAULT 'active' COMMENT 'Account status',
    `last_login_at` datetime DEFAULT NULL COMMENT 'Last successful login timestamp',
    `login_attempts` int(11) NOT NULL DEFAULT 0 COMMENT 'Failed login attempts counter',
    `locked_until` datetime DEFAULT NULL COMMENT 'Account lock expiry time',
    `created_at` datetime DEFAULT NULL,
    `updated_at` datetime DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `email` (`email`),
    KEY `status` (`status`),
    KEY `last_login_at` (`last_login_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default superadmin user
INSERT INTO `superadmin_users` (`name`, `email`, `password`, `status`, `created_at`, `updated_at`) 
VALUES (
    'Super Administrator', 
    '<EMAIL>', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: 1234
    'active', 
    NOW(), 
    NOW()
) ON DUPLICATE KEY UPDATE 
    `name` = VALUES(`name`),
    `status` = VALUES(`status`),
    `updated_at` = NOW();
