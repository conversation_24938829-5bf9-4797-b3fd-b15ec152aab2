<?php

namespace App\Services;

use App\Models\SchoolModel;
use App\Models\QuestionModel;

class PDFService
{
    protected $schoolModel;
    protected $questionModel;

    public function __construct()
    {
        $this->schoolModel = new SchoolModel();
        $this->questionModel = new QuestionModel();
    }

    /**
     * Generate PDF for question paper
     */
    public function generateQuestionPaperPDF($paperData, $schoolId, $includeAnswers = false)
    {
        // Get school information
        $school = $this->schoolModel->find($schoolId);
        
        // Generate HTML content
        $html = $this->generateQuestionPaperHTML($paperData, $school, $includeAnswers);
        
        // Create PDF using DomPDF
        return $this->createPDF($html, $paperData['title'] ?? 'Question Paper');
    }

    /**
     * Generate HTML content for question paper
     */
    private function generateQuestionPaperHTML($paperData, $school, $includeAnswers = false)
    {
        $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>' . esc($paperData['title'] ?? 'Question Paper') . '</title>
    <style>
        @page {
            margin: 20mm;
            size: A4;
        }
        body { 
            font-family: "Times New Roman", serif; 
            margin: 0; 
            padding: 0; 
            line-height: 1.6; 
            color: #000;
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
            border-bottom: 2px solid #000; 
            padding-bottom: 20px; 
        }
        .school-info { margin-bottom: 15px; }
        .school-name { 
            font-size: 20px; 
            font-weight: bold; 
            color: #000; 
            text-transform: uppercase;
        }
        .school-address { 
            font-size: 12px; 
            color: #333; 
            margin-top: 5px;
        }
        .paper-title { 
            font-size: 18px; 
            font-weight: bold; 
            margin: 15px 0; 
            text-transform: uppercase;
            text-decoration: underline;
        }
        .paper-details { 
            font-size: 12px; 
            color: #000; 
            margin: 10px 0;
        }
        .paper-details table {
            margin: 0 auto;
            border-collapse: collapse;
        }
        .paper-details td {
            padding: 3px 15px;
            border: 1px solid #000;
        }
        .instructions { 
            margin: 20px 0; 
            padding: 15px; 
            border: 1px solid #000; 
            background: #f9f9f9;
        }
        .instructions h3 { 
            margin-top: 0; 
            color: #000; 
            font-size: 14px;
            text-decoration: underline;
        }
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        .instructions li {
            margin: 5px 0;
            font-size: 12px;
        }
        .section { 
            margin: 25px 0; 
            page-break-inside: avoid; 
        }
        .section-header { 
            background: #f0f0f0; 
            padding: 10px; 
            border: 1px solid #000; 
            margin-bottom: 15px; 
            text-align: center;
        }
        .section-title { 
            font-size: 14px; 
            font-weight: bold; 
            margin: 0; 
            text-transform: uppercase;
        }
        .section-info { 
            font-size: 11px; 
            color: #333; 
            margin: 5px 0 0 0; 
        }
        .question { 
            margin: 15px 0; 
            padding: 10px 0; 
            border-bottom: 1px solid #ddd; 
            page-break-inside: avoid;
        }
        .question-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        .question-number { 
            font-weight: bold; 
            color: #000; 
            font-size: 13px;
        }
        .question-marks { 
            background: #000; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 3px; 
            font-size: 11px; 
            font-weight: bold;
        }
        .question-text { 
            margin: 8px 0; 
            font-size: 13px;
            line-height: 1.5;
        }
        .options { 
            margin: 10px 0 10px 20px; 
        }
        .option { 
            margin: 5px 0; 
            font-size: 12px;
        }
        .question-meta { 
            font-size: 10px; 
            color: #666; 
            margin-top: 8px; 
            font-style: italic;
        }
        .answer-section {
            margin-top: 15px;
            padding: 10px;
            background: #f0f8ff;
            border: 1px solid #007cba;
            border-radius: 5px;
        }
        .answer-label {
            font-weight: bold;
            color: #007cba;
            font-size: 12px;
        }
        .answer-text {
            margin-top: 5px;
            font-size: 12px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
        .footer {
            position: fixed;
            bottom: 10mm;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>';

        // Header
        $html .= $this->generateHeader($paperData, $school);
        
        // Instructions
        $html .= $this->generateInstructions($paperData);
        
        // Questions by sections
        $html .= $this->generateQuestions($paperData, $includeAnswers);
        
        // Footer
        $html .= '<div class="footer">
            <p>Generated on ' . date('d/m/Y H:i') . ' | Page <span class="pagenum"></span></p>
        </div>';

        $html .= '</body></html>';

        return $html;
    }

    /**
     * Generate header section
     */
    private function generateHeader($paperData, $school)
    {
        return '<div class="header">
            <div class="school-info">
                <div class="school-name">' . esc($school['name'] ?? 'School Name') . '</div>
                <div class="school-address">' . esc($school['address'] ?? '') . '</div>
            </div>
            <div class="paper-title">' . esc($paperData['title'] ?? 'Question Paper') . '</div>
            <div class="paper-details">
                <table>
                    <tr>
                        <td><strong>Academic Year:</strong> ' . esc($paperData['academicYear'] ?? 'Not specified') . '</td>
                        <td><strong>Exam Type:</strong> ' . esc($paperData['examType'] ?? 'Not specified') . '</td>
                    </tr>
                    <tr>
                        <td><strong>Date:</strong> ' . esc($paperData['examDate'] ?? 'Not specified') . '</td>
                        <td><strong>Duration:</strong> ' . esc($paperData['duration'] ?? '3') . ' Hours</td>
                    </tr>
                    <tr>
                        <td colspan="2"><strong>Maximum Marks:</strong> ' . esc($paperData['totalMarks'] ?? '100') . '</td>
                    </tr>
                </table>
            </div>
        </div>';
    }

    /**
     * Generate instructions section
     */
    private function generateInstructions($paperData)
    {
        $defaultInstructions = [
            'Read all questions carefully before answering.',
            'Answer all questions.',
            'Write your answers in the space provided.',
            'Use blue or black ink pen only.',
            'Mobile phones and electronic devices are not allowed.',
            'Maintain silence in the examination hall.'
        ];

        $customInstructions = !empty($paperData['instructions']) ? 
            explode("\n", $paperData['instructions']) : [];
        
        $instructions = !empty($customInstructions) ? $customInstructions : $defaultInstructions;

        $html = '<div class="instructions">
            <h3>Instructions to Students:</h3>
            <ul>';
        
        foreach ($instructions as $instruction) {
            if (trim($instruction)) {
                $html .= '<li>' . esc(trim($instruction)) . '</li>';
            }
        }
        
        $html .= '</ul></div>';

        return $html;
    }

    /**
     * Generate questions section
     */
    private function generateQuestions($paperData, $includeAnswers = false)
    {
        $html = '';
        
        if (empty($paperData['selectedQuestions'])) {
            return '<div class="section"><p>No questions available.</p></div>';
        }

        // Group questions by section
        $questionsBySection = [];
        foreach ($paperData['selectedQuestions'] as $question) {
            $sectionName = $question['section_name'] ?? 'Part I';
            if (!isset($questionsBySection[$sectionName])) {
                $questionsBySection[$sectionName] = [];
            }
            $questionsBySection[$sectionName][] = $question;
        }

        foreach ($questionsBySection as $sectionName => $questions) {
            $html .= $this->generateSection($sectionName, $questions, $includeAnswers);
        }

        return $html;
    }

    /**
     * Generate individual section
     */
    private function generateSection($sectionName, $questions, $includeAnswers = false)
    {
        $sectionMarks = array_sum(array_column($questions, 'marks'));
        
        $html = '<div class="section">
            <div class="section-header">
                <div class="section-title">' . esc($sectionName) . '</div>
                <div class="section-info">' . count($questions) . ' Questions | ' . $sectionMarks . ' Marks</div>
            </div>';

        foreach ($questions as $index => $question) {
            $html .= $this->generateQuestion($question, $index + 1, $includeAnswers);
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Generate individual question
     */
    private function generateQuestion($question, $questionNumber, $includeAnswers = false)
    {
        $questionText = $question['text'] ?? $question['question_text'] ?? '';
        $marks = $question['marks'] ?? 1;
        
        $html = '<div class="question">
            <div class="question-header">
                <div class="question-number">' . $questionNumber . '.</div>
                <div class="question-marks">' . $marks . ' Mark' . ($marks > 1 ? 's' : '') . '</div>
            </div>
            <div class="question-text">' . esc($questionText) . '</div>';

        // Add options for MCQ questions
        if (isset($question['question_type']) && $question['question_type'] === 'multiple_choice') {
            $html .= $this->generateMCQOptions($question);
        }

        // Add metadata
        if (!empty($question['difficulty']) || !empty($question['chapter'])) {
            $html .= '<div class="question-meta">';
            if (!empty($question['difficulty'])) {
                $html .= 'Difficulty: ' . esc($question['difficulty']) . ' ';
            }
            if (!empty($question['chapter'])) {
                $html .= '| Chapter: ' . esc($question['chapter']);
            }
            $html .= '</div>';
        }

        // Add answers if requested
        if ($includeAnswers) {
            $html .= $this->generateAnswerSection($question);
        }

        $html .= '</div>';
        return $html;
    }

    /**
     * Generate MCQ options
     */
    private function generateMCQOptions($question)
    {
        $html = '<div class="options">';
        
        $options = ['a', 'b', 'c', 'd'];
        foreach ($options as $option) {
            $optionKey = 'option_' . $option;
            if (!empty($question[$optionKey])) {
                $html .= '<div class="option">' . strtoupper($option) . ') ' . esc($question[$optionKey]) . '</div>';
            }
        }
        
        $html .= '</div>';
        return $html;
    }

    /**
     * Generate answer section for answer key
     */
    private function generateAnswerSection($question)
    {
        $html = '<div class="answer-section">
            <div class="answer-label">Answer:</div>
            <div class="answer-text">';
        
        if (isset($question['question_type']) && $question['question_type'] === 'multiple_choice') {
            $correctAnswer = $question['correct_answer'] ?? '';
            $html .= 'Correct Answer: ' . strtoupper($correctAnswer);
        } else {
            $answer = $question['answer'] ?? 'Answer not provided';
            $html .= esc($answer);
        }
        
        $html .= '</div></div>';
        return $html;
    }

    /**
     * Create PDF using DomPDF
     */
    private function createPDF($html, $filename = 'document')
    {
        require_once ROOTPATH . 'vendor/autoload.php';

        $options = new \Dompdf\Options();
        $options->set('defaultFont', 'Times');
        $options->set('isRemoteEnabled', true);
        $options->set('isHtml5ParserEnabled', true);
        $options->set('isPhpEnabled', true);

        $dompdf = new \Dompdf\Dompdf($options);
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();

        return [
            'content' => $dompdf->output(),
            'filename' => $this->sanitizeFilename($filename) . '_' . date('Y-m-d') . '.pdf'
        ];
    }

    /**
     * Sanitize filename for safe download
     */
    private function sanitizeFilename($filename)
    {
        return preg_replace('/[^a-zA-Z0-9_.-]/', '_', $filename);
    }
}
