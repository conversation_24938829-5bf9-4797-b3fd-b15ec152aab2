<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSchoolsTable extends Migration
{
    public function up()
{
    $this->forge->addField([
        'id' => ['type' => 'INT', 'unsigned' => true, 'auto_increment' => true],
        'name' => ['type' => 'VARCHAR', 'constraint' => 255],
        'email' => ['type' => 'VARCHAR', 'constraint' => 255, 'unique' => true],
        'password' => ['type' => 'VARCHAR', 'constraint' => 255],
        'plan_id' => ['type' => 'INT', 'unsigned' => true],
        'address' => ['type' => 'VARCHAR', 'constraint' => 255],
        'phone' => ['type' => 'VARCHAR', 'constraint' => 20],
        'status' => ['type' => 'ENUM', 'constraint' => ['active', 'inactive', 'rejected', 'suspended'], 'default' => 'inactive'],
        'rejection_reason' => ['type' => 'TEXT', 'null' => true],  // ✅ Add this line
        'created_at' => ['type' => 'DATETIME', 'null' => true],
        'updated_at' => ['type' => 'DATETIME', 'null' => true],
        'deleted_at' => ['type' => 'DATETIME', 'null' => true],
    ]);

    $this->forge->addKey('id', true);
    $this->forge->createTable('schools');
}

public function down()
{
    $this->forge->dropTable('schools');
}


   
}
