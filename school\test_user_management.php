<?php
// Test user management functionality
require_once 'vendor/autoload.php';

// Start CodeIgniter
$app = \Config\Services::codeigniter();
$app->initialize();

// Start session
session_start();

// Set superadmin session for testing
$_SESSION['logged_in'] = true;
$_SESSION['role'] = 'superadmin';
$_SESSION['user_id'] = 1;
$_SESSION['email'] = '<EMAIL>';

echo "<h1>🧪 User Management Test</h1>";

// Test database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=school", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✅ Database connected<br>";
    
    // Check if we have users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_deleted = 0");
    $userCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "👥 Total active users: $userCount<br>";
    
    if ($userCount == 0) {
        echo "<br>🔧 Creating test users...<br>";
        
        // Check for active school
        $stmt = $pdo->query("SELECT * FROM schools WHERE status = 'active' LIMIT 1");
        $school = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$school) {
            // Create test school
            $stmt = $pdo->prepare("INSERT INTO schools (name, email, password, phone, address, status, plan_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([
                'Test School',
                '<EMAIL>',
                password_hash('password123', PASSWORD_DEFAULT),
                '+1234567890',
                '123 Test Street',
                'active',
                1
            ]);
            $schoolId = $pdo->lastInsertId();
            echo "🏫 Created test school (ID: $schoolId)<br>";
        } else {
            $schoolId = $school['id'];
            echo "🏫 Using existing school: " . $school['name'] . " (ID: $schoolId)<br>";
        }
        
        // Create test users
        $testUsers = [
            ['name' => 'John Smith', 'email' => '<EMAIL>', 'status' => 'active'],
            ['name' => 'Jane Doe', 'email' => '<EMAIL>', 'status' => 'active'],
            ['name' => 'Bob Wilson', 'email' => '<EMAIL>', 'status' => 'inactive'],
        ];
        
        foreach ($testUsers as $user) {
            $stmt = $pdo->prepare("INSERT INTO users (school_id, name, email, password, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([
                $schoolId,
                $user['name'],
                $user['email'],
                password_hash('password123', PASSWORD_DEFAULT),
                $user['status']
            ]);
            echo "👤 Created user: " . $user['name'] . " (" . $user['status'] . ")<br>";
        }
        
        echo "<br>✅ Test users created successfully!<br>";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
    exit;
}

echo "<br><h2>🔗 Test Links</h2>";
echo "<a href='http://localhost:3000/m-dev.php/superadmin/dashboard' target='_blank'>📊 SuperAdmin Dashboard</a><br>";
echo "<a href='http://localhost:3000/m-dev.php/superadmin/getUsers' target='_blank'>👥 Get Users API</a><br>";
echo "<a href='http://localhost:3000/m-dev.php/test_api.html' target='_blank'>🧪 API Test Page</a><br>";

echo "<br><h2>📋 Instructions</h2>";
echo "1. Go to the SuperAdmin Dashboard<br>";
echo "2. Click on 'User Management' in the sidebar<br>";
echo "3. You should see the test users listed<br>";
echo "4. Try clicking the toggle button to change user status<br>";
echo "5. Try clicking the delete button to soft-delete a user<br>";
echo "6. Check the API Test Page for direct API testing<br>";

echo "<br><h2>🔐 Login Credentials</h2>";
echo "SuperAdmin: <EMAIL> / 1234<br>";

// Display current users
echo "<br><h2>👥 Current Users</h2>";
try {
    $stmt = $pdo->query("SELECT u.*, s.name as school_name FROM users u LEFT JOIN schools s ON s.id = u.school_id WHERE u.is_deleted = 0 ORDER BY u.created_at DESC");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($users) > 0) {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Status</th><th>School</th><th>Created</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . $user['status'] . "</td>";
            echo "<td>" . htmlspecialchars($user['school_name'] ?: 'No School') . "</td>";
            echo "<td>" . $user['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No users found.";
    }
} catch (PDOException $e) {
    echo "Error fetching users: " . $e->getMessage();
}
?>
