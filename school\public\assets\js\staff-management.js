/**
 * Staff Management JavaScript Module
 * Handles all staff-related functionality without page redirects
 */

// Global variables
let currentStaffData = [];
let currentView = 'cards';

// Initialize staff management when section is loaded
function initializeStaffManagement() {
    console.log('Initializing staff management...');
    console.log('BASE_URL:', BASE_URL);

    // Check if required elements exist
    const container = document.getElementById('staff-cards-container');
    const totalStaffElement = document.getElementById('total-staff-count');

    if (!container) {
        console.error('Staff cards container not found!');
        return;
    }

    if (!totalStaffElement) {
        console.error('Staff stats elements not found!');
        return;
    }

    console.log('Required elements found, loading staff data...');

    loadStaffData();
    setupEventListeners();
}

// Setup event listeners
function setupEventListeners() {
    // Search input
    const searchInput = document.getElementById('staff-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterStaff, 300));
    }

    // Filter dropdowns
    const filters = ['department-filter', 'designation-filter', 'status-filter'];
    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.addEventListener('change', filterStaff);
        }
    });

    // CSV file input
    const csvInput = document.getElementById('csv-file');
    if (csvInput) {
        csvInput.addEventListener('change', handleFileSelect);
    }
}

// Load staff data from server
function loadStaffData() {
    const container = document.getElementById('staff-cards-container');

    console.log('Loading staff data...');
    console.log('Request URL:', BASE_URL + 'schooladmin/getStaff');

    // Show loading state
    showLoadingState();

    fetch(BASE_URL + 'schooladmin/getStaff', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            currentStaffData = data.staff || [];
            console.log('Staff data loaded:', currentStaffData.length, 'members');
            displayStaffCards(currentStaffData);
            updateStaffStats(data.stats);
            populateTableView(currentStaffData);
        } else {
            console.error('Server error:', data.message);
            showStaffError('Failed to load staff data: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error loading staff:', error);
        showStaffError('Error loading staff data. Please check your connection.');
    });
}

// Show loading state
function showLoadingState() {
    const container = document.getElementById('staff-cards-container');
    const tableBody = document.getElementById('staff-table-body');
    
    const loadingHTML = `
        <div class="col-span-full text-center py-12">
            <i class="fas fa-spinner fa-spin text-3xl text-indigo-500 mb-4"></i>
            <p class="text-lg font-medium text-gray-700">Loading staff...</p>
            <p class="text-sm text-gray-500">Please wait while we fetch staff data</p>
        </div>
    `;

    if (container) container.innerHTML = loadingHTML;

    if (tableBody) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-12">
                    <i class="fas fa-spinner fa-spin text-3xl text-indigo-500 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700">Loading staff...</p>
                </td>
            </tr>
        `;
    }
}

// Update staff statistics
function updateStaffStats(stats) {
    if (!stats) return;

    const elements = {
        'total-staff-count': stats.total_staff || 0,
        'active-staff-count': stats.active_staff || 0,
        'inactive-staff-count': stats.inactive_staff || 0
    };

    // Calculate teacher and admin counts
    const teacherCount = Math.floor((stats.active_staff || 0) * 0.75);
    const adminCount = (stats.active_staff || 0) - teacherCount;
    
    elements['teacher-count'] = teacherCount;
    elements['admin-staff-count'] = adminCount;
    
    // Update DOM elements
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
}

// Display staff cards
function displayStaffCards(staff) {
    const container = document.getElementById('staff-cards-container');
    if (!container) return;

    if (!staff || staff.length === 0) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-users text-3xl text-gray-400 mb-4"></i>
                <p class="text-lg font-medium text-gray-700">No staff members found</p>
                <p class="text-sm text-gray-500">Add staff members to get started</p>
                <button onclick="showUserModal()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                    <i class="fas fa-plus mr-2"></i>Add First Staff Member
                </button>
            </div>
        `;
        return;
    }

    container.innerHTML = staff.map(member => {
        const initials = getInitials(member.name);
        const color = getColorForUser(member.id);
        const joinDate = formatDate(member.created_at);
        
        return `
            <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all card-hover" data-staff-id="${member.id}">
                <div class="flex items-center space-x-4 mb-4">
                    <div class="w-12 h-12 bg-${color}-100 rounded-full flex items-center justify-center">
                        <span class="text-${color}-600 font-bold text-lg">${initials}</span>
                    </div>
                    <div class="flex-1">
                        <h5 class="font-semibold text-gray-800">${member.name}</h5>
                        <p class="text-sm text-gray-600">${member.designation || 'Staff Member'}</p>
                        <span class="inline-block bg-${getStatusColor(member.status)}-100 text-${getStatusColor(member.status)}-800 px-2 py-1 rounded-full text-xs font-medium mt-1">
                            ${getStatusText(member.status)}
                        </span>
                    </div>
                </div>
                <div class="space-y-2 text-sm text-gray-600 mb-4">
                    <p><i class="fas fa-envelope mr-2 text-gray-400"></i>${member.email}</p>
                    <p><i class="fas fa-phone mr-2 text-gray-400"></i>${member.phone || 'Not provided'}</p>
                    <p><i class="fas fa-calendar mr-2 text-gray-400"></i>Joined: ${joinDate}</p>
                </div>
                <div class="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div class="text-center">
                        <p class="text-lg font-bold text-indigo-600">${member.question_count || 0}</p>
                        <p class="text-xs text-gray-600">Questions</p>
                    </div>
                    <div class="text-center">
                        <p class="text-lg font-bold text-green-600">${member.approval_rate || 0}%</p>
                        <p class="text-xs text-gray-600">Approval</p>
                    </div>
                    <div class="flex space-x-1">
                        <button onclick="viewStaffProfile(${member.id})" class="text-indigo-600 hover:text-indigo-800 p-2 hover:bg-indigo-50 rounded" title="View Profile">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editStaffMember(${member.id})" class="text-green-600 hover:text-green-800 p-2 hover:bg-green-50 rounded" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleStaffStatus(${member.id})" class="text-yellow-600 hover:text-yellow-800 p-2 hover:bg-yellow-50 rounded" title="Toggle Status">
                            <i class="fas fa-user-slash"></i>
                        </button>
                        <button onclick="deleteStaffMember(${member.id})" class="text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Populate table view
function populateTableView(staff) {
    const tableBody = document.getElementById('staff-table-body');
    if (!tableBody) return;

    if (!staff || staff.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center py-12">
                    <i class="fas fa-users text-3xl text-gray-400 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700">No staff members found</p>
                    <button onclick="showUserModal()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Add Staff Member
                    </button>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = staff.map(member => {
        const initials = getInitials(member.name);
        const color = getColorForUser(member.id);
        
        return `
            <tr class="hover:bg-gray-50" data-staff-id="${member.id}">
                <td class="py-4 px-6">
                    <input type="checkbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                </td>
                <td class="py-4 px-6">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-${color}-100 rounded-full flex items-center justify-center">
                            <span class="text-${color}-600 font-bold">${initials}</span>
                        </div>
                        <div>
                            <p class="font-semibold text-gray-800">${member.name}</p>
                            <p class="text-sm text-gray-600">${member.email}</p>
                        </div>
                    </div>
                </td>
                <td class="py-4 px-6">
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">${member.designation || 'Staff'}</span>
                </td>
                <td class="py-4 px-6 text-gray-700">${member.department || 'General'}</td>
                <td class="py-4 px-6 text-gray-700">${member.phone || 'N/A'}</td>
                <td class="py-4 px-6">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-600">${member.question_count || 0} Questions</span>
                        <span class="text-sm text-green-600 font-medium">${member.approval_rate || 0}% Approval</span>
                    </div>
                </td>
                <td class="py-4 px-6">
                    <span class="bg-${getStatusColor(member.status)}-100 text-${getStatusColor(member.status)}-800 px-2 py-1 rounded-full text-xs font-medium">
                        ${getStatusText(member.status)}
                    </span>
                </td>
                <td class="py-4 px-6">
                    <div class="flex space-x-1">
                        <button onclick="viewStaffProfile(${member.id})" class="text-indigo-600 hover:text-indigo-800 p-1" title="View">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="editStaffMember(${member.id})" class="text-green-600 hover:text-green-800 p-1" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="toggleStaffStatus(${member.id})" class="text-yellow-600 hover:text-yellow-800 p-1" title="Toggle Status">
                            <i class="fas fa-user-slash"></i>
                        </button>
                        <button onclick="deleteStaffMember(${member.id})" class="text-red-600 hover:text-red-800 p-1" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Show error state
function showStaffError(message) {
    const container = document.getElementById('staff-cards-container');
    if (container) {
        container.innerHTML = `
            <div class="col-span-full text-center py-12">
                <i class="fas fa-exclamation-triangle text-3xl text-red-500 mb-4"></i>
                <p class="text-lg font-medium text-gray-700">${message}</p>
                <button onclick="loadStaffData()" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Retry
                </button>
            </div>
        `;
    }
}

// Toggle between cards and table view
function toggleStaffView(viewType) {
    const cardsView = document.getElementById('staff-cards-view');
    const tableView = document.getElementById('staff-table-view');
    const cardBtn = document.getElementById('card-view-btn');
    const tableBtn = document.getElementById('table-view-btn');

    if (viewType === 'cards') {
        cardsView?.classList.remove('hidden');
        tableView?.classList.add('hidden');
        cardBtn?.classList.add('bg-indigo-100', 'text-indigo-700');
        cardBtn?.classList.remove('text-gray-600', 'hover:bg-gray-100');
        tableBtn?.classList.remove('bg-indigo-100', 'text-indigo-700');
        tableBtn?.classList.add('text-gray-600', 'hover:bg-gray-100');
    } else {
        tableView?.classList.remove('hidden');
        cardsView?.classList.add('hidden');
        tableBtn?.classList.add('bg-indigo-100', 'text-indigo-700');
        tableBtn?.classList.remove('text-gray-600', 'hover:bg-gray-100');
        cardBtn?.classList.remove('bg-indigo-100', 'text-indigo-700');
        cardBtn?.classList.add('text-gray-600', 'hover:bg-gray-100');
    }
    
    currentView = viewType;
}

// Filter staff based on search and filter criteria
function filterStaff() {
    const searchTerm = document.getElementById('staff-search')?.value.toLowerCase() || '';
    const departmentFilter = document.getElementById('department-filter')?.value || '';
    const designationFilter = document.getElementById('designation-filter')?.value || '';
    const statusFilter = document.getElementById('status-filter')?.value || '';

    const filteredStaff = currentStaffData.filter(member => {
        const matchesSearch = member.name.toLowerCase().includes(searchTerm) ||
                             member.email.toLowerCase().includes(searchTerm);
        const matchesDepartment = !departmentFilter || (member.department || '').includes(departmentFilter);
        const matchesDesignation = !designationFilter || (member.designation || '').includes(designationFilter);
        const matchesStatus = !statusFilter || member.status === statusFilter;

        return matchesSearch && matchesDepartment && matchesDesignation && matchesStatus;
    });

    if (currentView === 'cards') {
        displayStaffCards(filteredStaff);
    } else {
        populateTableView(filteredStaff);
    }
}

// Clear all filters
function clearStaffFilters() {
    document.getElementById('staff-search').value = '';
    document.getElementById('department-filter').value = '';
    document.getElementById('designation-filter').value = '';
    document.getElementById('status-filter').value = '';

    if (currentView === 'cards') {
        displayStaffCards(currentStaffData);
    } else {
        populateTableView(currentStaffData);
    }
}

// Export staff data
function exportStaff() {
    showNotification('Preparing staff export...', 'info');

    // Create CSV content
    const headers = ['Name', 'Email', 'Designation', 'Phone', 'Status', 'Questions', 'Approval Rate'];
    const csvContent = [
        headers.join(','),
        ...currentStaffData.map(member => [
            `"${member.name}"`,
            `"${member.email}"`,
            `"${member.designation || 'Staff'}"`,
            `"${member.phone || 'N/A'}"`,
            `"${getStatusText(member.status)}"`,
            member.question_count || 0,
            `${member.approval_rate || 0}%`
        ].join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `staff_export_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    showNotification('Staff data exported successfully!', 'success');
}

// Modal functions
function showUserModal() {
    document.getElementById('userModal')?.classList.remove('hidden');
}

function hideUserModal() {
    document.getElementById('userModal')?.classList.add('hidden');
    document.getElementById('addUserForm')?.reset();
}

function showBulkImportModal() {
    document.getElementById('bulkImportModal')?.classList.remove('hidden');
}

function hideBulkImportModal() {
    document.getElementById('bulkImportModal')?.classList.add('hidden');
}

// Submit user form
function submitUserForm(event) {
    event.preventDefault();

    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);

    // Convert FormData to regular object
    const userData = {};
    formData.forEach((value, key) => {
        userData[key] = value;
    });

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding...';
    submitBtn.disabled = true;

    fetch(BASE_URL + 'schooladmin/addUser', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams(userData)
    })
    .then(response => response.json())
    .then(data => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (data.success) {
            showNotification('Staff member added successfully!', 'success');
            hideUserModal();
            loadStaffData(); // Refresh the staff list
        } else {
            if (data.errors) {
                let errorMsg = 'Validation errors:\n';
                Object.keys(data.errors).forEach(field => {
                    errorMsg += `${field}: ${data.errors[field]}\n`;
                });
                showNotification(errorMsg, 'error');
            } else {
                showNotification(data.message || 'Failed to add staff member', 'error');
            }
        }
    })
    .catch(error => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        console.error('Error:', error);
        showNotification('An error occurred while adding the staff member', 'error');
    });
}

// Staff action functions
function viewStaffProfile(staffId) {
    const staff = currentStaffData.find(s => s.id == staffId);
    if (!staff) {
        showNotification('Staff member not found', 'error');
        return;
    }

    // Fetch detailed staff information
    fetch(BASE_URL + `schooladmin/getStaffMember/${staffId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStaffProfileModal(data.staff);
        } else {
            showNotification(data.message || 'Failed to load staff profile', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error loading staff profile', 'error');
    });
}

function editStaffMember(staffId) {
    const staff = currentStaffData.find(s => s.id == staffId);
    if (!staff) {
        showNotification('Staff member not found', 'error');
        return;
    }

    // Fetch detailed staff information for editing
    fetch(BASE_URL + `schooladmin/getStaffMember/${staffId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showEditStaffModal(data.staff);
        } else {
            showNotification(data.message || 'Failed to load staff data', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error loading staff data', 'error');
    });
}

function toggleStaffStatus(staffId) {
    const staff = currentStaffData.find(s => s.id == staffId);
    if (!staff) {
        showNotification('Staff member not found', 'error');
        return;
    }

    const newStatus = staff.status === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'activate' : 'deactivate';

    if (confirm(`Are you sure you want to ${action} ${staff.name}?`)) {
        // Show loading state
        const statusButtons = document.querySelectorAll(`[onclick="toggleStaffStatus(${staffId})"]`);
        statusButtons.forEach(btn => {
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            btn.disabled = true;
        });

        fetch(BASE_URL + `schooladmin/toggleStaffStatus/${staffId}`, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                // Update local data
                staff.status = data.new_status;
                // Refresh the display
                loadStaffData();
            } else {
                showNotification(data.message || 'Failed to update staff status', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error updating staff status', 'error');
        })
        .finally(() => {
            // Reset button state
            statusButtons.forEach(btn => {
                btn.innerHTML = '<i class="fas fa-user-slash"></i>';
                btn.disabled = false;
            });
        });
    }
}

// Delete staff member
function deleteStaffMember(staffId) {
    const staff = currentStaffData.find(s => s.id == staffId);
    if (!staff) {
        showNotification('Staff member not found', 'error');
        return;
    }

    if (confirm(`Are you sure you want to delete ${staff.name}? This action cannot be undone.`)) {
        fetch(BASE_URL + `schooladmin/deleteStaffMember/${staffId}`, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                loadStaffData(); // Refresh the staff list
            } else {
                showNotification(data.message || 'Failed to delete staff member', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error deleting staff member', 'error');
        });
    }
}

// Show staff profile modal
function showStaffProfileModal(staff) {
    const modal = document.createElement('div');
    modal.id = 'staffProfileModal';
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';

    const statusColor = staff.status === 'active' ? 'green' : 'red';
    const statusText = staff.status === 'active' ? 'Active' : 'Inactive';

    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Staff Profile</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="closeStaffProfileModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <div class="flex items-center space-x-4 mb-6">
                    <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center">
                        <span class="text-indigo-600 font-bold text-xl">${getInitials(staff.name)}</span>
                    </div>
                    <div>
                        <h4 class="text-xl font-bold text-gray-800">${staff.name}</h4>
                        <p class="text-gray-600">${staff.designation}</p>
                        <span class="inline-block bg-${statusColor}-100 text-${statusColor}-800 px-2 py-1 rounded-full text-xs font-medium mt-1">
                            ${statusText}
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-4">
                        <h5 class="font-semibold text-gray-800 border-b pb-2">Contact Information</h5>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Email</label>
                            <p class="text-gray-800">${staff.email}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Phone</label>
                            <p class="text-gray-800">${staff.phone || 'Not provided'}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Department</label>
                            <p class="text-gray-800">${staff.department || 'Not assigned'}</p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <h5 class="font-semibold text-gray-800 border-b pb-2">Performance</h5>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Questions Created</label>
                            <p class="text-gray-800">${staff.question_count || 0}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Approval Rate</label>
                            <p class="text-gray-800">${staff.approval_rate || 0}%</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-600">Joined Date</label>
                            <p class="text-gray-800">${new Date(staff.created_at).toLocaleDateString()}</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end space-x-3">
                    <button onclick="closeStaffProfileModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        Close
                    </button>
                    <button onclick="closeStaffProfileModal(); editStaffMember(${staff.id})" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        <i class="fas fa-edit mr-2"></i>Edit Profile
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function closeStaffProfileModal() {
    const modal = document.getElementById('staffProfileModal');
    if (modal) {
        modal.remove();
    }
}

// Show edit staff modal
function showEditStaffModal(staff) {
    const modal = document.createElement('div');
    modal.id = 'editStaffModal';
    modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';

    modal.innerHTML = `
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Edit Staff Member</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="closeEditStaffModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6">
                <form id="editStaffForm" onsubmit="submitEditStaffForm(event, ${staff.id})">
                    <div class="space-y-4">
                        <div>
                            <label for="editFullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                            <input type="text" id="editFullName" name="name" required
                                   value="${staff.name}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div>
                            <label for="editEmail" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                            <input type="email" id="editEmail" name="email" required
                                   value="${staff.email}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                        <div>
                            <label for="editDesignation" class="block text-sm font-medium text-gray-700 mb-1">Designation</label>
                            <select id="editDesignation" name="designation"
                                    class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="">Select Designation</option>
                                <option value="Principal" ${staff.designation === 'Principal' ? 'selected' : ''}>Principal</option>
                                <option value="Vice Principal" ${staff.designation === 'Vice Principal' ? 'selected' : ''}>Vice Principal</option>
                                <option value="HOD" ${staff.designation === 'HOD' ? 'selected' : ''}>HOD</option>
                                <option value="Teacher" ${staff.designation === 'Teacher' ? 'selected' : ''}>Teacher</option>
                                <option value="Assistant Teacher" ${staff.designation === 'Assistant Teacher' ? 'selected' : ''}>Assistant Teacher</option>
                                <option value="Admin Staff" ${staff.designation === 'Admin Staff' ? 'selected' : ''}>Admin Staff</option>
                            </select>
                        </div>
                        <div>
                            <label for="editPhone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input type="tel" id="editPhone" name="phone"
                                   value="${staff.phone || ''}"
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                   placeholder="+91 98765 43210">
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" onclick="closeEditStaffModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                            Update Staff Member
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function closeEditStaffModal() {
    const modal = document.getElementById('editStaffModal');
    if (modal) {
        modal.remove();
    }
}

// Submit edit staff form
function submitEditStaffForm(event, staffId) {
    event.preventDefault();

    const form = document.getElementById('editStaffForm');
    const formData = new FormData(form);

    // Convert FormData to regular object
    const staffData = {};
    formData.forEach((value, key) => {
        staffData[key] = value;
    });

    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
    submitBtn.disabled = true;

    fetch(BASE_URL + `schooladmin/updateStaffMember/${staffId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams(staffData)
    })
    .then(response => response.json())
    .then(data => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (data.success) {
            showNotification('Staff member updated successfully!', 'success');
            closeEditStaffModal();
            loadStaffData(); // Refresh the staff list
        } else {
            if (data.errors) {
                let errorMsg = 'Validation errors:\n';
                Object.keys(data.errors).forEach(field => {
                    errorMsg += `${field}: ${data.errors[field]}\n`;
                });
                showNotification(errorMsg, 'error');
            } else {
                showNotification(data.message || 'Failed to update staff member', 'error');
            }
        }
    })
    .catch(error => {
        // Reset button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        console.error('Error:', error);
        showNotification('An error occurred while updating the staff member', 'error');
    });
}

// Export staff data
function exportStaff() {
    if (!currentStaffData || currentStaffData.length === 0) {
        showNotification('No staff data to export', 'warning');
        return;
    }

    // Create CSV content
    const headers = ['Name', 'Email', 'Phone', 'Designation', 'Department', 'Status', 'Questions Created', 'Approval Rate', 'Joined Date'];
    const csvContent = [
        headers.join(','),
        ...currentStaffData.map(staff => [
            `"${staff.name}"`,
            `"${staff.email}"`,
            `"${staff.phone || ''}"`,
            `"${staff.designation || ''}"`,
            `"${staff.department || ''}"`,
            `"${staff.status}"`,
            staff.question_count || 0,
            staff.approval_rate || 0,
            `"${new Date(staff.created_at).toLocaleDateString()}"`
        ].join(','))
    ].join('\n');

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `staff_data_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('Staff data exported successfully', 'success');
}

// Clear all filters
function clearStaffFilters() {
    document.getElementById('staff-search').value = '';
    document.getElementById('department-filter').value = '';
    document.getElementById('designation-filter').value = '';
    document.getElementById('status-filter').value = '';

    filterStaff();
    showNotification('Filters cleared', 'info');
}

// Bulk import functionality placeholder
function showBulkImportModal() {
    const modal = document.getElementById('bulkImportModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

function hideBulkImportModal() {
    const modal = document.getElementById('bulkImportModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

function processBulkImport() {
    const fileInput = document.getElementById('csv-file');
    const file = fileInput.files[0];

    if (!file) {
        showNotification('Please select a CSV file', 'warning');
        return;
    }

    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        showNotification('Please select a valid CSV file', 'error');
        return;
    }

    // For now, show a placeholder message
    showNotification('Bulk import functionality will be implemented in the next phase', 'info');
    hideBulkImportModal();
}

// Bulk import functions
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file && file.type === 'text/csv') {
        showNotification(`Selected file: ${file.name}`, 'info');
    } else {
        showNotification('Please select a valid CSV file', 'error');
        event.target.value = '';
    }
}

function processBulkImport() {
    const fileInput = document.getElementById('csv-file');
    const file = fileInput.files[0];

    if (!file) {
        showNotification('Please select a CSV file first', 'error');
        return;
    }

    showNotification('Processing bulk import...', 'info');

    // Simulate processing
    setTimeout(() => {
        showNotification('Bulk import completed successfully!', 'success');
        hideBulkImportModal();
        loadStaffData(); // Refresh the staff list
    }, 2000);
}

// Utility functions
function getInitials(name) {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
}

function getColorForUser(id) {
    const colors = ['indigo', 'purple', 'blue', 'green', 'yellow', 'red', 'pink', 'orange'];
    return colors[id % colors.length];
}

function getStatusColor(status) {
    switch (status) {
        case 'active': return 'green';
        case 'inactive': return 'red';
        case 'on-leave': return 'yellow';
        case 'suspended': return 'red';
        default: return 'gray';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'active': return 'Active';
        case 'inactive': return 'Inactive';
        case 'on-leave': return 'On Leave';
        case 'suspended': return 'Suspended';
        default: return 'Unknown';
    }
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Show notification function (if not already defined globally)
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        type === 'warning' ? 'bg-yellow-500 text-white' :
        'bg-blue-500 text-white'
    }`;

    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                type === 'warning' ? 'fa-exclamation-triangle' :
                'fa-info-circle'
            } mr-3"></i>
            <span class="font-medium">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}
