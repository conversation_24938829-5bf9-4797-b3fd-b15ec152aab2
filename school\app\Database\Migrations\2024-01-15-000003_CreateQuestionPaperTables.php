<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateQuestionPaperTables extends Migration
{
    public function up()
    {
        // Create question_papers table
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'title' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'standard' => [
                'type' => 'VARCHAR',
                'constraint' => 10,
            ],
            'subject' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
            ],
            'duration' => [
                'type' => 'DECIMAL',
                'constraint' => '3,1',
                'default' => 3.0,
            ],
            'total_marks' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 100,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['draft', 'published', 'archived'],
                'default' => 'draft',
            ],
            'academic_year' => [
                'type' => 'VARCHAR',
                'constraint' => 20,
                'null' => true,
            ],
            'exam_type' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
            ],
            'exam_date' => [
                'type' => 'DATE',
                'null' => true,
            ],
            'instructions' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'created_by' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('school_id');
        $this->forge->addKey('created_by');
        $this->forge->createTable('question_papers');

        // Create paper_questions table (junction table)
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'paper_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'question_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'question_order' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 1,
            ],
            'marks' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 1,
            ],
            'section_name' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'comment' => 'Section name for organizing questions (e.g., Part A, Part B)',
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey('paper_id');
        $this->forge->addKey('question_id');
        $this->forge->createTable('paper_questions');
    }

    public function down()
    {
        $this->forge->dropTable('paper_questions');
        $this->forge->dropTable('question_papers');
    }
}
