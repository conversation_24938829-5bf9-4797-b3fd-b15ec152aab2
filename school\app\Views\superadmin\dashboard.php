<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?= csrf_hash() ?>">
    <title>Super Admin Dashboard QuestionBank Pro</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stats-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
        .stats-card-2 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
        .stats-card-3 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
        .stats-card-4 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
        .nav-item { transition: all 0.3s ease; }
        .nav-item:hover { background: linear-gradient(90deg, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%); }
        .nav-item.active { background: linear-gradient(90deg, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0.05) 100%); border-right: 4px solid #6366f1; }
        .animate-fade-in { animation: fadeIn 0.5s ease-in-out; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .notification-badge { animation: pulse 2s infinite; }
        @keyframes pulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.1); } }

        /* Error styling for form inputs */
        .border-red-500 {
            border-color: #ef4444 !important;
            box-shadow: 0 0 0 1px #ef4444;
        }

        .error-message {
            font-size: 0.875rem;
            line-height: 1.25rem;
        }

        /* Modal animations */
        .modal-enter {
            animation: modalEnter 0.3s ease-out;
        }

        @keyframes modalEnter {
            from {
                opacity: 0;
                transform: scale(0.95);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        .status-active {
            background-color: rgb(220 252 231);
            color: rgb(22 101 52);
        }

        .status-trial {
            background-color: rgb(254 249 195);
            color: rgb(133 77 14);
        }

        .status-expired {
            background-color: rgb(254 226 226);
            color: rgb(153 27 27);
        }

        .status-suspended {
            background-color: rgb(243 244 246);
            color: rgb(55 65 81);
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body class="bg-gray-50">
    <!-- Enhanced Sidebar -->
    <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl sidebar-transition flex flex-col">
        <!-- Super Admin Header -->
        <div class="gradient-bg h-24 flex items-center justify-center relative flex-shrink-0 py-4">
            <div class="absolute inset-0 bg-black opacity-10"></div>
            <div class="relative text-center px-4 w-full">
                <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-crown text-white text-xl"></i>
                </div>
                <h1 class="text-white text-sm font-bold leading-tight">Super Admin</h1>
                <p class="text-white text-xs opacity-90 leading-tight mt-1">System Control</p>
            </div>
        </div>
        <!-- Navigation Menu -->
        <nav class="mt-6 px-3 flex-grow overflow-y-auto">
            <div class="space-y-1">
                <a href="#" onclick="showSection('dashboard')"
                    class="nav-item active flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-tachometer-alt text-indigo-600 text-sm"></i>
                    </div>
                    <span>Dashboard</span>
                </a>
                <a href="#" onclick="showSection('schools')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-school text-blue-600 text-sm"></i>
                    </div>
                    <span>Schools Management</span>
                </a>
                <a href="#" onclick="showSection('users')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-users text-green-600 text-sm"></i>
                    </div>
                    <span>User Management</span>
                </a>
                <a href="#" onclick="showSection('subscriptions')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-credit-card text-orange-600 text-sm"></i>
                    </div>
                    <span>Subscriptions</span>
                </a>
                <a href="#" onclick="showSection('analytics')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-chart-bar text-purple-600 text-sm"></i>
                    </div>
                    <span>Analytics</span>
                </a>
                <a href="#" onclick="showSection('system')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cog text-gray-600 text-sm"></i>
                    </div>
                    <span>System Settings</span>
                </a>
                <a href="#" onclick="showSection('audit')"
                    class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-history text-yellow-600 text-sm"></i>
                    </div>
                    <span>Audit Logs</span>
                </a>

            </div>
        </nav>
        <!-- Logout Button at Bottom -->
        <div class="px-3 pb-4">
            <a href="<?= site_url('logout') ?>" class="nav-item flex items-center px-4 py-3 text-red-600 rounded-lg font-medium hover:bg-red-50 transition-colors">
                <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-sign-out-alt text-red-600 text-sm"></i>
                </div>
                <span>Logout</span>
            </a>
        </div>

        <!-- User Profile Section -->
        <div class="p-4 border-t border-gray-200 flex-shrink-0">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm">SA</span>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">Super Admin</p>
                    <p class="text-xs text-gray-500 truncate">System Administrator</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Enhanced Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <button class="lg:hidden text-gray-600 hover:text-gray-900 p-2 rounded-lg hover:bg-gray-100" onclick="toggleSidebar()">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <div class="ml-4">
                        <h2 id="pageTitle" class="text-2xl font-bold text-gray-800">Dashboard Overview</h2>
                        <p id="pageSubtitle" class="text-sm text-gray-600 mt-1">Welcome back, manage your entire platform from here</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button id="notificationBtn" class="p-2 text-gray-400 hover:text-gray-600 relative rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="fas fa-bell text-xl"></i>
                            <?php if ($notificationCount > 0): ?>
                                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center notification-badge">
                                    <?= $notificationCount > 99 ? '99+' : $notificationCount ?>
                                </span>
                            <?php endif; ?>
                        </button>

                        <!-- Notification Dropdown -->
                        <div id="notificationDropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <div class="p-4 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                                    <button id="refreshNotifications" class="text-indigo-600 hover:text-indigo-800 text-sm">
                                        <i class="fas fa-sync-alt"></i> Refresh
                                    </button>
                                </div>
                            </div>
                            <div id="notificationList" class="max-h-96 overflow-y-auto">
                                <div class="p-4 text-center text-gray-500">
                                    <i class="fas fa-spinner fa-spin"></i> Loading notifications...
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-8 h-8 gradient-bg rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-sm">SA</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Content -->
        <main class="p-6">
            <!-- Flash Messages -->
            <?php if (session()->getFlashdata('success')): ?>
                <div class="bg-green-100 text-green-800 px-4 py-3 rounded-lg mb-6 border border-green-200">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <?= session()->getFlashdata('success') ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="bg-red-100 text-red-800 px-4 py-3 rounded-lg mb-6 border border-red-200">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <?= session()->getFlashdata('error') ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section active">
                <!-- Enhanced Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-fade-in">
                    <div class="stats-card card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden cursor-pointer transition-transform duration-200 hover:scale-105" onclick="showSection('schools')">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Schools</p>
                                    <p class="text-3xl font-bold text-white"><?= count($schools ?? []) ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <i class="fa fa-arrow-up mr-1"></i>Registered schools
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-school text-2xl text-white"></i>
                                </div>
                            </div>
                            <div class="mt-3 text-white text-opacity-70 text-xs">
                                <i class="fas fa-mouse-pointer mr-1"></i>Click to manage schools
                            </div>
                        </div>
                    </div>
                    <div class="stats-card-2 card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden cursor-pointer transition-transform duration-200 hover:scale-105" onclick="showSection('users')">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Active Users</p>
                                    <p class="text-3xl font-bold text-white"><?= $totalUsers ?? 0 ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <i class="fa fa-arrow-up mr-1"></i>Total registered users
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-users text-2xl text-white"></i>
                                </div>
                            </div>
                            <div class="mt-3 text-white text-opacity-70 text-xs">
                                <i class="fas fa-mouse-pointer mr-1"></i>Click to manage users
                            </div>
                        </div>
                    </div>
                    <div class="stats-card-3 card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden cursor-pointer transition-transform duration-200 hover:scale-105" onclick="showSection('analytics')">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Total Questions</p>
                                    <p class="text-3xl font-bold text-white"><?= number_format($totalQuestions) ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <i class="fa fa-question-circle mr-1"></i>Across all schools
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fa fa-question-circle text-2xl text-white"></i>
                                </div>
                            </div>
                            <div class="mt-3 text-white text-opacity-70 text-xs">
                                <i class="fas fa-mouse-pointer mr-1"></i>Click to view analytics
                            </div>
                        </div>
                    </div>
                    <div class="stats-card-4 card-hover p-6 rounded-xl shadow-lg text-white relative overflow-hidden cursor-pointer transition-transform duration-200 hover:scale-105" onclick="showSection('subscriptions')">
                        <div class="absolute top-0 right-0 w-20 h-20 bg-white bg-opacity-10 rounded-full -mr-10 -mt-10"></div>
                        <div class="relative">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-white text-opacity-80 text-sm">Monthly Revenue</p>
                                    <p class="text-3xl font-bold text-white">₹<?= number_format($monthlyRevenue, 2) ?></p>
                                    <p class="text-white text-opacity-90 text-sm mt-1">
                                        <?php if ($revenueGrowth > 0): ?>
                                            <i class="fa fa-arrow-up mr-1"></i><?= $revenueGrowth ?>% from last month
                                        <?php elseif ($revenueGrowth < 0): ?>
                                            <i class="fa fa-arrow-down mr-1"></i><?= abs($revenueGrowth) ?>% from last month
                                        <?php else: ?>
                                            <i class="fa fa-minus mr-1"></i>No change from last month
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fa fa-dollar-sign text-2xl text-white"></i>
                                </div>
                            </div>
                            <div class="mt-3 text-white text-opacity-70 text-xs">
                                <i class="fas fa-mouse-pointer mr-1"></i>Click to view subscriptions
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Charts Row -->
                <div class="grid lg:grid-cols-2 gap-6 mb-8 animate-fade-in">
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">School Growth Trend</h3>
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-line text-blue-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="relative h-[250px]">
                            <canvas id="schoolGrowthChart"></canvas>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-xl shadow-lg card-hover border border-gray-100">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-800">Subscription Distribution</h3>
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-chart-pie text-purple-600 text-sm"></i>
                            </div>
                        </div>
                        <div class="relative h-[250px]">
                            <canvas id="subscriptionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Recent Activity -->
                <div class="bg-white rounded-xl shadow-lg card-hover border border-gray-100 animate-fade-in">
                    <div class="p-6 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-clock text-gray-600 text-sm"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">Recent Activity</h3>
                            </div>
                            <button onclick="loadRecentActivities()" class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg text-sm">
                                <i class="fas fa-refresh mr-1"></i>Refresh
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div id="recent-activities-list" class="space-y-4">
                            <div class="text-center py-8 text-gray-500">
                                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                                <p>Loading recent activities...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Schools Section -->
            <div id="schools-section" class="section hidden animate-fade-in">
                <div class="bg-white rounded-xl shadow-lg border border-gray-100">
                    <div class="p-6 border-b border-gray-100 flex justify-between items-center">
                        <div>
                            <div class="flex items-center mb-3">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-school text-blue-600 text-sm"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-800">Schools Management</h3>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="showSchoolTab('all')" class="px-4 py-2 rounded-lg border-2 border-indigo-600 text-indigo-600 hover:bg-indigo-50 transition-colors font-medium">All Schools</button>
                                <button onclick="showSchoolTab('pending')" class="px-4 py-2 rounded-lg border-2 border-yellow-600 text-yellow-600 hover:bg-yellow-50 transition-colors font-medium">Pending Approval</button>
                                <button onclick="showSchoolTab('active')" class="px-4 py-2 rounded-lg border-2 border-green-600 text-green-600 hover:bg-green-50 transition-colors font-medium">Active</button>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <div class="relative">
                                <input type="text" id="school-search" placeholder="Search schools..."
                                    class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-colors">
                                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                            </div>
                            <button onclick="openAddSchoolModal()"
                                class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-md hover:shadow-lg">
                                <i class="fa fa-plus mr-2"></i>Add New School
                            </button>
                        </div>
                    </div>

                    <!-- Enhanced Schools Table -->
                    <div id="schools-table" class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gradient-to-r from-gray-50 to-gray-100">
                                <tr>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">School Name</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Email</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Phone</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Address</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Status</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Registered At</th>
                                    <th class="px-6 py-4 font-semibold text-xs uppercase text-gray-700 tracking-wider text-left">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="schools-tbody" class="divide-y divide-gray-200 bg-white">
                                <!-- Dynamic content will be loaded here -->
                                <tr class="hover:bg-gray-50 transition-colors">
                                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                                        <div class="flex items-center justify-center">
                                            <i class="fas fa-spinner fa-spin mr-2 text-indigo-600"></i>
                                            <span>Loading schools...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Add School Modal -->
            <div id="addSchoolModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                    <!-- Modal Header -->
                    <div class="flex justify-between items-center p-6 border-b">
                        <h2 class="text-xl font-semibold text-gray-800">Add New School</h2>
                        <button id="closeAddSchoolModal" class="text-gray-400 hover:text-gray-600 text-2xl font-bold">
                            &times;
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6">
                        <form id="addSchoolForm" class="space-y-6">
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">School Name *</label>
                                    <input type="text" name="name" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                    <input type="email" name="email" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                            </div>

                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Plan *</label>
                                    <select name="plan_id" required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="">Select Plan</option>
                                        <option value="1">Free Trial (30 days)</option>
                                        <option value="2">Professional ($49/month)</option>
                                        <option value="3">Enterprise ($99/month)</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">School Address *</label>
                                <textarea name="address" required rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                          placeholder="Enter complete school address"></textarea>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                                <input type="password" name="password" required minlength="8"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Minimum 8 characters">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Confirm Password *</label>
                                <input type="password" name="confirm_password" required minlength="8"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                       placeholder="Re-enter password">
                            </div>

                            <!-- Status Selection -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Initial Status</label>
                                <select name="status"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <option value="inactive">Pending Approval</option>
                                    <option value="active">Active (Approved)</option>
                                </select>
                            </div>

                            <!-- Error/Success Messages -->
                            <div id="addSchoolMessages" class="hidden"></div>

                            <!-- Submit Button -->
                            <div class="flex justify-end space-x-3 pt-4">
                                <button type="button" onclick="closeAddSchoolModal()"
                                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button type="submit"
                                        class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-300">
                                    <i class="fas fa-plus mr-2"></i>Add School
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

          <!-- Users Management Section -->
            <div id="users-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold">User Management</h3>
                        <div class="text-sm text-gray-600">
                            Total Users: <span class="font-semibold"><?= $totalUsers ?? 0 ?></span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid md:grid-cols-3 gap-6 mb-6">
                            <div class="bg-blue-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-blue-800">Super Admins</h4>
                                <p class="text-2xl font-bold text-blue-600">1</p>
                            </div>
                            <div class="bg-green-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-green-800">School Admins</h4>
                                <p class="text-2xl font-bold text-green-600"><?= $schoolAdmins ?? 0 ?></p>
                            </div>
                            <div class="bg-purple-50 p-4 rounded-lg">
                                <h4 class="font-semibold text-purple-800">Staff Members</h4>
                                <p class="text-2xl font-bold text-purple-600"><?= $staffMembers ?? 0 ?></p>
                            </div>
                        </div>
                        <div class="flex justify-between items-center mb-4">
                            <input type="text" id="user-search" placeholder="Search users..."
                                class="px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <select id="user-role-filter" class="px-4 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Roles</option>
                                <option value="active">Active Users</option>
                                <option value="inactive">Inactive Users</option>
                            </select>
                        </div>
                        <div id="users-list" class="space-y-3">
                            <!-- Loading state -->
                            <div id="users-loading" class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-4"></i>
                                <p class="text-gray-600">Loading users...</p>
                            </div>
                            <!-- Users will be loaded here dynamically -->
                        </div>
                    </div>
                </div>
            </div>



            <!-- View School Details Modal -->
            <div id="viewSchoolModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
                <div class="bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300">
                    <!-- Modal Header -->
                    <div class="flex justify-between items-center p-6 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-school text-indigo-600"></i>
                            </div>
                            <h2 class="text-2xl font-bold text-gray-800">School Details</h2>
                        </div>
                        <button id="closeViewSchoolModal" class="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-all duration-200">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6 bg-gray-50">
                        <!-- Loading State -->
                        <div id="schoolDetailsLoading" class="text-center py-12">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                                <i class="fas fa-spinner fa-spin text-2xl text-indigo-600"></i>
                            </div>
                            <p class="text-gray-600 text-lg">Loading school details...</p>
                            <div class="mt-4">
                                <div class="w-48 h-2 bg-gray-200 rounded-full mx-auto overflow-hidden">
                                    <div class="h-full bg-indigo-600 rounded-full animate-pulse"></div>
                                </div>
                            </div>
                        </div>

                        <!-- School Details Content -->
                        <div id="schoolDetailsContent" class="hidden">
                            <!-- Basic Information -->
                            <div class="grid md:grid-cols-2 gap-6 mb-6">
                                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
                                    <div class="flex items-center mb-4">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-info-circle text-blue-600"></i>
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-800">Basic Information</h3>
                                    </div>
                                    <div class="space-y-4">
                                        <div class="border-l-4 border-blue-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">School Name</label>
                                            <p id="school-name" class="text-gray-900 font-semibold text-lg">-</p>
                                        </div>
                                        <div class="border-l-4 border-green-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Email Address</label>
                                            <p id="school-email" class="text-gray-900 font-medium">-</p>
                                        </div>
                                        <div class="border-l-4 border-purple-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Phone Number</label>
                                            <p id="school-phone" class="text-gray-900 font-medium">-</p>
                                        </div>
                                        <div class="border-l-4 border-yellow-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Status</label>
                                            <span id="school-status" class="inline-flex px-3 py-1 text-sm font-semibold rounded-full">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
                                    <div class="flex items-center mb-4">
                                        <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-clipboard-list text-indigo-600"></i>
                                        </div>
                                        <h3 class="text-lg font-semibold text-gray-800">Additional Details</h3>
                                    </div>
                                    <div class="space-y-4">
                                        <div class="border-l-4 border-indigo-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Subscription Plan</label>
                                            <p id="school-plan" class="text-gray-900 font-medium">-</p>
                                        </div>
                                        <div class="border-l-4 border-green-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Registration Date</label>
                                            <p id="school-created" class="text-gray-900 font-medium">-</p>
                                        </div>
                                        <div class="border-l-4 border-blue-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Last Updated</label>
                                            <p id="school-updated" class="text-gray-900 font-medium">-</p>
                                        </div>
                                        <div id="rejection-reason-container" class="hidden border-l-4 border-red-500 pl-4">
                                            <label class="block text-sm font-medium text-gray-500 mb-1">Rejection Reason</label>
                                            <div class="bg-red-50 p-3 rounded-lg">
                                                <p id="school-rejection-reason" class="text-red-700 font-medium">-</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Address -->
                            <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200 mb-6">
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-map-marker-alt text-orange-600"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-800">Address Information</h3>
                                </div>
                                <div class="border-l-4 border-orange-500 pl-4">
                                    <label class="block text-sm font-medium text-gray-500 mb-2">Complete Address</label>
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <p id="school-address" class="text-gray-900 font-medium leading-relaxed">-</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div id="school-actions" class="flex justify-center space-x-4 pt-6 border-t border-gray-200">
                                <button id="approve-school-btn" onclick="approveSchoolFromModal()"
                                        class="hidden px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                                    <i class="fas fa-check mr-2"></i>Approve School
                                </button>
                                <button id="reject-school-btn" onclick="rejectSchoolFromModal()"
                                        class="hidden px-8 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-xl hover:from-red-600 hover:to-red-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                                    <i class="fas fa-times mr-2"></i>Reject School
                                </button>
                                <button onclick="closeViewSchoolModal()"
                                        class="px-8 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                                    <i class="fas fa-times mr-2"></i>Close
                                </button>
                            </div>
                        </div>

                        <!-- Error State -->
                        <div id="schoolDetailsError" class="hidden text-center py-12">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                                <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">Unable to Load School Details</h3>
                            <p class="text-gray-600 mb-6">We encountered an error while fetching the school information. Please try again.</p>
                            <div class="space-x-3">
                                <button onclick="fetchSchoolDetails(currentSchoolId)"
                                        class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">
                                    <i class="fas fa-redo mr-2"></i>Try Again
                                </button>
                                <button onclick="closeViewSchoolModal()"
                                        class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition duration-200">
                                    <i class="fas fa-times mr-2"></i>Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscriptions Section -->
            <div id="subscriptions-section" class="section hidden">
                <!-- Loading State -->
                <div id="subscriptions-loading" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                        <i class="fas fa-spinner fa-spin text-2xl text-indigo-600"></i>
                    </div>
                    <p class="text-gray-600 text-lg">Loading subscription data...</p>
                </div>

                <!-- Subscription Content -->
                <div id="subscriptions-content" class="hidden">
                    <!-- Statistics Cards -->
                    <div class="grid lg:grid-cols-4 gap-6 mb-6">
                        <div class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl shadow-md border border-green-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-check-circle text-white text-xl"></i>
                                </div>
                                <span class="text-green-600 text-sm font-medium">Active</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Active Subscriptions</h4>
                            <p id="active-count" class="text-3xl font-bold text-green-600">-</p>
                            <p class="text-sm text-green-700 mt-2">Currently paying customers</p>
                        </div>

                        <div class="bg-gradient-to-br from-yellow-50 to-yellow-100 p-6 rounded-xl shadow-md border border-yellow-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-clock text-white text-xl"></i>
                                </div>
                                <span class="text-yellow-600 text-sm font-medium">Trial</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Trial Users</h4>
                            <p id="trial-count" class="text-3xl font-bold text-yellow-600">-</p>
                            <p class="text-sm text-yellow-700 mt-2">Free trial period</p>
                        </div>

                        <div class="bg-gradient-to-br from-red-50 to-red-100 p-6 rounded-xl shadow-md border border-red-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                                </div>
                                <span class="text-red-600 text-sm font-medium">Expired</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Expired/Suspended</h4>
                            <p id="expired-count" class="text-3xl font-bold text-red-600">-</p>
                            <p class="text-sm text-red-700 mt-2">Requires attention</p>
                        </div>

                        <div class="bg-gradient-to-br from-indigo-50 to-indigo-100 p-6 rounded-xl shadow-md border border-indigo-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-indigo-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-rupee-sign text-white text-xl"></i>
                                </div>
                                <span class="text-indigo-600 text-sm font-medium">Revenue</span>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Total Revenue</h4>
                            <p id="total-revenue" class="text-3xl font-bold text-indigo-600">-</p>
                            <p class="text-sm text-indigo-700 mt-2">All-time earnings</p>
                        </div>
                    </div>
                    <!-- Subscription Details Table -->
                    <div class="bg-white rounded-xl shadow-md border border-gray-100">
                        <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-credit-card text-indigo-600"></i>
                                    </div>
                                    <h3 class="text-xl font-bold text-gray-800">Subscription Management</h3>
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="refreshSubscriptionData()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">
                                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                                    </button>
                                    <button onclick="exportSubscriptionData()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                                        <i class="fas fa-download mr-2"></i>Export
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Search and Filter -->
                        <div class="p-4 border-b border-gray-200 bg-gray-50">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-3 md:space-y-0">
                                <div class="flex-1 max-w-md">
                                    <div class="relative">
                                        <input type="text" id="subscription-search" placeholder="Search schools, plans, or status..."
                                               class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-search text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-3">
                                    <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="trial">Trial</option>
                                        <option value="pending">Pending Payment</option>
                                        <option value="expired">Expired</option>
                                        <option value="cancelled">Cancelled</option>
                                        <option value="suspended">Suspended</option>
                                    </select>
                                    <select id="plan-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="">All Plans</option>
                                        <option value="Free Trial">Free Trial</option>
                                        <option value="Professional">Professional</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gradient-to-r from-gray-100 to-gray-200">
                                    <tr>
                                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-school text-gray-500"></i>
                                                <span>School</span>
                                            </div>
                                        </th>
                                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-tag text-gray-500"></i>
                                                <span>Plan</span>
                                            </div>
                                        </th>
                                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-info-circle text-gray-500"></i>
                                                <span>Status</span>
                                            </div>
                                        </th>
                                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-calendar text-gray-500"></i>
                                                <span>Next Billing</span>
                                            </div>
                                        </th>
                                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-rupee-sign text-gray-500"></i>
                                                <span>Amount</span>
                                            </div>
                                        </th>
                                        <th class="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                                            <div class="flex items-center space-x-1">
                                                <i class="fas fa-cogs text-gray-500"></i>
                                                <span>Actions</span>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody id="subscriptions-tbody" class="divide-y divide-gray-200 bg-white">
                                    <!-- Dynamic content will be loaded here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Empty State -->
                        <div id="subscriptions-empty" class="hidden text-center py-12">
                            <div class="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                                <i class="fas fa-credit-card text-gray-400 text-2xl"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">No Subscriptions Found</h3>
                            <p class="text-gray-600">No subscription data matches your current filters.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="section hidden">
                <!-- Loading State -->
                <div id="analytics-loading" class="text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                        <i class="fas fa-spinner fa-spin text-2xl text-indigo-600"></i>
                    </div>
                    <p class="text-gray-600 text-lg">Loading analytics data...</p>
                </div>

                <!-- Analytics Content -->
                <div id="analytics-content" class="hidden">
                    <!-- Analytics Controls -->
                    <div class="bg-white p-6 rounded-xl shadow-md border border-gray-100 mb-6">
                        <div class="flex flex-wrap items-center justify-between gap-4">
                            <div class="flex items-center space-x-4">
                                <h3 class="text-lg font-semibold text-gray-800">Analytics Dashboard</h3>
                                <div class="flex items-center space-x-2">
                                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                                    <span class="text-sm text-gray-600">Live Data</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-3">
                                <select id="analytics-date-range" class="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-indigo-500 text-sm">
                                    <option value="7">Last 7 Days</option>
                                    <option value="30" selected>Last 30 Days</option>
                                    <option value="90">Last 3 Months</option>
                                    <option value="365">Last Year</option>
                                    <option value="custom">Custom Range</option>
                                </select>
                                <div id="custom-date-range" class="hidden flex items-center space-x-2">
                                    <input type="date" id="start-date" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <span class="text-gray-500">to</span>
                                    <input type="date" id="end-date" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                </div>
                                <button onclick="refreshAnalytics()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200 text-sm">
                                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                                </button>
                                <button onclick="exportAnalytics()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200 text-sm">
                                    <i class="fas fa-download mr-2"></i>Export
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Stats Cards -->
                    <div class="grid lg:grid-cols-4 gap-6 mb-6">
                        <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-6 rounded-xl shadow-md border border-blue-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-users text-white text-xl"></i>
                                </div>
                                <div class="text-right">
                                    <span class="text-blue-600 text-sm font-medium">Total</span>
                                    <div id="users-trend" class="flex items-center mt-1">
                                        <i class="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
                                        <span class="text-xs text-green-600 font-medium">+0%</span>
                                    </div>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Total Users</h4>
                            <div class="flex items-baseline space-x-2">
                                <p id="analytics-total-users" class="text-3xl font-bold text-blue-600">-</p>
                                <span id="users-change" class="text-sm text-gray-500">vs last period</span>
                            </div>
                            <p class="text-sm text-blue-700 mt-2">Across all schools</p>
                        </div>

                        <div class="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-xl shadow-md border border-green-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-school text-white text-xl"></i>
                                </div>
                                <div class="text-right">
                                    <span class="text-green-600 text-sm font-medium">Active</span>
                                    <div id="schools-trend" class="flex items-center mt-1">
                                        <i class="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
                                        <span class="text-xs text-green-600 font-medium">+0%</span>
                                    </div>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Active Schools</h4>
                            <div class="flex items-baseline space-x-2">
                                <p id="analytics-active-schools" class="text-3xl font-bold text-green-600">-</p>
                                <span id="schools-change" class="text-sm text-gray-500">vs last period</span>
                            </div>
                            <p class="text-sm text-green-700 mt-2">Currently operational</p>
                        </div>

                        <div class="bg-gradient-to-br from-purple-50 to-purple-100 p-6 rounded-xl shadow-md border border-purple-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-question-circle text-white text-xl"></i>
                                </div>
                                <div class="text-right">
                                    <span class="text-purple-600 text-sm font-medium">Total</span>
                                    <div id="questions-trend" class="flex items-center mt-1">
                                        <i class="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
                                        <span class="text-xs text-green-600 font-medium">+0%</span>
                                    </div>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Questions Created</h4>
                            <div class="flex items-baseline space-x-2">
                                <p id="analytics-total-questions" class="text-3xl font-bold text-purple-600">-</p>
                                <span id="questions-change" class="text-sm text-gray-500">vs last period</span>
                            </div>
                            <p class="text-sm text-purple-700 mt-2">Platform-wide</p>
                        </div>

                        <div class="bg-gradient-to-br from-orange-50 to-orange-100 p-6 rounded-xl shadow-md border border-orange-200 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-dollar-sign text-white text-xl"></i>
                                </div>
                                <div class="text-right">
                                    <span class="text-orange-600 text-sm font-medium">Monthly</span>
                                    <div id="revenue-trend" class="flex items-center mt-1">
                                        <i class="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
                                        <span class="text-xs text-green-600 font-medium">+0%</span>
                                    </div>
                                </div>
                            </div>
                            <h4 class="font-semibold text-gray-800 mb-2">Revenue</h4>
                            <div class="flex items-baseline space-x-2">
                                <p id="analytics-monthly-revenue" class="text-3xl font-bold text-orange-600">-</p>
                                <span id="revenue-change" class="text-sm text-gray-500">vs last period</span>
                            </div>
                            <p class="text-sm text-orange-700 mt-2">Current period</p>
                        </div>
                    </div>

                    <!-- Charts Row -->
                    <div class="grid lg:grid-cols-2 gap-6 mb-6">
                        <div class="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">Platform Usage Analytics</h3>
                                <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-indigo-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="relative h-[300px]">
                                <canvas id="usageChart"></canvas>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">Revenue Analytics</h3>
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-bar text-green-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="relative h-[300px]">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Analytics Row -->
                    <div class="grid lg:grid-cols-2 gap-6">
                        <div class="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">School Growth Trend</h3>
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-area text-blue-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="relative h-[300px]">
                                <canvas id="schoolGrowthAnalyticsChart"></canvas>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-xl shadow-md border border-gray-100 hover:shadow-lg transition-shadow duration-200">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-800">Subscription Status Distribution</h3>
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-pie text-purple-600 text-sm"></i>
                                </div>
                            </div>
                            <div class="relative h-[300px]">
                                <canvas id="subscriptionAnalyticsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error State -->
                <div id="analytics-error" class="hidden text-center py-12">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                        <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Unable to Load Analytics</h3>
                    <p class="text-gray-600 mb-6">We encountered an error while fetching the analytics data. Please try again.</p>
                    <button onclick="loadAnalyticsData()" class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">
                        <i class="fas fa-redo mr-2"></i>Try Again
                    </button>
                </div>
            </div>

            <!-- System Settings Section -->
            <div id="system-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-cogs text-indigo-600"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-800">System Settings</h3>
                                    <p class="text-sm text-gray-600">Configure global system parameters</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="refresh-settings-btn" onclick="loadSystemSettings()" class="text-gray-500 hover:text-indigo-600 p-2 rounded-lg hover:bg-indigo-50 transition-all duration-200">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Loading State -->
                    <div id="settings-loading" class="text-center py-12">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full mb-4">
                            <i class="fas fa-spinner fa-spin text-2xl text-indigo-600"></i>
                        </div>
                        <p class="text-gray-600 text-lg">Loading system settings...</p>
                    </div>

                    <!-- Settings Content -->
                    <div id="settings-content" class="hidden p-6 bg-gray-50">
                        <!-- Settings will be dynamically loaded here -->
                    </div>

                    <!-- Error State -->
                    <div id="settings-error" class="hidden text-center py-12">
                        <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-4">
                            <i class="fas fa-exclamation-triangle text-2xl text-red-600"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 mb-2">Unable to Load Settings</h3>
                        <p class="text-gray-600 mb-6">We encountered an error while fetching the system settings.</p>
                        <button onclick="loadSystemSettings()" class="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition duration-200">
                            <i class="fas fa-redo mr-2"></i>Try Again
                        </button>
                    </div>
                </div>
            </div>

            <!-- Audit Logs Section -->
            <div id="audit-section" class="section hidden">
                <div class="bg-white rounded-xl shadow-md">
                    <div class="p-6 border-b flex justify-between items-center">
                        <h3 class="text-lg font-semibold">Audit Logs</h3>
                        <div class="flex space-x-3">
                            <button onclick="refreshAuditLogs()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700">
                                <i class="fas fa-refresh mr-2"></i>Refresh
                            </button>
                            <button onclick="exportAuditLogs()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                                <i class="fas fa-download mr-2"></i>Export Logs
                            </button>
                        </div>
                    </div>

                    <!-- Audit Statistics -->
                    <div class="p-6 border-b bg-gray-50">
                        <div class="grid md:grid-cols-4 gap-4" id="audit-stats">
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Total Actions</h4>
                                <p class="text-2xl font-bold text-gray-900" id="total-actions">-</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Today's Actions</h4>
                                <p class="text-2xl font-bold text-blue-600" id="today-actions">-</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Critical Events</h4>
                                <p class="text-2xl font-bold text-red-600" id="critical-events">-</p>
                            </div>
                            <div class="bg-white p-4 rounded-lg shadow-sm">
                                <h4 class="text-sm font-medium text-gray-600">Failed Actions</h4>
                                <p class="text-2xl font-bold text-orange-600" id="failed-actions">-</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <!-- Filters -->
                        <div class="grid md:grid-cols-6 gap-3 mb-6">
                            <input type="text" id="audit-search" placeholder="Search logs..."
                                class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <input type="date" id="audit-date-from"
                                class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <input type="date" id="audit-date-to"
                                class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                            <select id="audit-action-filter" class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Actions</option>
                            </select>
                            <select id="audit-severity-filter" class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Severities</option>
                                <option value="low">Low</option>
                                <option value="medium">Medium</option>
                                <option value="high">High</option>
                                <option value="critical">Critical</option>
                            </select>
                            <select id="audit-school-filter" class="px-3 py-2 border rounded-lg focus:outline-none focus:border-indigo-500">
                                <option value="">All Schools</option>
                            </select>
                        </div>

                        <!-- Audit Logs List -->
                        <div id="audit-logs-container">
                            <div id="audit-logs-loading" class="text-center py-8">
                                <i class="fas fa-spinner fa-spin text-2xl text-indigo-600 mb-4"></i>
                                <p class="text-gray-600">Loading audit logs...</p>
                            </div>
                            <div id="audit-logs-list" class="space-y-3 hidden"></div>
                        </div>

                        <!-- Pagination -->
                        <div id="audit-pagination" class="flex justify-between items-center mt-6 hidden">
                            <div class="text-sm text-gray-600" id="audit-pagination-info"></div>
                            <div class="flex space-x-2" id="audit-pagination-buttons"></div>
                        </div>
                    </div>
                </div>
            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Include Profile Popup -->
    <?php include APPPATH . 'Views/superadmin/profile_popup.php'; ?>

    <!-- Safe Action Popup (Non-intrusive) -->
    <div id="safe-action-popup" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform scale-95 transition-transform duration-200" id="safe-popup-content">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div id="safe-popup-icon" class="w-12 h-12 rounded-full flex items-center justify-center mr-4">
                        <!-- Icon will be set by JS -->
                    </div>
                    <h3 id="safe-popup-title" class="text-lg font-semibold text-gray-900">Confirm Action</h3>
                </div>
                <p id="safe-popup-message" class="text-gray-600 mb-6">Are you sure you want to perform this action?</p>
                <div id="safe-popup-input" class="hidden mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
                    <textarea id="safe-popup-textarea" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" placeholder="Enter reason..."></textarea>
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="safe-popup-cancel" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors">Cancel</button>
                    <button id="safe-popup-confirm" class="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors">Confirm</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Safe Toast Notifications -->
    <div id="safe-toast-container" class="fixed top-4 right-4 z-60 space-y-2"></div>

<script>
    function showSection(sectionName, clickedElement = null) {
        document.querySelectorAll('.section').forEach(section => {
            section.classList.remove('active');
            section.classList.add('hidden');
        });

        const targetSection = document.getElementById(sectionName + '-section');
        if (targetSection) {
            targetSection.classList.remove('hidden');
            targetSection.classList.add('active');
        }

        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active', 'bg-indigo-50', 'text-indigo-600');
        });

        // Handle both event-based and programmatic calls
        let navItem = clickedElement;
        if (!navItem && event && event.currentTarget) {
            navItem = event.currentTarget;
        }
        if (!navItem) {
            // Find the nav item by section name
            navItem = document.querySelector(`[onclick*="showSection('${sectionName}')"]`);
        }

        if (navItem) {
            navItem.classList.add('active', 'bg-indigo-50', 'text-indigo-600');
        }

        const titles = {
            'dashboard': 'Dashboard Overview',
            'schools': 'Schools Management',
            'users': 'User Management',
            'subscriptions': 'Subscription Management',
            'analytics': 'Platform Analytics',
            'system': 'System Settings',
            'audit': 'Audit Logs'
        };

        const subtitles = {
            'dashboard': 'Welcome back, manage your entire platform from here',
            'schools': 'Manage all educational institutions on the platform',
            'users': 'Oversee all user accounts and permissions',
            'subscriptions': 'Monitor and manage subscription plans',
            'analytics': 'View detailed platform usage and performance metrics',
            'system': 'Configure global platform settings',
            'audit': 'Review all system activities and changes'
        };

        document.getElementById('pageTitle').textContent = titles[sectionName] || 'Dashboard Overview';
        document.getElementById('pageSubtitle').textContent = subtitles[sectionName] || 'Welcome back';

        if (sectionName === 'schools') {
            filterSchools('all'); // Default tab

        } else if (sectionName === 'users') {
            loadUsers(); // Load users when users section is shown
        } else if (sectionName === 'audit') {
            loadAuditLogs(); // Load audit logs when audit section is shown
            loadAuditStats(); // Load audit statistics
            loadAuditFilterOptions(); // Load filter options

        } else if (sectionName === 'subscriptions') {
            loadSubscriptionData(); // Load subscription data

        } else if (sectionName === 'analytics') {
            loadAnalyticsData(); // Load analytics data and initialize charts
        }
    }

    function filterSchools(status = 'all') {
        // Update tab styling
        updateTabStyling(status);

        // Determine the correct endpoint based on status
        let endpoint = '/admin/schools/all';
        if (status === 'active') {
            endpoint = '/admin/schools/active';
        } else if (status === 'inactive') {
            endpoint = '/admin/schools/pending';
        }

        const tbody = document.querySelector('#schools-tbody');
        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-gray-500"><i class="fas fa-spinner fa-spin mr-2"></i>Loading schools...</td></tr>';

        fetch(endpoint)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    tbody.innerHTML = '';

                    if (data.schools.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-gray-500">No schools found.</td></tr>';
                        return;
                    }

                    data.schools.forEach(school => {
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-gray-50';

                        const statusBadge = getStatusBadge(school.status);
                        const actionButtons = getActionButtons(school);

                        row.innerHTML = `
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">${escapeHtml(school.name)}</div>
                            </td>
                            <td class="px-6 py-4 text-gray-600">${escapeHtml(school.email)}</td>
                            <td class="px-6 py-4 text-gray-600">${escapeHtml(school.phone || 'N/A')}</td>
                            <td class="px-6 py-4">
                                <div class="max-w-xs truncate text-gray-600" title="${escapeHtml(school.address)}">
                                    ${escapeHtml(school.address || 'N/A')}
                                </div>
                            </td>
                            <td class="px-6 py-4">${statusBadge}</td>
                            <td class="px-6 py-4 text-gray-600">${new Date(school.created_at).toLocaleDateString()}</td>
                            <td class="px-6 py-4">${actionButtons}</td>
                        `;
                        tbody.appendChild(row);
                    });
                } else {
                    tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-red-500">Error loading schools: ' + (data.message || 'Unknown error') + '</td></tr>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center text-red-500">Network error occurred. Please try again.</td></tr>';
            });
    }

    // Helper functions for school management
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function getStatusBadge(status) {
        const badges = {
            'active': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Active</span>',
            'inactive': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>',
            'rejected': '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>'
        };
        return badges[status] || '<span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">Unknown</span>';
    }

    function getActionButtons(school) {
        let buttons = '';

        if (school.status === 'inactive') {
            buttons += `
                <button onclick="approveSchool(${school.id})" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 mr-2">
                    <i class="fas fa-check mr-1"></i>Approve
                </button>
                <button onclick="rejectSchool(${school.id})" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 mr-2">
                    <i class="fas fa-times mr-1"></i>Reject
                </button>
            `;
        }

        buttons += `
            <button onclick="viewSchoolDetails(${school.id})" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                <i class="fas fa-eye mr-1"></i>View
            </button>
        `;

        return '<div class="flex space-x-1">' + buttons + '</div>';
    }

    function updateTabStyling(activeStatus) {
        // Reset all tab buttons
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.className = 'px-4 py-1 rounded-lg border text-gray-600 hover:bg-gray-50 tab-button';
        });

        // Set active tab styling
        const tabMap = {
            'all': 'border-indigo-600 text-indigo-600 bg-indigo-50',
            'inactive': 'border-yellow-600 text-yellow-600 bg-yellow-50',
            'active': 'border-green-600 text-green-600 bg-green-50'
        };

        const activeTab = document.querySelector(`[onclick="showSchoolTab('${activeStatus === 'inactive' ? 'pending' : activeStatus}')"]`);
        if (activeTab) {
            activeTab.className = `px-4 py-1 rounded-lg border ${tabMap[activeStatus]} tab-button`;
        }
    }

    function showSchoolTab(tabName) {
        if (tabName === 'pending') {
            filterSchools('inactive');
        } else if (tabName === 'active') {
            filterSchools('active');
        } else {
            filterSchools('all');
        }
    }

    function approveSchool(id) {
        showSafePopup({
            type: 'confirm',
            title: 'Approve School',
            message: 'Are you sure you want to approve this school? This will activate their account and allow them to access the system.',
            confirmText: 'Approve School',
            confirmClass: 'bg-green-600 hover:bg-green-700',
            onConfirm: function() {
                fetch(`<?= site_url('superadmin/approve') ?>/${id}`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSafeToast("School approved successfully!", 'success');
                        filterSchools('all');
                    } else {
                        showSafeToast(data.message || 'Failed to approve school', 'error');
                    }
                })
                .catch(error => {
                    showSafeToast(error.message || 'Network error occurred', 'error');
                });
            }
        });
    }

    function rejectSchool(id) {
        console.log('rejectSchool function called with ID:', id);
        showSafePopup({
            type: 'warning',
            title: 'Reject School',
            message: 'Please provide a reason for rejecting this school application. This will help them understand why their application was not approved.',
            showInput: true,
            confirmText: 'Reject School',
            confirmClass: 'bg-red-600 hover:bg-red-700',
            onConfirm: function(reason) {
                if (!reason || reason.trim() === '') {
                    showSafeToast('Please provide a reason for rejection', 'warning');
                    return false; // Don't close popup
                }

                console.log('Rejecting school with ID:', id, 'Reason:', reason);
                fetch(`<?= site_url('superadmin/reject') ?>/${id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ reason: reason })
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        showSafeToast("School rejected successfully!", 'success');
                        filterSchools('all');
                    } else {
                        showSafeToast(data.message || 'Failed to reject school', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showSafeToast(error.message || 'Network error occurred', 'error');
                });
            }
        });
    }
    

    // Subscription Management Functions
    let subscriptionData = [];
    let filteredSubscriptionData = [];

    function loadSubscriptionData() {
        console.log('Loading subscription data...');

        // Show loading state
        document.getElementById('subscriptions-loading').classList.remove('hidden');
        document.getElementById('subscriptions-content').classList.add('hidden');

        fetch('/superadmin/getSubscriptionData', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Subscription data received:', data);
            if (data.success) {
                subscriptionData = data.subscriptions;
                filteredSubscriptionData = [...subscriptionData];
                updateSubscriptionStats(data.stats);
                displaySubscriptions(filteredSubscriptionData);

                // Hide loading, show content
                document.getElementById('subscriptions-loading').classList.add('hidden');
                document.getElementById('subscriptions-content').classList.remove('hidden');

                console.log('Subscription data loaded successfully');
            } else {
                console.error('API Error:', data.message);
                showSubscriptionError();
            }
        })
        .catch(error => {
            console.error('Error loading subscription data:', error);
            showSubscriptionError();
        });
    }

    function updateSubscriptionStats(stats) {
        document.getElementById('active-count').textContent = stats.active || 0;
        document.getElementById('trial-count').textContent = stats.trial || 0;
        document.getElementById('expired-count').textContent = (stats.expired || 0) + (stats.pending || 0);
        document.getElementById('total-revenue').textContent = '₹' + (parseFloat(stats.total_revenue || 0).toLocaleString('en-IN'));

        // Update the expired card to show pending payments
        const expiredCard = document.querySelector('.bg-red-50');
        if (expiredCard && stats.pending > 0) {
            const expiredTitle = expiredCard.querySelector('h3');
            const expiredIcon = expiredCard.querySelector('i');
            if (expiredTitle) expiredTitle.textContent = 'Pending Payments';
            if (expiredIcon) {
                expiredIcon.className = 'fas fa-clock text-orange-600';
                expiredCard.className = expiredCard.className.replace('bg-red-50', 'bg-orange-50');
            }
        }
    }

    function displaySubscriptions(subscriptions) {
        const tbody = document.getElementById('subscriptions-tbody');
        const emptyState = document.getElementById('subscriptions-empty');

        if (!subscriptions || subscriptions.length === 0) {
            tbody.innerHTML = '';
            emptyState.classList.remove('hidden');
            return;
        }

        emptyState.classList.add('hidden');

        tbody.innerHTML = subscriptions.map(subscription => `
            <tr class="hover:bg-gray-50 transition-colors duration-150">
                <td class="px-6 py-4">
                    <div class="flex flex-col">
                        <div class="font-semibold text-gray-900">${escapeHtml(subscription.school_name)}</div>
                        <div class="text-sm text-gray-500">${escapeHtml(subscription.school_email)}</div>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex px-3 py-1 text-sm font-medium rounded-full ${getPlanBadgeClass(subscription.plan_name)}">
                            ${escapeHtml(subscription.plan_name)}
                        </span>
                        <span class="text-xs text-gray-500">${subscription.billing_cycle}</span>
                    </div>
                </td>
                <td class="px-6 py-4">
                    ${getSubscriptionStatusBadge(subscription.actual_status || subscription.status)}
                </td>
                <td class="px-6 py-4">
                    <div class="text-sm text-gray-900">
                        ${subscription.expires_at ? formatDate(subscription.expires_at) : 'N/A'}
                    </div>
                    ${subscription.trial_ends_at ? `<div class="text-xs text-yellow-600">Trial ends: ${formatDate(subscription.trial_ends_at)}</div>` : ''}
                </td>
                <td class="px-6 py-4">
                    <div class="font-semibold text-gray-900">₹${parseFloat(subscription.amount).toLocaleString('en-IN')}</div>
                    <div class="text-xs text-gray-500">per ${subscription.billing_cycle.replace('ly', '')}</div>
                </td>
                <td class="px-6 py-4">
                    <div class="flex space-x-2">
                        <button onclick="viewSubscriptionDetails(${subscription.id})"
                                class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                            <i class="fas fa-eye mr-1"></i>View
                        </button>
                        <button onclick="manageSubscription(${subscription.id})"
                                class="text-green-600 hover:text-green-800 text-sm font-medium">
                            <i class="fas fa-cog mr-1"></i>Manage
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    function getPlanBadgeClass(planName) {
        switch (planName?.toLowerCase()) {
            case 'free trial':
                return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
            case 'professional':
                return 'bg-blue-100 text-blue-800 border border-blue-200';
            default:
                return 'bg-gray-100 text-gray-800 border border-gray-200';
        }
    }

    function getSubscriptionStatusBadge(status) {
        const badges = {
            'active': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 border border-green-200"><i class="fas fa-check-circle mr-1"></i>Active</span>',
            'trial': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200"><i class="fas fa-clock mr-1"></i>Trial</span>',
            'pending': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-orange-100 text-orange-800 border border-orange-200"><i class="fas fa-hourglass-half mr-1"></i>Pending Payment</span>',
            'expired': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 border border-red-200"><i class="fas fa-times-circle mr-1"></i>Expired</span>',
            'cancelled': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800 border border-gray-200"><i class="fas fa-ban mr-1"></i>Cancelled</span>',
            'suspended': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-orange-100 text-orange-800 border border-orange-200"><i class="fas fa-pause-circle mr-1"></i>Suspended</span>'
        };
        return badges[status] || '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800 border border-gray-200"><i class="fas fa-question-circle mr-1"></i>Unknown</span>';
    }

    function showSubscriptionError() {
        document.getElementById('subscriptions-loading').classList.add('hidden');
        document.getElementById('subscriptions-content').classList.add('hidden');
        // You can add an error state here if needed
    }

    function refreshSubscriptionData() {
        loadSubscriptionData();
    }

    function exportSubscriptionData() {
        // Implement export functionality
        alert('Export functionality will be implemented soon!');
    }

    function viewSubscriptionDetails(subscriptionId) {
        // Implement view details functionality
        alert(`View details for subscription ID: ${subscriptionId}`);
    }

    function manageSubscription(subscriptionId) {
        // Implement manage subscription functionality
        alert(`Manage subscription ID: ${subscriptionId}`);
    }

    // Search and filter functionality
    function setupSubscriptionFilters() {
        const searchInput = document.getElementById('subscription-search');
        const statusFilter = document.getElementById('status-filter');
        const planFilter = document.getElementById('plan-filter');

        function applyFilters() {
            let filtered = [...subscriptionData];

            const searchTerm = searchInput?.value.toLowerCase() || '';
            const statusValue = statusFilter?.value || '';
            const planValue = planFilter?.value || '';

            if (searchTerm) {
                filtered = filtered.filter(sub =>
                    sub.school_name.toLowerCase().includes(searchTerm) ||
                    sub.school_email.toLowerCase().includes(searchTerm) ||
                    sub.plan_name.toLowerCase().includes(searchTerm) ||
                    (sub.actual_status || sub.status).toLowerCase().includes(searchTerm)
                );
            }

            if (statusValue) {
                filtered = filtered.filter(sub => (sub.actual_status || sub.status) === statusValue);
            }

            if (planValue) {
                filtered = filtered.filter(sub => sub.plan_name === planValue);
            }

            filteredSubscriptionData = filtered;
            displaySubscriptions(filteredSubscriptionData);
        }

        searchInput?.addEventListener('input', applyFilters);
        statusFilter?.addEventListener('change', applyFilters);
        planFilter?.addEventListener('change', applyFilters);
    }

    document.addEventListener('DOMContentLoaded', function() {
        showSection('dashboard');
        setupSubscriptionFilters();
    });

    // Enhanced search functionality
    document.getElementById('school-search')?.addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase().trim();
        const rows = document.querySelectorAll('#schools-tbody tr');

        if (!searchTerm) {
            // Show all rows if search is empty
            rows.forEach(row => {
                row.style.display = '';
            });
            return;
        }

        rows.forEach(row => {
            // Skip loading/error rows
            if (row.cells.length < 7) {
                return;
            }

            // Search in school name, email, phone, and address
            const schoolName = row.cells[0].textContent.toLowerCase();
            const email = row.cells[1].textContent.toLowerCase();
            const phone = row.cells[2].textContent.toLowerCase();
            const address = row.cells[3].textContent.toLowerCase();

            const isMatch = schoolName.includes(searchTerm) ||
                           email.includes(searchTerm) ||
                           phone.includes(searchTerm) ||
                           address.includes(searchTerm);

            row.style.display = isMatch ? '' : 'none';
        });
    });

      function viewSchoolDetails(schoolId) {
        openViewSchoolModal(schoolId);
    }

    // Add School Modal Functions
    function openAddSchoolModal() {
        document.getElementById('addSchoolModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        // Clear form
        document.getElementById('addSchoolForm').reset();
        document.getElementById('addSchoolMessages').classList.add('hidden');
    }

    function closeAddSchoolModal() {
        document.getElementById('addSchoolModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
        // Clear form and messages
        document.getElementById('addSchoolForm').reset();
        document.getElementById('addSchoolMessages').classList.add('hidden');
    }

    // Close modal when clicking outside
    document.getElementById('addSchoolModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeAddSchoolModal();
        }
    });

    // Close modal button
    document.getElementById('closeAddSchoolModal')?.addEventListener('click', closeAddSchoolModal);

    // Handle Add School Form Submission
    document.getElementById('addSchoolForm')?.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;

        // Validate passwords match
        if (formData.get('password') !== formData.get('confirm_password')) {
            showAddSchoolMessage('Passwords do not match!', 'error');
            return;
        }

        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding School...';

        // Clear previous messages
        document.getElementById('addSchoolMessages').classList.add('hidden');

        fetch('<?= site_url('school/register') ?>', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.text())
        .then(data => {
            // Check if the response indicates success
            if (data.includes('success') || data.includes('Registration successful')) {
                showAddSchoolMessage('School added successfully!', 'success');
                setTimeout(() => {
                    closeAddSchoolModal();
                    // Refresh the schools list
                    filterSchools('all');
                }, 2000);
            } else if (data.includes('already registered') || data.includes('Duplicate entry')) {
                showAddSchoolMessage('Email address is already registered!', 'error');
            } else if (data.includes('error') || data.includes('failed')) {
                showAddSchoolMessage('Failed to add school. Please check all fields and try again.', 'error');
            } else {
                showAddSchoolMessage('School added successfully!', 'success');
                setTimeout(() => {
                    closeAddSchoolModal();
                    filterSchools('all');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAddSchoolMessage('Network error occurred. Please try again.', 'error');
        })
        .finally(() => {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });

    function showAddSchoolMessage(message, type) {
        const messagesDiv = document.getElementById('addSchoolMessages');
        const alertClass = type === 'error' ? 'bg-red-100 text-red-800 border-red-200' : 'bg-green-100 text-green-800 border-green-200';
        messagesDiv.innerHTML = `
            <div class="p-3 rounded border ${alertClass}">
                ${message}
            </div>
        `;
        messagesDiv.classList.remove('hidden');
    }



    // View School Details Modal Functions
    let currentSchoolId = null;

    function openViewSchoolModal(schoolId) {
        currentSchoolId = schoolId;
        document.getElementById('viewSchoolModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';

        // Show loading state
        document.getElementById('schoolDetailsLoading').classList.remove('hidden');
        document.getElementById('schoolDetailsContent').classList.add('hidden');
        document.getElementById('schoolDetailsError').classList.add('hidden');

        // Fetch school details
        fetchSchoolDetails(schoolId);
    }

    function closeViewSchoolModal() {
        document.getElementById('viewSchoolModal').classList.add('hidden');
        document.body.style.overflow = 'auto';
        currentSchoolId = null;
    }

    // Close modal when clicking outside
    document.getElementById('viewSchoolModal')?.addEventListener('click', function(e) {
        if (e.target === this) {
            closeViewSchoolModal();
        }
    });

    // Close modal button
    document.getElementById('closeViewSchoolModal')?.addEventListener('click', closeViewSchoolModal);

    function fetchSchoolDetails(schoolId) {
        // Show loading state
        document.getElementById('schoolDetailsLoading').classList.remove('hidden');
        document.getElementById('schoolDetailsContent').classList.add('hidden');
        document.getElementById('schoolDetailsError').classList.add('hidden');

        // Fetch school details from the dedicated endpoint
        fetch(`/superadmin/getSchoolDetails/${schoolId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.school) {
                displaySchoolDetails(data.school);
            } else {
                console.error('API Error:', data.message);
                showSchoolDetailsError();
            }
        })
        .catch(error => {
            console.error('Error fetching school details:', error);
            showSchoolDetailsError();
        });
    }

    function displaySchoolDetails(school) {
        // Hide loading, show content
        document.getElementById('schoolDetailsLoading').classList.add('hidden');
        document.getElementById('schoolDetailsContent').classList.remove('hidden');
        document.getElementById('schoolDetailsError').classList.add('hidden');

        // Populate school details with enhanced data handling
        document.getElementById('school-name').textContent = school.name || 'Not Available';
        document.getElementById('school-email').textContent = school.email || 'Not Available';
        document.getElementById('school-phone').textContent = school.phone || 'Not Available';
        document.getElementById('school-address').textContent = school.address || 'Not Available';

        // Handle plan information
        const planText = getPlanName(school.plan_id);
        document.getElementById('school-plan').textContent = planText;

        // Format and display dates
        document.getElementById('school-created').textContent = formatDate(school.created_at);
        document.getElementById('school-updated').textContent = formatDate(school.updated_at);

        // Set status badge with enhanced styling
        const statusElement = document.getElementById('school-status');
        const statusBadge = getEnhancedStatusBadge(school.status);
        statusElement.outerHTML = '<span id="school-status">' + statusBadge + '</span>';

        // Show/hide rejection reason with enhanced styling
        const rejectionContainer = document.getElementById('rejection-reason-container');
        const rejectionReason = document.getElementById('school-rejection-reason');
        if (school.rejection_reason && school.rejection_reason.trim() !== '') {
            rejectionReason.textContent = school.rejection_reason;
            rejectionContainer.classList.remove('hidden');
        } else {
            rejectionContainer.classList.add('hidden');
        }

        // Show/hide action buttons based on status
        const approveBtn = document.getElementById('approve-school-btn');
        const rejectBtn = document.getElementById('reject-school-btn');

        if (school.status === 'inactive') {
            approveBtn.classList.remove('hidden');
            rejectBtn.classList.remove('hidden');
        } else {
            approveBtn.classList.add('hidden');
            rejectBtn.classList.add('hidden');
        }
    }

    function showSchoolDetailsError() {
        document.getElementById('schoolDetailsLoading').classList.add('hidden');
        document.getElementById('schoolDetailsContent').classList.add('hidden');
        document.getElementById('schoolDetailsError').classList.remove('hidden');
    }

    function getPlanName(planId) {
        const plans = {
            '1': 'Free Trial (30 days)',
            '2': 'Professional (₹2,999/month)'
        };
        return plans[planId] || (planId ? `Plan ${planId}` : 'Not Assigned');
    }

    function formatDate(dateString) {
        if (!dateString || dateString === 'N/A' || dateString === null) return 'Not Available';
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return 'Invalid Date';

            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                hour12: true
            });
        } catch (e) {
            return 'Invalid Date';
        }
    }

    function getEnhancedStatusBadge(status) {
        const badges = {
            'active': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-green-100 text-green-800 border border-green-200"><i class="fas fa-check-circle mr-1"></i>Active</span>',
            'inactive': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200"><i class="fas fa-clock mr-1"></i>Pending Approval</span>',
            'rejected': '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-red-100 text-red-800 border border-red-200"><i class="fas fa-times-circle mr-1"></i>Rejected</span>'
        };
        return badges[status] || '<span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full bg-gray-100 text-gray-800 border border-gray-200"><i class="fas fa-question-circle mr-1"></i>Unknown</span>';
    }

    function approveSchoolFromModal() {
        if (currentSchoolId) {
            closeViewSchoolModal();
            approveSchool(currentSchoolId);
        }
    }

    function rejectSchoolFromModal() {
        if (currentSchoolId) {
            rejectSchool(currentSchoolId);
            closeViewSchoolModal();
        }
    }

    // Notification Functions
    function showSuccess(message) {
        showNotification(message, 'success');
    }

    function showError(message) {
        showNotification(message, 'error');
    }

    function showNotification(message, type) {
        // Remove any existing notifications
        const existingNotifications = document.querySelectorAll('.notification-toast');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification-toast fixed top-4 right-4 z-50 px-6 py-4 rounded-lg shadow-lg transform transition-all duration-300 translate-x-full`;

        if (type === 'success') {
            notification.className += ' bg-green-500 text-white';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-3"></i>
                    <span>${message}</span>
                </div>
            `;
        } else if (type === 'error') {
            notification.className += ' bg-red-500 text-white';
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle mr-3"></i>
                    <span>${message}</span>
                </div>
            `;
        }

        // Add to document
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 5000);
    }

    // User Management Functions
    let allUsers = [];
    let filteredUsers = [];

    function loadUsers() {
        fetch('<?= site_url('superadmin/getUsers') ?>', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allUsers = data.users;
                filteredUsers = [...allUsers];
                displayUsers(filteredUsers);
            } else {
                showError('Failed to load users: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading users:', error);
            showError('Failed to load users');
        });
    }

    function displayUsers(users) {
        const usersContainer = document.getElementById('users-list');
        const loadingElement = document.getElementById('users-loading');

        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (users.length === 0) {
            usersContainer.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-users text-4xl mb-4"></i>
                    <p>No users found</p>
                </div>
            `;
            return;
        }

        const usersHTML = users.map(user => {
            const initials = user.name.split(' ').map(n => n[0]).join('').toUpperCase();
            const statusClass = user.status === 'active' ? 'status-active' : 'status-expired';
            const statusText = user.status === 'active' ? 'Active' : 'Inactive';

            return `
                <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-indigo-600 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                            ${initials}
                        </div>
                        <div>
                            <p class="text-lg font-bold text-gray-900">${user.name}</p>
                            <p class="text-sm font-medium text-indigo-600">
                                <i class="fas fa-school mr-1"></i>${user.school_name || 'No School Assigned'}
                            </p>
                            <p class="text-sm text-gray-600">${user.email}</p>
                            ${user.designation ? `<p class="text-xs text-gray-500"><i class="fas fa-user-tag mr-1"></i>${user.designation}</p>` : ''}
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="${statusClass} px-2 py-1 rounded-full text-xs font-medium">${statusText}</span>
                        <button onclick="toggleUserStatus(${user.id})" class="text-blue-600 hover:text-blue-800" title="Toggle Status">
                            <i class="fas fa-toggle-${user.status === 'active' ? 'on' : 'off'}"></i>
                        </button>
                        <button onclick="viewUserDetails(${user.id})" class="text-indigo-600 hover:text-indigo-800" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button onclick="deleteUser(${user.id})" class="text-red-600 hover:text-red-800" title="Delete User">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        usersContainer.innerHTML = usersHTML;
    }

    function toggleUserStatus(userId) {
        showSafePopup({
            type: 'confirm',
            title: 'Toggle User Status',
            message: 'Are you sure you want to change this user\'s status? This will affect their ability to access the system.',
            confirmText: 'Toggle Status',
            confirmClass: 'bg-blue-600 hover:bg-blue-700',
            onConfirm: function() {
                fetch(`<?= site_url('superadmin/toggleUserStatus') ?>/${userId}`, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSafeToast(data.message, 'success');
                        loadUsers(); // Reload users to reflect changes
                    } else {
                        showSafeToast(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error toggling user status:', error);
                    showSafeToast('Failed to update user status', 'error');
                });
            }
        });
    }

    function deleteUser(userId) {
        showSafePopup({
            type: 'danger',
            title: 'Delete User',
            message: 'Are you sure you want to delete this user? This action cannot be undone and will permanently remove all user data.',
            confirmText: 'Delete User',
            confirmClass: 'bg-red-600 hover:bg-red-700',
            onConfirm: function() {
                fetch(`<?= site_url('superadmin/deleteUser') ?>/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showSafeToast(data.message, 'success');
                        loadUsers(); // Reload users to reflect changes
                    } else {
                        showSafeToast(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error deleting user:', error);
                    showSafeToast('Failed to delete user', 'error');
                });
            }
        });
    }

    function viewUserDetails(userId) {
        fetch(`<?= site_url('superadmin/getUserDetails') ?>/${userId}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUserDetailsModal(data.user);
            } else {
                showError(data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching user details:', error);
            showError('Failed to load user details');
        });
    }

    function showUserDetailsModal(user) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
        modal.innerHTML = `
            <div class="bg-white rounded-xl shadow-2xl max-w-5xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300">
                <!-- Modal Header -->
                <div class="flex justify-between items-center p-6 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-purple-50">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-indigo-600"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800">User Details</h2>
                    </div>
                    <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full p-2 transition-all duration-200">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Modal Content -->
                <div class="p-6 bg-gray-50">
                    <!-- User Details Content -->
                    <div>
                        <!-- Basic Information -->
                        <div class="grid md:grid-cols-2 gap-6 mb-6">
                            <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-info-circle text-blue-600"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-800">Basic Information</h3>
                                </div>
                                <div class="space-y-4">
                                    <div class="border-l-4 border-blue-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Full Name</label>
                                        <p class="text-gray-900 font-semibold text-lg">${user.name}</p>
                                    </div>
                                    <div class="border-l-4 border-green-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Email Address</label>
                                        <p class="text-gray-900 font-medium">${user.email}</p>
                                    </div>
                                    ${user.phone ? `
                                    <div class="border-l-4 border-purple-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Phone Number</label>
                                        <p class="text-gray-900 font-medium">${user.phone}</p>
                                    </div>
                                    ` : ''}
                                    <div class="border-l-4 border-yellow-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Status</label>
                                        <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full ${user.status === 'active' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'}">
                                            <i class="fas fa-${user.status === 'active' ? 'check-circle' : 'times-circle'} mr-1"></i>${user.status === 'active' ? 'Active' : 'Inactive'}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200">
                                <div class="flex items-center mb-4">
                                    <div class="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-clipboard-list text-indigo-600"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-800">Additional Details</h3>
                                </div>
                                <div class="space-y-4">
                                    <div class="border-l-4 border-indigo-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">School Assignment</label>
                                        <p class="text-gray-900 font-medium">${user.school_name || 'No School Assigned'}</p>
                                    </div>
                                    ${user.designation ? `
                                    <div class="border-l-4 border-orange-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Designation</label>
                                        <p class="text-gray-900 font-medium">${user.designation}</p>
                                    </div>
                                    ` : ''}
                                    <div class="border-l-4 border-green-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Registration Date</label>
                                        <p class="text-gray-900 font-medium">${new Date(user.created_at).toLocaleDateString()}</p>
                                    </div>
                                    <div class="border-l-4 border-blue-500 pl-4">
                                        <label class="block text-sm font-medium text-gray-500 mb-1">Last Updated</label>
                                        <p class="text-gray-900 font-medium">${new Date(user.updated_at).toLocaleDateString()}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex justify-center space-x-4 pt-6 border-t border-gray-200">
                            <button onclick="this.closest('.fixed').remove()"
                                    class="px-8 py-3 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-xl hover:from-gray-600 hover:to-gray-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                                <i class="fas fa-times mr-2"></i>Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Search and filter functionality
    document.addEventListener('DOMContentLoaded', function() {
        const userSearch = document.getElementById('user-search');
        const userRoleFilter = document.getElementById('user-role-filter');

        if (userSearch) {
            userSearch.addEventListener('input', function() {
                filterUsers();
            });
        }

        if (userRoleFilter) {
            userRoleFilter.addEventListener('change', function() {
                filterUsers();
            });
        }
    });

    function filterUsers() {
        const searchTerm = document.getElementById('user-search').value.toLowerCase();
        const statusFilter = document.getElementById('user-role-filter').value;

        filteredUsers = allUsers.filter(user => {
            const matchesSearch = user.name.toLowerCase().includes(searchTerm) ||
                                user.email.toLowerCase().includes(searchTerm) ||
                                (user.school_name && user.school_name.toLowerCase().includes(searchTerm));

            const matchesStatus = !statusFilter || user.status === statusFilter;

            return matchesSearch && matchesStatus;
        });

        displayUsers(filteredUsers);
    }

    // Audit Logs Functions
    let currentAuditPage = 1;
    let auditFilters = {};

    function loadAuditLogs(page = 1) {
        currentAuditPage = page;
        const params = new URLSearchParams({
            page: page,
            per_page: 20,
            ...auditFilters
        });

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch(`<?= site_url('superadmin/getAuditLogs') ?>?${params}`, {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Audit logs response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Audit logs data:', data);
            if (data.success) {
                displayAuditLogs(data.data.logs);
                updateAuditPagination(data.data);
            } else {
                console.error('Audit logs error:', data.message);
                showError('Failed to load audit logs: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading audit logs:', error);
            showError('Failed to load audit logs');
        });
    }

    function displayAuditLogs(logs) {
        const container = document.getElementById('audit-logs-list');
        const loading = document.getElementById('audit-logs-loading');

        loading.style.display = 'none';
        container.classList.remove('hidden');

        if (logs.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-history text-4xl mb-4"></i>
                    <p>No audit logs found</p>
                </div>
            `;
            return;
        }

        const logsHTML = logs.map(log => {
            const severityClass = getSeverityClass(log.severity);
            const statusClass = getStatusClass(log.status);
            const date = new Date(log.created_at).toLocaleString();

            return `
                <div class="bg-gray-50 p-4 rounded-lg border-l-4 ${severityClass}">
                    <div class="flex justify-between items-start">
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <span class="font-semibold text-gray-900">${log.action.toUpperCase()}</span>
                                <span class="text-sm text-gray-600">${log.entity_type}</span>
                                <span class="px-2 py-1 rounded-full text-xs font-medium ${statusClass}">
                                    ${log.status.toUpperCase()}
                                </span>
                                <span class="px-2 py-1 rounded-full text-xs font-medium ${getSeverityBadgeClass(log.severity)}">
                                    ${log.severity.toUpperCase()}
                                </span>
                            </div>
                            <p class="text-gray-700 mb-2">${log.description || 'No description'}</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-user mr-1"></i>${log.user_name || 'System'}</span>
                                <span><i class="fas fa-school mr-1"></i>${log.school_name || 'N/A'}</span>
                                <span><i class="fas fa-clock mr-1"></i>${date}</span>
                                ${log.ip_address ? `<span><i class="fas fa-globe mr-1"></i>${log.ip_address}</span>` : ''}
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            ${log.old_values || log.new_values ? `
                                <button onclick="viewAuditDetails(${log.id})" class="text-indigo-600 hover:text-indigo-800" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        container.innerHTML = logsHTML;
    }

    function getSeverityClass(severity) {
        switch(severity) {
            case 'critical': return 'border-red-500';
            case 'high': return 'border-orange-500';
            case 'medium': return 'border-yellow-500';
            case 'low': return 'border-green-500';
            default: return 'border-gray-500';
        }
    }

    function getSeverityBadgeClass(severity) {
        switch(severity) {
            case 'critical': return 'bg-red-100 text-red-800';
            case 'high': return 'bg-orange-100 text-orange-800';
            case 'medium': return 'bg-yellow-100 text-yellow-800';
            case 'low': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function getStatusClass(status) {
        switch(status) {
            case 'success': return 'bg-green-100 text-green-800';
            case 'failed': return 'bg-red-100 text-red-800';
            case 'warning': return 'bg-yellow-100 text-yellow-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function updateAuditPagination(data) {
        const pagination = document.getElementById('audit-pagination');
        const info = document.getElementById('audit-pagination-info');
        const buttons = document.getElementById('audit-pagination-buttons');

        if (data.totalPages <= 1) {
            pagination.classList.add('hidden');
            return;
        }

        pagination.classList.remove('hidden');

        const start = ((data.page - 1) * data.perPage) + 1;
        const end = Math.min(data.page * data.perPage, data.total);
        info.textContent = `Showing ${start}-${end} of ${data.total} logs`;

        let buttonsHTML = '';

        // Previous button
        if (data.page > 1) {
            buttonsHTML += `<button onclick="loadAuditLogs(${data.page - 1})" class="px-3 py-2 border rounded-lg hover:bg-gray-50">Previous</button>`;
        }

        // Page numbers
        const startPage = Math.max(1, data.page - 2);
        const endPage = Math.min(data.totalPages, data.page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === data.page ? 'bg-indigo-600 text-white' : 'hover:bg-gray-50';
            buttonsHTML += `<button onclick="loadAuditLogs(${i})" class="px-3 py-2 border rounded-lg ${activeClass}">${i}</button>`;
        }

        // Next button
        if (data.page < data.totalPages) {
            buttonsHTML += `<button onclick="loadAuditLogs(${data.page + 1})" class="px-3 py-2 border rounded-lg hover:bg-gray-50">Next</button>`;
        }

        buttons.innerHTML = buttonsHTML;
    }

    function loadAuditStats() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/getAuditStats') ?>', {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Audit stats response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Audit stats data:', data);
            if (data.success) {
                updateAuditStats(data.stats);
            } else {
                console.error('Audit stats error:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading audit stats:', error);
        });
    }

    function updateAuditStats(stats) {
        document.getElementById('total-actions').textContent = stats.total_actions || 0;

        // Calculate today's actions
        const today = new Date().toISOString().split('T')[0];
        const todayActions = stats.actions_by_type?.reduce((sum, action) => sum + parseInt(action.count), 0) || 0;
        document.getElementById('today-actions').textContent = todayActions;

        // Calculate critical events
        const criticalEvents = stats.actions_by_severity?.find(s => s.severity === 'critical')?.count || 0;
        document.getElementById('critical-events').textContent = criticalEvents;

        // Calculate failed actions
        const failedActions = stats.actions_by_status?.find(s => s.status === 'failed')?.count || 0;
        document.getElementById('failed-actions').textContent = failedActions;
    }

    function loadAuditFilterOptions() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/getAuditFilterOptions') ?>', {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateAuditFilters(data.options);
            }
        })
        .catch(error => {
            console.error('Error loading filter options:', error);
        });
    }

    function populateAuditFilters(options) {
        // Populate action filter
        const actionFilter = document.getElementById('audit-action-filter');
        actionFilter.innerHTML = '<option value="">All Actions</option>';
        options.actions.forEach(action => {
            actionFilter.innerHTML += `<option value="${action}">${action.toUpperCase()}</option>`;
        });

        // Populate school filter
        const schoolFilter = document.getElementById('audit-school-filter');
        schoolFilter.innerHTML = '<option value="">All Schools</option>';
        options.schools.forEach(school => {
            schoolFilter.innerHTML += `<option value="${school.id}">${school.name}</option>`;
        });
    }

    function refreshAuditLogs() {
        loadAuditLogs(1);
        loadAuditStats();
    }

    function exportAuditLogs() {
        const params = new URLSearchParams(auditFilters);
        window.open(`<?= site_url('superadmin/exportAuditLogs') ?>?${params}`, '_blank');
    }

    function applyAuditFilters() {
        auditFilters = {
            search: document.getElementById('audit-search').value,
            date_from: document.getElementById('audit-date-from').value,
            date_to: document.getElementById('audit-date-to').value,
            action: document.getElementById('audit-action-filter').value,
            severity: document.getElementById('audit-severity-filter').value,
            school_id: document.getElementById('audit-school-filter').value
        };

        // Remove empty filters
        Object.keys(auditFilters).forEach(key => {
            if (!auditFilters[key]) {
                delete auditFilters[key];
            }
        });

        loadAuditLogs(1);
    }

    function viewAuditDetails(logId) {
        // This would show a modal with detailed audit information
        // For now, we'll just show an alert
        showSuccess('Audit details view would be implemented here for log ID: ' + logId);
    }

    // Add event listeners for audit filters
    document.addEventListener('DOMContentLoaded', function() {
        const auditSearch = document.getElementById('audit-search');
        const auditDateFrom = document.getElementById('audit-date-from');
        const auditDateTo = document.getElementById('audit-date-to');
        const auditActionFilter = document.getElementById('audit-action-filter');
        const auditSeverityFilter = document.getElementById('audit-severity-filter');
        const auditSchoolFilter = document.getElementById('audit-school-filter');

        // Add debounced search
        let searchTimeout;
        if (auditSearch) {
            auditSearch.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(applyAuditFilters, 500);
            });
        }

        // Add change listeners for other filters
        [auditDateFrom, auditDateTo, auditActionFilter, auditSeverityFilter, auditSchoolFilter].forEach(element => {
            if (element) {
                element.addEventListener('change', applyAuditFilters);
            }
        });
    });

    // Mobile sidebar toggle function
    function toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('-translate-x-full');
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        const sidebar = document.getElementById('sidebar');
        const toggleButton = event.target.closest('button[onclick="toggleSidebar()"]');

        if (!sidebar.contains(event.target) && !toggleButton && window.innerWidth < 1024) {
            sidebar.classList.add('-translate-x-full');
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        const sidebar = document.getElementById('sidebar');
        if (window.innerWidth >= 1024) {
            sidebar.classList.remove('-translate-x-full');
        }
    });

    // Recent Activities Functions
    function loadRecentActivities() {
        const activitiesList = document.getElementById('recent-activities-list');

        activitiesList.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-spinner fa-spin text-2xl mb-3"></i>
                <p>Loading recent activities...</p>
            </div>
        `;

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/getRecentActivities') ?>', {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRecentActivities(data.activities);
            } else {
                activitiesList.innerHTML = `
                    <div class="text-center py-8 text-red-500">
                        <i class="fas fa-exclamation-triangle text-2xl mb-3"></i>
                        <p>Failed to load activities: ${data.message}</p>
                        <button onclick="loadRecentActivities()" class="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Try Again
                        </button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading activities:', error);
            activitiesList.innerHTML = `
                <div class="text-center py-8 text-red-500">
                    <i class="fas fa-exclamation-triangle text-2xl mb-3"></i>
                    <p>Network error occurred. Please try again.</p>
                    <button onclick="loadRecentActivities()" class="mt-3 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        Try Again
                    </button>
                </div>
            `;
        });
    }

    function displayRecentActivities(activities) {
        const activitiesList = document.getElementById('recent-activities-list');

        if (!activities || activities.length === 0) {
            activitiesList.innerHTML = `
                <div class="text-center py-12 text-gray-500">
                    <i class="fas fa-clock text-4xl mb-4"></i>
                    <p class="text-lg font-medium">No recent activities</p>
                    <p class="text-sm mt-2">Activities will appear here as actions are performed</p>
                </div>
            `;
            return;
        }

        let html = '';
        activities.forEach(activity => {
            const timeAgo = formatTimeAgo(activity.time);
            const colorClass = getColorClass(activity.color);

            html += `
                <div class="flex items-center p-4 bg-gradient-to-r ${colorClass.bg} rounded-lg border-l-4 ${colorClass.border} hover:shadow-md transition-shadow">
                    <div class="w-10 h-10 ${colorClass.iconBg} rounded-full flex items-center justify-center mr-4">
                        <i class="${activity.icon} text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-semibold text-gray-800">${escapeHtml(activity.title)}</p>
                        <p class="text-sm text-gray-600">${escapeHtml(activity.description)}</p>
                    </div>
                    <span class="text-sm text-gray-500 bg-white px-2 py-1 rounded-full">${timeAgo}</span>
                </div>
            `;
        });

        activitiesList.innerHTML = html;
    }

    function getColorClass(color) {
        const colorClasses = {
            'blue': {
                bg: 'from-blue-50 to-blue-100',
                border: 'border-blue-500',
                iconBg: 'bg-blue-600'
            },
            'green': {
                bg: 'from-green-50 to-green-100',
                border: 'border-green-500',
                iconBg: 'bg-green-600'
            },
            'yellow': {
                bg: 'from-yellow-50 to-yellow-100',
                border: 'border-yellow-500',
                iconBg: 'bg-yellow-600'
            },
            'red': {
                bg: 'from-red-50 to-red-100',
                border: 'border-red-500',
                iconBg: 'bg-red-600'
            },
            'purple': {
                bg: 'from-purple-50 to-purple-100',
                border: 'border-purple-500',
                iconBg: 'bg-purple-600'
            },
            'gray': {
                bg: 'from-gray-50 to-gray-100',
                border: 'border-gray-500',
                iconBg: 'bg-gray-600'
            }
        };

        return colorClasses[color] || colorClasses['gray'];
    }

    function formatTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) {
            return 'Just now';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} day${days > 1 ? 's' : ''} ago`;
        }
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Load recent activities when dashboard loads
    document.addEventListener('DOMContentLoaded', function() {
        loadRecentActivities();
        initializeDashboardCharts();

        // Auto-refresh activities every 5 minutes
        setInterval(loadRecentActivities, 300000);

        // Notification functionality
        setupNotifications();
    });

    // Dashboard Charts Initialization
    function initializeDashboardCharts() {
        loadDashboardData();
    }

    function loadDashboardData() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        // Load dashboard analytics data
        fetch('<?= site_url('superadmin/getAnalyticsData') ?>?date_range=30', {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            console.log('Dashboard data loaded:', data);
            if (data.success) {
                console.log('Dashboard chart data:', data.data.chart_data);
                console.log('Dashboard subscription stats:', data.data.subscription_stats);
                initializeDashboardChartsWithData(data.data);
            } else {
                console.error('Failed to load dashboard data:', data.message);
                initializeDashboardChartsWithData(null);
            }
        })
        .catch(error => {
            console.error('Error loading dashboard data:', error);
            initializeDashboardChartsWithData(null);
        });
    }

    function initializeDashboardChartsWithData(data) {
        // School Growth Chart
        const schoolGrowthCtx = document.getElementById('schoolGrowthChart');
        if (schoolGrowthCtx) {
            const chartData = data?.chart_data?.schools || {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                data: [2, 3, 1, 4, 2, 3]
            };

            new Chart(schoolGrowthCtx, {
                type: 'line',
                data: {
                    labels: chartData.labels,
                    datasets: [{
                        label: 'New Schools',
                        data: chartData.data,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#3b82f6',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 3,
                        pointHoverRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#3b82f6',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 11
                                }
                            }
                        }
                    }
                }
            });
        }

        // Subscription Distribution Chart
        const subscriptionCtx = document.getElementById('subscriptionChart');
        if (subscriptionCtx) {
            const subscriptionData = data?.subscription_stats || {
                active: 6,
                trial: 5,
                expired: 0,
                pending: 1
            };

            new Chart(subscriptionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Trial', 'Expired', 'Pending'],
                    datasets: [{
                        data: [
                            subscriptionData.active,
                            subscriptionData.trial,
                            subscriptionData.expired,
                            subscriptionData.pending
                        ],
                        backgroundColor: [
                            '#22c55e',
                            '#f59e0b',
                            '#ef4444',
                            '#6366f1'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff',
                        hoverBorderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 15,
                                font: {
                                    size: 11
                                },
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderWidth: 1,
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        }
    }

    // Notification System
    function setupNotifications() {
        const notificationBtn = document.getElementById('notificationBtn');
        const notificationDropdown = document.getElementById('notificationDropdown');
        const refreshBtn = document.getElementById('refreshNotifications');

        // Toggle notification dropdown
        notificationBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationDropdown.classList.toggle('hidden');
            if (!notificationDropdown.classList.contains('hidden')) {
                loadNotifications();
            }
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!notificationDropdown.contains(e.target) && !notificationBtn.contains(e.target)) {
                notificationDropdown.classList.add('hidden');
            }
        });

        // Refresh notifications
        refreshBtn.addEventListener('click', function() {
            loadNotifications();
        });
    }

    function loadNotifications() {
        const notificationList = document.getElementById('notificationList');

        notificationList.innerHTML = '<div class="p-4 text-center text-gray-500"><i class="fas fa-spinner fa-spin"></i> Loading notifications...</div>';

        fetch('/superadmin/getNotifications', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.notifications);
            } else {
                notificationList.innerHTML = '<div class="p-4 text-center text-red-500">Failed to load notifications</div>';
            }
        })
        .catch(error => {
            console.error('Error loading notifications:', error);
            notificationList.innerHTML = '<div class="p-4 text-center text-red-500">Error loading notifications</div>';
        });
    }

    function displayNotifications(notifications) {
        const notificationList = document.getElementById('notificationList');

        if (notifications.length === 0) {
            notificationList.innerHTML = '<div class="p-4 text-center text-gray-500">No new notifications</div>';
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const timeAgo = getTimeAgo(notification.time);
            const colorClass = getNotificationColorClass(notification.color);

            html += `
                <div class="p-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer" onclick="event.preventDefault(); handleNotificationClick('${notification.section || extractSectionFromUrl(notification.action_url)}'); return false;">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
                                <i class="${notification.icon} text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                            <p class="text-sm text-gray-600">${notification.message}</p>
                            <p class="text-xs text-gray-400 mt-1">${timeAgo}</p>
                        </div>
                    </div>
                </div>
            `;
        });

        notificationList.innerHTML = html;
    }

    function getNotificationColorClass(color) {
        const colorMap = {
            'red': 'bg-red-500',
            'orange': 'bg-orange-500',
            'yellow': 'bg-yellow-500',
            'green': 'bg-green-500',
            'blue': 'bg-blue-500',
            'indigo': 'bg-indigo-500',
            'purple': 'bg-purple-500'
        };
        return colorMap[color] || 'bg-gray-500';
    }

    function extractSectionFromUrl(actionUrl) {
        if (!actionUrl) return '';

        // Extract section from URLs like '/superadmin/schools' -> 'schools'
        const urlParts = actionUrl.split('/');
        const lastPart = urlParts[urlParts.length - 1];

        // Map common URL patterns to sections
        const sectionMap = {
            'schools': 'schools',
            'users': 'users',
            'subscriptions': 'subscriptions',
            'analytics': 'analytics',
            'system': 'system',
            'audit': 'audit'
        };

        return sectionMap[lastPart] || '';
    }

    function handleNotificationClick(section) {
        console.log('Notification clicked, navigating to section:', section); // Debug log

        // Prevent default link behavior
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }

        if (section && section.trim() !== '') {
            console.log('Navigating to section:', section); // Debug log

            // Close notification dropdown
            const dropdown = document.getElementById('notificationDropdown');
            if (dropdown) {
                dropdown.classList.add('hidden');
                console.log('Notification dropdown closed');
            }

            // Show the appropriate section
            console.log('Calling showSection with:', section);
            showSection(section);

            // Verify section was shown
            const targetSection = document.getElementById(section + '-section');
            if (targetSection) {
                console.log('Target section found:', targetSection);
                console.log('Section classes:', targetSection.className);
            } else {
                console.log('Target section NOT found:', section + '-section');
            }

            // If it's schools section, show pending tab by default
            if (section === 'schools') {
                console.log('Schools section - showing pending tab');
                setTimeout(() => {
                    if (typeof showSchoolTab === 'function') {
                        showSchoolTab('pending');
                    } else {
                        console.log('showSchoolTab function not found');
                    }
                }, 100);
            }
        } else {
            console.log('No valid section provided:', section);
        }
    }

    function getTimeAgo(dateString) {
        const now = new Date();
        const date = new Date(dateString);
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return Math.floor(diffInSeconds / 60) + ' minutes ago';
        if (diffInSeconds < 86400) return Math.floor(diffInSeconds / 3600) + ' hours ago';
        if (diffInSeconds < 2592000) return Math.floor(diffInSeconds / 86400) + ' days ago';
        return Math.floor(diffInSeconds / 2592000) + ' months ago';
    }

    // Analytics functionality
    let analyticsCharts = {};
    let currentAnalyticsData = null;

    function loadAnalyticsData() {
        console.log('Loading analytics data...');

        // Show loading state
        document.getElementById('analytics-loading').classList.remove('hidden');
        document.getElementById('analytics-content').classList.add('hidden');
        document.getElementById('analytics-error').classList.add('hidden');

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        // Get selected date range
        const dateRange = document.getElementById('analytics-date-range')?.value || '30';
        const startDate = document.getElementById('start-date')?.value;
        const endDate = document.getElementById('end-date')?.value;

        // Build query parameters
        let queryParams = `?date_range=${dateRange}`;
        if (dateRange === 'custom' && startDate && endDate) {
            queryParams += `&start_date=${startDate}&end_date=${endDate}`;
        }

        // Load analytics data from the dedicated endpoint
        fetch('<?= site_url('superadmin/getAnalyticsData') ?>' + queryParams, {
            method: 'GET',
            headers: headers,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            console.log('Analytics data loaded:', data);

            if (data.success) {
                currentAnalyticsData = data.data;
                console.log('Analytics stats:', data.data.stats);
                console.log('Analytics trends:', data.data.trends);
                updateAnalyticsStats(data.data);
                initializeAnalyticsCharts(data.data);

                // Show content
                document.getElementById('analytics-loading').classList.add('hidden');
                document.getElementById('analytics-content').classList.remove('hidden');
            } else {
                throw new Error('Failed to load analytics data: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error loading analytics:', error);
            document.getElementById('analytics-loading').classList.add('hidden');
            document.getElementById('analytics-error').classList.remove('hidden');
        });
    }

    // Refresh analytics data
    function refreshAnalytics() {
        loadAnalyticsData();
    }

    // Export analytics data
    function exportAnalytics() {
        if (!currentAnalyticsData) {
            alert('No data to export. Please load analytics first.');
            return;
        }

        const csvData = generateCSVData(currentAnalyticsData);
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `analytics_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
    }

    // Generate CSV data for export
    function generateCSVData(data) {
        let csv = 'Metric,Current Period,Previous Period,Change (%)\n';

        if (data.trends) {
            Object.keys(data.trends).forEach(key => {
                const trend = data.trends[key];
                csv += `${key.replace('_', ' ').toUpperCase()},${trend.current},${trend.previous},${trend.change}%\n`;
            });
        }

        return csv;
    }

    function updateAnalyticsStats(data) {
        // Update stats cards with data and trends
        updateStatCard('analytics-total-users', 'users-trend', 'users-change', data.stats?.total_users || 0, data.trends?.total_users);
        updateStatCard('analytics-active-schools', 'schools-trend', 'schools-change', data.stats?.active_schools || 0, data.trends?.active_schools);
        updateStatCard('analytics-total-questions', 'questions-trend', 'questions-change', data.stats?.total_questions || 0, data.trends?.total_questions);
        updateStatCard('analytics-monthly-revenue', 'revenue-trend', 'revenue-change', data.stats?.revenue || 0, data.trends?.revenue, true);
    }

    function updateStatCard(valueId, trendId, changeId, value, trend, isCurrency = false) {
        // Update main value
        const formattedValue = isCurrency ? '₹' + value.toLocaleString() : value.toLocaleString();
        document.getElementById(valueId).textContent = formattedValue;

        // Update trend indicator
        if (trend && document.getElementById(trendId)) {
            const trendElement = document.getElementById(trendId);
            const changeElement = document.getElementById(changeId);

            // Clear existing classes
            trendElement.className = 'flex items-center mt-1';

            if (trend.direction === 'up') {
                trendElement.innerHTML = `
                    <i class="fas fa-arrow-up text-green-500 text-xs mr-1"></i>
                    <span class="text-xs text-green-600 font-medium">+${Math.abs(trend.change)}%</span>
                `;
            } else if (trend.direction === 'down') {
                trendElement.innerHTML = `
                    <i class="fas fa-arrow-down text-red-500 text-xs mr-1"></i>
                    <span class="text-xs text-red-600 font-medium">-${Math.abs(trend.change)}%</span>
                `;
            } else {
                trendElement.innerHTML = `
                    <i class="fas fa-minus text-gray-500 text-xs mr-1"></i>
                    <span class="text-xs text-gray-600 font-medium">0%</span>
                `;
            }

            if (changeElement) {
                const prevValue = isCurrency ? '₹' + trend.previous.toLocaleString() : trend.previous.toLocaleString();
                changeElement.textContent = `vs ${prevValue}`;
            }
        }
    }

    function initializeAnalyticsCharts(data) {
        // Destroy existing charts
        Object.values(analyticsCharts).forEach(chart => {
            if (chart) chart.destroy();
        });
        analyticsCharts = {};

        // Usage Chart (Line Chart showing user activity over time)
        const usageCtx = document.getElementById('usageChart');
        if (usageCtx && data.chart_data?.users) {
            analyticsCharts.usage = new Chart(usageCtx, {
                type: 'line',
                data: {
                    labels: data.chart_data.users.labels,
                    datasets: [{
                        label: 'New Users',
                        data: data.chart_data.users.data,
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#6366f1',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#6366f1',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // Revenue Chart (Bar Chart)
        const revenueCtx = document.getElementById('revenueChart');
        if (revenueCtx && data.chart_data?.revenue) {
            analyticsCharts.revenue = new Chart(revenueCtx, {
                type: 'bar',
                data: {
                    labels: data.chart_data.revenue.labels,
                    datasets: [{
                        label: 'Revenue (₹)',
                        data: data.chart_data.revenue.data,
                        backgroundColor: 'rgba(34, 197, 94, 0.8)',
                        borderColor: '#22c55e',
                        borderWidth: 1,
                        borderRadius: 4,
                        borderSkipped: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#22c55e',
                            borderWidth: 1,
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: ₹' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return '₹' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // School Growth Chart (Area Chart)
        const schoolGrowthCtx = document.getElementById('schoolGrowthAnalyticsChart');
        if (schoolGrowthCtx && data.chart_data?.schools) {
            analyticsCharts.schoolGrowth = new Chart(schoolGrowthCtx, {
                type: 'line',
                data: {
                    labels: data.chart_data.schools.labels,
                    datasets: [{
                        label: 'New Schools',
                        data: data.chart_data.schools.data,
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4,
                        fill: true,
                        pointBackgroundColor: '#3b82f6',
                        pointBorderColor: '#ffffff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: '#3b82f6',
                            borderWidth: 1
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            },
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    }
                }
            });
        }

        // Subscription Distribution Chart (Doughnut Chart)
        const subscriptionCtx = document.getElementById('subscriptionAnalyticsChart');
        if (subscriptionCtx && data.subscription_stats) {
            analyticsCharts.subscription = new Chart(subscriptionCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Trial', 'Expired', 'Pending'],
                    datasets: [{
                        data: [
                            data.subscription_stats.active || 0,
                            data.subscription_stats.trial || 0,
                            data.subscription_stats.expired || 0,
                            data.subscription_stats.pending || 0
                        ],
                        backgroundColor: [
                            '#22c55e',
                            '#f59e0b',
                            '#ef4444',
                            '#f97316'
                        ],
                        borderWidth: 2,
                        borderColor: '#ffffff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    // Helper functions to generate sample data
    function generateMonthlyData(total) {
        const data = [];
        const currentMonth = new Date().getMonth();
        for (let i = 0; i < 12; i++) {
            if (i <= currentMonth) {
                // Generate realistic growth data
                const baseValue = Math.floor(total * (i + 1) / (currentMonth + 1));
                const variation = Math.floor(Math.random() * (baseValue * 0.2));
                data.push(Math.max(0, baseValue + variation - (baseValue * 0.1)));
            } else {
                data.push(0);
            }
        }
        return data;
    }

    function generateRevenueData(monthlyRevenue) {
        const data = [];
        const currentMonth = new Date().getMonth();
        for (let i = 0; i < 12; i++) {
            if (i <= currentMonth) {
                // Generate revenue data with some variation
                const baseRevenue = monthlyRevenue * (0.7 + Math.random() * 0.6);
                data.push(Math.floor(baseRevenue));
            } else {
                data.push(0);
            }
        }
        return data;
    }

    function generateSchoolGrowthData(totalSchools) {
        const data = [];
        const currentMonth = new Date().getMonth();
        let cumulative = 0;
        for (let i = 0; i < 12; i++) {
            if (i <= currentMonth) {
                const newSchools = Math.floor(totalSchools / (currentMonth + 1)) + Math.floor(Math.random() * 3);
                cumulative += newSchools;
                data.push(newSchools);
            } else {
                data.push(0);
            }
        }
        return data;
    }

    // Event listeners for analytics controls
    document.addEventListener('DOMContentLoaded', function() {
        // Date range selector
        const dateRangeSelect = document.getElementById('analytics-date-range');
        const customDateRange = document.getElementById('custom-date-range');

        if (dateRangeSelect) {
            dateRangeSelect.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customDateRange?.classList.remove('hidden');
                    // Set default dates
                    const endDate = new Date();
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() - 30);

                    document.getElementById('start-date').value = startDate.toISOString().split('T')[0];
                    document.getElementById('end-date').value = endDate.toISOString().split('T')[0];
                } else {
                    customDateRange?.classList.add('hidden');
                    // Auto-refresh when changing predefined ranges
                    if (document.getElementById('analytics-content') && !document.getElementById('analytics-content').classList.contains('hidden')) {
                        loadAnalyticsData();
                    }
                }
            });
        }

        // Custom date inputs
        const startDateInput = document.getElementById('start-date');
        const endDateInput = document.getElementById('end-date');

        if (startDateInput && endDateInput) {
            [startDateInput, endDateInput].forEach(input => {
                input.addEventListener('change', function() {
                    if (dateRangeSelect?.value === 'custom' && startDateInput.value && endDateInput.value) {
                        // Validate date range
                        if (new Date(startDateInput.value) > new Date(endDateInput.value)) {
                            alert('Start date cannot be after end date');
                            return;
                        }
                        // Auto-refresh when dates change
                        if (document.getElementById('analytics-content') && !document.getElementById('analytics-content').classList.contains('hidden')) {
                            loadAnalyticsData();
                        }
                    }
                });
            });
        }

        // Auto-refresh analytics every 5 minutes
        setInterval(function() {
            if (document.getElementById('analytics-content') && !document.getElementById('analytics-content').classList.contains('hidden')) {
                console.log('Auto-refreshing analytics...');
                loadAnalyticsData();
            }
        }, 300000); // 5 minutes
    });

    // System Settings Functions
    function loadSystemSettings() {
        console.log('Loading system settings...');

        // Show loading state
        document.getElementById('settings-loading').classList.remove('hidden');
        document.getElementById('settings-content').classList.add('hidden');
        document.getElementById('settings-error').classList.add('hidden');

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/getSystemSettings') ?>', {
            method: 'GET',
            headers: headers
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('System settings loaded:', data.data);
                displaySystemSettings(data.data);

                // Show content
                document.getElementById('settings-loading').classList.add('hidden');
                document.getElementById('settings-content').classList.remove('hidden');
            } else {
                throw new Error('Failed to load system settings: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error loading system settings:', error);
            document.getElementById('settings-loading').classList.add('hidden');
            document.getElementById('settings-error').classList.remove('hidden');
        });
    }

    function displaySystemSettings(settings) {
        const container = document.getElementById('settings-content');
        let html = '';

        // Group settings by category
        const categories = {
            'platform': {
                title: 'Platform Configuration',
                icon: 'fas fa-server',
                color: 'blue'
            },
            'notifications': {
                title: 'Email Notifications',
                icon: 'fas fa-envelope',
                color: 'green'
            },
            'system': {
                title: 'System Configuration',
                icon: 'fas fa-cog',
                color: 'indigo'
            },
            'security': {
                title: 'Security Settings',
                icon: 'fas fa-shield-alt',
                color: 'red'
            }
        };

        Object.keys(categories).forEach(categoryKey => {
            if (settings[categoryKey]) {
                const category = categories[categoryKey];
                html += `
                    <div class="bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-200 mb-6">
                        <div class="flex items-center p-6 border-b border-gray-200">
                            <div class="w-8 h-8 bg-${category.color}-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="${category.icon} text-${category.color}-600"></i>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-800">${category.title}</h3>
                        </div>
                        <div class="p-6">
                            <div class="grid md:grid-cols-2 gap-6">
                `;

                Object.keys(settings[categoryKey]).forEach(settingKey => {
                    const setting = settings[categoryKey][settingKey];
                    html += generateSettingField(settingKey, setting, categoryKey);
                });

                html += `
                            </div>
                        </div>
                    </div>
                `;
            }
        });

        // Add save button
        html += `
            <div class="flex justify-center pt-6 border-t border-gray-200">
                <button onclick="saveSystemSettings()" class="px-8 py-3 bg-gradient-to-r from-indigo-500 to-indigo-600 text-white rounded-xl hover:from-indigo-600 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                    <i class="fas fa-save mr-2"></i>Save All Settings
                </button>
            </div>
        `;

        container.innerHTML = html;
    }

    function generateSettingField(key, setting, category) {
        const isEditable = setting.is_editable;
        const fieldId = `setting_${key}`;
        const readonlyClass = !isEditable ? 'bg-gray-100 cursor-not-allowed' : '';
        const readonlyAttr = !isEditable ? 'readonly disabled' : '';

        let fieldHtml = '';
        let lockIcon = !isEditable ? '<i class="fas fa-lock text-gray-400 ml-2" title="This setting cannot be modified"></i>' : '';

        switch (setting.type) {
            case 'boolean':
                fieldHtml = `
                    <div class="border-l-4 border-indigo-500 pl-4">
                        <label class="flex items-center justify-between">
                            <div>
                                <span class="text-sm font-medium text-gray-700">${formatSettingLabel(key)}</span>
                                ${lockIcon}
                                ${setting.description ? `<p class="text-xs text-gray-500 mt-1">${setting.description}</p>` : ''}
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="${fieldId}" ${setting.value ? 'checked' : ''} ${readonlyAttr}
                                       class="toggle-switch ${readonlyClass}" data-key="${key}" data-category="${category}"
                                       onchange="updateSettingValue('${key}', this.checked, '${category}')">
                            </div>
                        </label>
                    </div>
                `;
                break;
            case 'integer':
            case 'decimal':
                fieldHtml = `
                    <div class="border-l-4 border-green-500 pl-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            ${formatSettingLabel(key)} ${lockIcon}
                        </label>
                        ${setting.description ? `<p class="text-xs text-gray-500 mb-2">${setting.description}</p>` : ''}
                        <input type="number" id="${fieldId}" value="${setting.value}" ${readonlyAttr}
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${readonlyClass}"
                               data-key="${key}" data-category="${category}"
                               onchange="updateSettingValue('${key}', this.value, '${category}')">
                    </div>
                `;
                break;
            default: // string
                if (key.includes('email')) {
                    fieldHtml = `
                        <div class="border-l-4 border-purple-500 pl-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                ${formatSettingLabel(key)} ${lockIcon}
                            </label>
                            ${setting.description ? `<p class="text-xs text-gray-500 mb-2">${setting.description}</p>` : ''}
                            <input type="email" id="${fieldId}" value="${setting.value}" ${readonlyAttr}
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${readonlyClass}"
                                   data-key="${key}" data-category="${category}"
                                   onchange="updateSettingValue('${key}', this.value, '${category}')">
                        </div>
                    `;
                } else if (key.includes('frequency') || key.includes('timezone')) {
                    let options = '';
                    if (key.includes('frequency')) {
                        options = '<option value="daily">Daily</option><option value="weekly">Weekly</option><option value="monthly">Monthly</option>';
                    } else if (key.includes('timezone')) {
                        options = '<option value="Asia/Kolkata">Asia/Kolkata (IST)</option><option value="UTC">UTC</option><option value="America/New_York">America/New_York (EST)</option>';
                    }

                    fieldHtml = `
                        <div class="border-l-4 border-yellow-500 pl-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                ${formatSettingLabel(key)} ${lockIcon}
                            </label>
                            ${setting.description ? `<p class="text-xs text-gray-500 mb-2">${setting.description}</p>` : ''}
                            <select id="${fieldId}" ${readonlyAttr}
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${readonlyClass}"
                                    data-key="${key}" data-category="${category}"
                                    onchange="updateSettingValue('${key}', this.value, '${category}')">
                                ${options}
                            </select>
                        </div>
                    `;
                } else {
                    fieldHtml = `
                        <div class="border-l-4 border-blue-500 pl-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                ${formatSettingLabel(key)} ${lockIcon}
                            </label>
                            ${setting.description ? `<p class="text-xs text-gray-500 mb-2">${setting.description}</p>` : ''}
                            <input type="text" id="${fieldId}" value="${setting.value}" ${readonlyAttr}
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 ${readonlyClass}"
                                   data-key="${key}" data-category="${category}"
                                   onchange="updateSettingValue('${key}', this.value, '${category}')">
                        </div>
                    `;
                }
                break;
        }

        return fieldHtml;
    }

    function formatSettingLabel(key) {
        return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    let settingsChanges = {};

    function updateSettingValue(key, value, category) {
        // Store the change
        if (!settingsChanges[category]) {
            settingsChanges[category] = {};
        }
        settingsChanges[category][key] = value;

        console.log('Setting changed:', key, '=', value);

        // Show visual feedback
        const field = document.getElementById(`setting_${key}`);
        if (field) {
            field.classList.add('border-yellow-400', 'bg-yellow-50');
            setTimeout(() => {
                field.classList.remove('border-yellow-400', 'bg-yellow-50');
            }, 1000);
        }
    }

    function saveSystemSettings() {
        if (Object.keys(settingsChanges).length === 0) {
            showNotification('No changes to save', 'info');
            return;
        }

        const button = event.target;
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
        button.disabled = true;

        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        const headers = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };

        if (csrfToken) {
            headers['X-CSRF-TOKEN'] = csrfToken.getAttribute('content');
        }

        fetch('<?= site_url('superadmin/updateSystemSettings') ?>', {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(settingsChanges)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                settingsChanges = {}; // Clear changes

                // Remove visual indicators
                document.querySelectorAll('[data-key]').forEach(field => {
                    field.classList.remove('border-yellow-400', 'bg-yellow-50');
                });
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);
            showNotification('Failed to save settings', 'error');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    // Load system settings when the section is shown
    document.addEventListener('DOMContentLoaded', function() {
        // Load settings when system section is first shown
        const systemMenuItem = document.querySelector('[onclick="showSection(\'system\')"]');
        if (systemMenuItem) {
            systemMenuItem.addEventListener('click', function() {
                // Only load if not already loaded
                if (document.getElementById('settings-content').classList.contains('hidden')) {
                    loadSystemSettings();
                }
            });
        }
    });

    // Safe Action Popup System (Non-intrusive)
    let safePopupCallback = null;

    function showSafePopup(config) {
        console.log('showSafePopup called with config:', config);
        const popup = document.getElementById('safe-action-popup');
        const icon = document.getElementById('safe-popup-icon');
        const title = document.getElementById('safe-popup-title');
        const message = document.getElementById('safe-popup-message');
        const input = document.getElementById('safe-popup-input');
        const textarea = document.getElementById('safe-popup-textarea');
        const confirmBtn = document.getElementById('safe-popup-confirm');

        // Set icon and colors
        const iconConfig = {
            confirm: { icon: 'fas fa-question-circle', bg: 'bg-blue-100', color: 'text-blue-600' },
            warning: { icon: 'fas fa-exclamation-triangle', bg: 'bg-yellow-100', color: 'text-yellow-600' },
            danger: { icon: 'fas fa-exclamation-circle', bg: 'bg-red-100', color: 'text-red-600' }
        };

        const currentIcon = iconConfig[config.type] || iconConfig.confirm;
        icon.className = `w-12 h-12 rounded-full flex items-center justify-center mr-4 ${currentIcon.bg}`;
        icon.innerHTML = `<i class="${currentIcon.icon} ${currentIcon.color}"></i>`;

        // Set content
        title.textContent = config.title || 'Confirm Action';
        message.textContent = config.message || 'Are you sure?';

        // Handle input field
        if (config.showInput) {
            input.classList.remove('hidden');
            textarea.value = '';
            textarea.focus();
        } else {
            input.classList.add('hidden');
        }

        // Set button text and color
        confirmBtn.textContent = config.confirmText || 'Confirm';
        confirmBtn.className = `px-4 py-2 text-white rounded-md transition-colors ${config.confirmClass || 'bg-blue-600 hover:bg-blue-700'}`;

        // Store callback
        safePopupCallback = config.onConfirm;

        // Show popup
        popup.classList.remove('hidden');
        document.getElementById('safe-popup-content').classList.remove('scale-95');
        document.getElementById('safe-popup-content').classList.add('scale-100');
    }

    function closeSafePopup() {
        const popup = document.getElementById('safe-action-popup');
        document.getElementById('safe-popup-content').classList.add('scale-95');
        document.getElementById('safe-popup-content').classList.remove('scale-100');
        setTimeout(() => {
            popup.classList.add('hidden');
        }, 200);
        safePopupCallback = null;
    }

    function confirmSafeAction() {
        const textarea = document.getElementById('safe-popup-textarea');
        const inputValue = textarea.value.trim();

        if (safePopupCallback) {
            const result = safePopupCallback(inputValue);
            if (result !== false) {
                closeSafePopup();
            }
        } else {
            closeSafePopup();
        }
    }

    // Safe toast notifications
    function showSafeToast(message, type = 'success') {
        const container = document.getElementById('safe-toast-container');
        const toast = document.createElement('div');

        const typeConfig = {
            success: { bg: 'bg-green-500', icon: 'fas fa-check-circle' },
            error: { bg: 'bg-red-500', icon: 'fas fa-exclamation-circle' },
            warning: { bg: 'bg-yellow-500', icon: 'fas fa-exclamation-triangle' },
            info: { bg: 'bg-blue-500', icon: 'fas fa-info-circle' }
        };

        const config = typeConfig[type] || typeConfig.success;

        toast.className = `flex items-center p-4 rounded-lg shadow-lg text-white transform translate-x-full transition-transform duration-300 ${config.bg}`;
        toast.innerHTML = `
            <i class="${config.icon} mr-3"></i>
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.remove()" class="ml-3 hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(toast);

        setTimeout(() => toast.classList.remove('translate-x-full'), 10);
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // Event listeners for safe popup
    document.getElementById('safe-popup-cancel').addEventListener('click', closeSafePopup);
    document.getElementById('safe-popup-confirm').addEventListener('click', confirmSafeAction);
    document.getElementById('safe-action-popup').addEventListener('click', function(e) {
        if (e.target === this) closeSafePopup();
    });

</script>

<!-- Include Superadmin Profile JavaScript -->
<script src="<?= base_url('js/superadmin_profile.js') ?>"></script>

</body>
</html>
