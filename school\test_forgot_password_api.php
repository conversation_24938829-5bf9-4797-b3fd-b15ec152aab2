<?php
/**
 * Test Forgot Password API Endpoint
 * Run this from the school directory: php test_forgot_password_api.php
 */

echo "=== Testing Forgot Password API Endpoint ===\n\n";

// Test the API endpoint directly
$baseUrl = 'http://localhost/schoolquestionbank/school/public';
$endpoint = $baseUrl . '/forgot-password/send';

// Test data
$testData = [
    'email' => '<EMAIL>', // Known school admin email
    'user_type' => 'school'
];

echo "1. Testing API endpoint: $endpoint\n";
echo "2. Test data: " . json_encode($testData) . "\n\n";

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt_array($ch, [
    CURLOPT_URL => $endpoint,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query($testData),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTPHEADER => [
        'X-Requested-With: XMLHttpRequest',
        'Content-Type: application/x-www-form-urlencoded'
    ],
    CURLOPT_COOKIEJAR => 'cookies.txt',
    CURLOPT_COOKIEFILE => 'cookies.txt'
]);

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

if (curl_error($ch)) {
    echo "❌ cURL Error: " . curl_error($ch) . "\n";
    curl_close($ch);
    exit(1);
}

curl_close($ch);

echo "3. Response Details:\n";
echo "   - HTTP Code: $httpCode\n";
echo "   - Content Type: $contentType\n";
echo "   - Response Length: " . strlen($response) . " bytes\n\n";

echo "4. Raw Response:\n";
echo "---START RESPONSE---\n";
echo $response;
echo "\n---END RESPONSE---\n\n";

// Try to parse as JSON
$jsonData = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON Response:\n";
    echo "   - Success: " . ($jsonData['success'] ? 'true' : 'false') . "\n";
    echo "   - Message: " . ($jsonData['message'] ?? 'N/A') . "\n";
    
    if ($jsonData['success']) {
        echo "✅ Forgot password request successful!\n";
    } else {
        echo "⚠️ Forgot password request failed: " . $jsonData['message'] . "\n";
    }
} else {
    echo "❌ Invalid JSON Response\n";
    echo "   - JSON Error: " . json_last_error_msg() . "\n";
    
    // Check if it's HTML (redirect or error page)
    if (strpos($response, '<html') !== false || strpos($response, '<!DOCTYPE') !== false) {
        echo "   - Response appears to be HTML (possible redirect or error page)\n";
        
        // Extract title if present
        if (preg_match('/<title>(.*?)<\/title>/i', $response, $matches)) {
            echo "   - Page Title: " . $matches[1] . "\n";
        }
        
        // Check for error messages
        if (preg_match('/error|exception|fatal/i', $response)) {
            echo "   - Response contains error indicators\n";
        }
    }
}

echo "\n=== Test Complete ===\n";

// Clean up
if (file_exists('cookies.txt')) {
    unlink('cookies.txt');
}

// Additional test: Check if the endpoint exists
echo "\n=== Additional Checks ===\n";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $endpoint,
    CURLOPT_NOBODY => true,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true
]);

curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    echo "✅ Endpoint is accessible (HTTP 200)\n";
} elseif ($httpCode === 404) {
    echo "❌ Endpoint not found (HTTP 404)\n";
} elseif ($httpCode === 405) {
    echo "⚠️ Method not allowed (HTTP 405) - endpoint exists but may not accept POST\n";
} else {
    echo "⚠️ Unexpected HTTP code: $httpCode\n";
}

echo "\n📋 Troubleshooting Tips:\n";
echo "1. Make sure Apache/Nginx is running\n";
echo "2. Check if the route is defined in Routes.php\n";
echo "3. Verify the ForgotPassword controller exists\n";
echo "4. Check Apache error logs for PHP errors\n";
echo "5. Ensure CSRF protection is properly configured\n";
