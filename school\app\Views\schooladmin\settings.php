<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($school_name) ? esc($school_name) : 'School' ?> - Settings</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .settings-tab { 
            transition: all 0.3s ease; 
            cursor: pointer;
        }
        .settings-tab:hover { 
            background-color: rgba(99, 102, 241, 0.05);
            border-radius: 6px;
        }
        .settings-tab.active {
            background-color: rgba(99, 102, 241, 0.1);
            border-radius: 6px;
        }
        .readonly-field {
            background-color: #f9fafb !important;
            border-color: #d1d5db !important;
            color: #6b7280 !important;
            cursor: not-allowed !important;
        }
        .settings-tab-content { 
            display: none; 
            animation: fadeIn 0.3s ease-in-out;
        }
        .settings-tab-content.active { 
            display: block; 
        }
        @keyframes fadeIn { 
            from { opacity: 0; transform: translateY(10px); } 
            to { opacity: 1; transform: translateY(0); } 
        }
        .breadcrumb-separator::before {
            content: '/';
            margin: 0 8px;
            color: #9ca3af;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Header with Navigation -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Breadcrumb Navigation -->
                <nav class="flex items-center space-x-2 text-sm">
                    <a href="<?= site_url('schooladmin/dashboard') ?>" class="text-indigo-600 hover:text-indigo-800 font-medium">
                        <i class="fas fa-tachometer-alt mr-1"></i>Dashboard
                    </a>
                    <span class="breadcrumb-separator"></span>
                    <span class="text-gray-700 font-medium">Settings</span>
                </nav>

                <!-- Header Actions -->
                <div class="flex items-center space-x-4">
                    <button onclick="saveAllSettings()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                    <button onclick="resetSettings()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-undo mr-2"></i>Reset
                    </button>
                    
                    <!-- User Menu -->
                    <div class="relative">
                        <button class="flex items-center space-x-2 hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors">
                            <div class="w-8 h-8 bg-indigo-600 rounded-full flex items-center justify-center">
                                <span class="text-white text-sm font-medium">A</span>
                            </div>
                            <span class="text-gray-700 font-medium text-sm"><?= isset($school_name) ? esc($school_name) : 'School' ?> Admin</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header -->
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <div class="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center">
                    <i class="fas fa-cog text-white text-lg"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">School Settings</h1>
                    <p class="text-gray-600 mt-1"><?= isset($school_name) ? esc($school_name) : 'School' ?> - Configuration and preferences</p>
                </div>
            </div>
        </div>

        <!-- Settings Layout -->
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <!-- Settings Navigation Sidebar -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-24">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Settings Categories</h3>
                    <nav class="space-y-2">
                        <button onclick="showSettingsTab('school-profile')" class="settings-tab active w-full text-left px-4 py-3 rounded-lg font-medium text-sm text-indigo-600 border-l-4 border-indigo-500">
                            <i class="fas fa-school mr-3"></i>School Profile
                        </button>
                        <button onclick="showSettingsTab('question-bank')" class="settings-tab w-full text-left px-4 py-3 rounded-lg font-medium text-sm text-gray-600 border-l-4 border-transparent hover:border-gray-300">
                            <i class="fas fa-question-circle mr-3"></i>Question Bank
                        </button>
                        <button onclick="showSettingsTab('user-management')" class="settings-tab w-full text-left px-4 py-3 rounded-lg font-medium text-sm text-gray-600 border-l-4 border-transparent hover:border-gray-300">
                            <i class="fas fa-users mr-3"></i>User Management
                        </button>
                        <button onclick="showSettingsTab('notifications')" class="settings-tab w-full text-left px-4 py-3 rounded-lg font-medium text-sm text-gray-600 border-l-4 border-transparent hover:border-gray-300">
                            <i class="fas fa-bell mr-3"></i>Notifications
                        </button>
                        <button onclick="showSettingsTab('system')" class="settings-tab w-full text-left px-4 py-3 rounded-lg font-medium text-sm text-gray-600 border-l-4 border-transparent hover:border-gray-300">
                            <i class="fas fa-cogs mr-3"></i>System
                        </button>
                        <button onclick="showSettingsTab('security')" class="settings-tab w-full text-left px-4 py-3 rounded-lg font-medium text-sm text-gray-600 border-l-4 border-transparent hover:border-gray-300">
                            <i class="fas fa-shield-alt mr-3"></i>Security
                        </button>
                    </nav>
                </div>
            </div>

            <!-- Settings Content -->
            <div class="lg:col-span-3">
                <!-- School Profile Settings -->
                <div id="school-profile-tab" class="settings-tab-content active">
                    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                        <div class="px-6 py-4 border-b border-gray-200">
                            <h2 class="text-xl font-semibold text-gray-800">School Profile</h2>
                            <p class="text-sm text-gray-600 mt-1">Update your school's basic information and contact details</p>
                        </div>
                        <div class="p-6">
                            <form id="school-profile-form" class="space-y-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">School Name *</label>
                                        <input type="text" id="school-name" value="<?= isset($school_name) ? esc($school_name) : '' ?>" 
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            Email Address 
                                            <span class="text-xs text-gray-500 font-normal">(Read-only)</span>
                                        </label>
                                        <input type="email" id="school-email" value="<?= isset($school_email) ? esc($school_email) : '' ?>" 
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg readonly-field" readonly>
                                        <p class="text-xs text-gray-500 mt-1">
                                            <i class="fas fa-lock mr-1"></i>Email cannot be changed after registration
                                        </p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                        <input type="tel" id="school-phone" value="<?= isset($school_phone) ? esc($school_phone) : '' ?>" 
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                               placeholder="+91 XXXXX XXXXX">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">
                                            Status 
                                            <span class="text-xs text-gray-500 font-normal">(System Managed)</span>
                                        </label>
                                        <select id="school-status" class="w-full px-4 py-3 border border-gray-300 rounded-lg readonly-field" disabled>
                                            <option value="active" <?= (isset($school_status) && $school_status === 'active') ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= (isset($school_status) && $school_status === 'inactive') ? 'selected' : '' ?>>Inactive</option>
                                            <option value="suspended" <?= (isset($school_status) && $school_status === 'suspended') ? 'selected' : '' ?>>Suspended</option>
                                        </select>
                                        <p class="text-xs text-gray-500 mt-1">
                                            <i class="fas fa-shield-alt mr-1"></i>Status is managed by system administrator
                                        </p>
                                    </div>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">School Address</label>
                                    <textarea id="school-address" rows="3" 
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" 
                                              placeholder="Enter complete school address"><?= isset($school_address) ? esc($school_address) : '' ?></textarea>
                                </div>

                                <div class="flex justify-end pt-4 border-t border-gray-200">
                                    <button type="button" onclick="updateSchoolProfile()" 
                                            class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors font-medium">
                                        <i class="fas fa-save mr-2"></i>Update Profile
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Question Bank Settings -->
                <div id="question-bank-tab" class="settings-tab-content">
                    <div class="space-y-6">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-800">Question Bank Configuration</h2>
                                <p class="text-sm text-gray-600 mt-1">Configure subjects, standards, and question management settings</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Subject Management -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Default Subjects</h3>
                                        <div class="space-y-3">
                                            <label class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm font-medium">Mathematics</span>
                                            </label>
                                            <label class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm font-medium">Science</span>
                                            </label>
                                            <label class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm font-medium">English</span>
                                            </label>
                                            <label class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <input type="checkbox" class="mr-3 text-indigo-600">
                                                <span class="text-sm font-medium">Tamil</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Question Settings -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Question Management</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Auto-approval threshold (days)</label>
                                                <input type="number" value="7" min="1" max="30"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                                <p class="text-xs text-gray-500 mt-1">Questions pending review for this many days will be auto-approved</p>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Require admin approval for new questions</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Allow duplicate question detection</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Management Settings -->
                <div id="user-management-tab" class="settings-tab-content">
                    <div class="space-y-6">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-800">User Management</h2>
                                <p class="text-sm text-gray-600 mt-1">Configure user registration and access controls</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Registration Settings -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Registration Settings</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Registration Method</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                                    <option value="admin-only">Admin Only</option>
                                                    <option value="invite-only" selected>Invitation Only</option>
                                                    <option value="open">Open Registration</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Require email verification</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Auto-approve new staff members</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Security Settings -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Security Settings</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Password Minimum Length</label>
                                                <input type="number" value="8" min="6" max="20"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                                                <input type="number" value="60" min="15" max="480"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Enable two-factor authentication</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Settings -->
                <div id="notifications-tab" class="settings-tab-content">
                    <div class="space-y-6">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-800">Notification Preferences</h2>
                                <p class="text-sm text-gray-600 mt-1">Configure when and how to send notifications</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Email Notifications -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Email Notifications</h3>
                                        <div class="space-y-3">
                                            <label class="flex items-center">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm">New question submissions</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                <span class="text-sm">Question approvals/rejections</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="mr-3 text-indigo-600">
                                                <span class="text-sm">New staff registrations</span>
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="mr-3 text-indigo-600">
                                                <span class="text-sm">System maintenance alerts</span>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- System Notifications -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">System Notifications</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Notification Retention (days)</label>
                                                <input type="number" value="30" min="7" max="90"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                                <p class="text-xs text-gray-500 mt-1">How long to keep notifications in the system</p>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Show desktop notifications</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Send daily summary emails</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div id="system-tab" class="settings-tab-content">
                    <div class="space-y-6">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-800">System Configuration</h2>
                                <p class="text-sm text-gray-600 mt-1">Configure general system preferences and maintenance settings</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- General Settings -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">General Settings</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Time Zone</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                                    <option value="Asia/Kolkata" selected>Asia/Kolkata (IST)</option>
                                                    <option value="UTC">UTC</option>
                                                    <option value="America/New_York">America/New_York (EST)</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                                    <option value="DD/MM/YYYY" selected>DD/MM/YYYY</option>
                                                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Enable system maintenance mode</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Backup Settings -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Backup & Maintenance</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Auto Backup Frequency</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                                    <option value="daily" selected>Daily</option>
                                                    <option value="weekly">Weekly</option>
                                                    <option value="monthly">Monthly</option>
                                                    <option value="disabled">Disabled</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Data Retention (months)</label>
                                                <input type="number" value="12" min="1" max="60"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                            </div>
                                            <div>
                                                <button type="button" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                                    <i class="fas fa-download mr-2"></i>Create Manual Backup
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Settings -->
                <div id="security-tab" class="settings-tab-content">
                    <div class="space-y-6">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                            <div class="px-6 py-4 border-b border-gray-200">
                                <h2 class="text-xl font-semibold text-gray-800">Security Settings</h2>
                                <p class="text-sm text-gray-600 mt-1">Configure security policies and access controls</p>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Access Control -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Access Control</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
                                                <input type="number" value="5" min="3" max="10"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Account Lockout Duration (minutes)</label>
                                                <input type="number" value="30" min="5" max="1440"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Enable IP-based access restrictions</span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Audit & Logging -->
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-800 mb-4">Audit & Logging</h3>
                                        <div class="space-y-4">
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Log user activities</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="flex items-center">
                                                    <input type="checkbox" checked class="mr-3 text-indigo-600">
                                                    <span class="text-sm">Log system changes</span>
                                                </label>
                                            </div>
                                            <div>
                                                <label class="block text-sm font-medium text-gray-700 mb-2">Log Retention (days)</label>
                                                <input type="number" value="90" min="30" max="365"
                                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // Settings tab functionality
        function showSettingsTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.settings-tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Show selected tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // Update tab navigation
            const tabs = document.querySelectorAll('.settings-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active', 'border-indigo-500', 'text-indigo-600');
                tab.classList.add('border-transparent', 'text-gray-600');
            });

            // Activate selected tab
            const activeTab = document.querySelector(`[onclick="showSettingsTab('${tabName}')"]`);
            if (activeTab) {
                activeTab.classList.remove('border-transparent', 'text-gray-600');
                activeTab.classList.add('active', 'border-indigo-500', 'text-indigo-600');
            }

            // Update page title
            const pageTitle = document.querySelector('h1');
            const tabTitles = {
                'school-profile': 'School Profile Settings',
                'question-bank': 'Question Bank Settings',
                'user-management': 'User Management Settings',
                'notifications': 'Notification Settings',
                'system': 'System Settings',
                'security': 'Security Settings'
            };
            if (pageTitle && tabTitles[tabName]) {
                pageTitle.textContent = tabTitles[tabName];
            }
        }

        // Update school profile
        function updateSchoolProfile() {
            const formData = {
                name: document.getElementById('school-name').value,
                email: document.getElementById('school-email').value,
                phone: document.getElementById('school-phone').value,
                address: document.getElementById('school-address').value
            };

            // Validate required fields
            if (!formData.name) {
                showNotification('Please fill in the school name', 'error');
                return;
            }

            // Show loading state
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
            button.disabled = true;

            // Make API call
            fetch('<?= site_url('schooladmin/updateSchoolProfile') ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(formData)
            })
            .then(response => response.json())
            .then(data => {
                // Reset button state
                button.innerHTML = originalText;
                button.disabled = false;

                if (data.success) {
                    showNotification('Profile updated successfully!', 'success');
                    // Update breadcrumb if school name changed
                    if (formData.name) {
                        const userMenuText = document.querySelector('header span');
                        if (userMenuText) {
                            userMenuText.textContent = formData.name + ' Admin';
                        }
                    }
                } else {
                    showNotification(data.message || 'Failed to update profile', 'error');
                }
            })
            .catch(error => {
                // Reset button state
                button.innerHTML = originalText;
                button.disabled = false;

                console.error('Error:', error);
                showNotification('An error occurred while updating the profile', 'error');
            });
        }

        // Save all settings
        function saveAllSettings() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
            button.disabled = true;

            // Collect all settings data (you can expand this based on your needs)
            const settingsData = {
                school_profile: {
                    name: document.getElementById('school-name').value,
                    phone: document.getElementById('school-phone').value,
                    address: document.getElementById('school-address').value
                }
                // Add other settings as needed
            };

            // Simulate API call
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
                showNotification('All settings saved successfully!', 'success');
            }, 1500);
        }

        // Reset settings
        function resetSettings() {
            if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
                const button = event.target;
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Resetting...';
                button.disabled = true;

                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.disabled = false;
                    showNotification('Settings reset to default values', 'success');
                    location.reload();
                }, 1500);
            }
        }

        // Show notification
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-blue-500 text-white'
            }`;

            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${
                        type === 'success' ? 'fa-check-circle' :
                        type === 'error' ? 'fa-exclamation-circle' :
                        type === 'warning' ? 'fa-exclamation-triangle' :
                        'fa-info-circle'
                    } mr-3"></i>
                    <span class="font-medium">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Show default tab
            showSettingsTab('school-profile');

            // Load settings data
            loadSettings();
        });

        // Load settings from server
        function loadSettings() {
            // This would typically load from your API
            console.log('Loading settings...');
        }
    </script>
</body>
</html>
