<?php
// Start session
session_start();

// Check if we're using CodeIgniter session
if (function_exists('session')) {
    $ci_session = session();
    echo "<h2>CodeIgniter Session Data:</h2>";
    echo "<pre>";
    print_r($ci_session->get());
    echo "</pre>";
} else {
    echo "<h2>PHP Session Data:</h2>";
    echo "<pre>";
    print_r($_SESSION);
    echo "</pre>";
}

// Check database connection
echo "<h2>Database Connection Test:</h2>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=school", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test users query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users WHERE is_deleted = 0");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✅ Database connected successfully<br>";
    echo "📊 Total active users: " . $result['count'] . "<br>";
    
    // Test schools query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM schools");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "🏫 Total schools: " . $result['count'] . "<br>";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Check if superadmin is logged in
echo "<h2>Authentication Status:</h2>";
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] && isset($_SESSION['role']) && $_SESSION['role'] === 'superadmin') {
    echo "✅ SuperAdmin is logged in<br>";
    echo "👤 User ID: " . ($_SESSION['user_id'] ?? 'Not set') . "<br>";
    echo "📧 Email: " . ($_SESSION['email'] ?? 'Not set') . "<br>";
} else {
    echo "❌ SuperAdmin is not logged in<br>";
    echo "🔗 <a href='http://localhost:3000/m-dev.php/login/superadmin'>Login as SuperAdmin</a><br>";
}

echo "<h2>Test Links:</h2>";
echo "🏠 <a href='http://localhost:3000/m-dev.php/superadmin/dashboard'>SuperAdmin Dashboard</a><br>";
echo "👥 <a href='http://localhost:3000/m-dev.php/superadmin/getUsers'>Get Users API</a><br>";
echo "🧪 <a href='http://localhost:3000/m-dev.php/test_api.html'>API Test Page</a><br>";
?>
