<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class TestSchoolSeeder extends Seeder
{
    public function run()
    {
        // Check if test school already exists
        $existingSchool = $this->db->table('schools')
            ->where('email', '<EMAIL>')
            ->get()
            ->getRowArray();

        if ($existingSchool) {
            echo "Test school already exists!\n";
            echo "Email: <EMAIL>\n";
            echo "Password: password123\n";
            echo "School ID: " . $existingSchool['id'] . "\n";
            echo "Status: " . $existingSchool['status'] . "\n";
            return;
        }

        // Create test school
        $schoolData = [
            'name' => 'Test School',
            'email' => '<EMAIL>',
            'password' => password_hash('password123', PASSWORD_DEFAULT),
            'phone' => '+1234567890',
            'address' => '123 Test Street, Test City',
            'status' => 'active',
            'plan_id' => 1, // Free plan
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        $result = $this->db->table('schools')->insert($schoolData);

        if ($result) {
            $schoolId = $this->db->insertID();
            echo "Test school created successfully!\n";
            echo "School ID: $schoolId\n";
            echo "Email: <EMAIL>\n";
            echo "Password: password123\n";
            echo "Status: active\n";
            echo "\nYou can now log in at: http://localhost:8080/login/schooladmin\n";
        } else {
            echo "Failed to create test school.\n";
            print_r($this->db->error());
        }
    }
}
