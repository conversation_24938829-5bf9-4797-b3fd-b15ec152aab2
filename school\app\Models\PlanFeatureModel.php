<?php

namespace App\Models;

use CodeIgniter\Model;

class PlanFeatureModel extends Model
{
    protected $table = 'plan_features';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'plan_id', 'feature_key', 'feature_name', 'feature_value', 
        'value_type', 'is_unlimited'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'plan_id' => 'required|integer',
        'feature_key' => 'required|max_length[100]',
        'feature_name' => 'required|max_length[255]',
        'feature_value' => 'required',
        'value_type' => 'required|in_list[boolean,integer,string,json]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get features for a plan as key-value array
     */
    public function getPlanFeaturesArray($planId)
    {
        $features = $this->where('plan_id', $planId)->findAll();
        $result = [];

        foreach ($features as $feature) {
            $value = $this->parseFeatureValue($feature);
            $result[$feature['feature_key']] = [
                'value' => $value,
                'name' => $feature['feature_name'],
                'type' => $feature['value_type'],
                'unlimited' => (bool)$feature['is_unlimited']
            ];
        }

        return $result;
    }

    /**
     * Parse feature value based on type
     */
    public function parseFeatureValue($feature)
    {
        if ($feature['is_unlimited']) {
            return 'unlimited';
        }

        switch ($feature['value_type']) {
            case 'boolean':
                return filter_var($feature['feature_value'], FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int)$feature['feature_value'];
            case 'json':
                return json_decode($feature['feature_value'], true);
            case 'string':
            default:
                return $feature['feature_value'];
        }
    }

    /**
     * Get specific feature for a plan
     */
    public function getPlanFeature($planId, $featureKey)
    {
        $feature = $this->where('plan_id', $planId)
                        ->where('feature_key', $featureKey)
                        ->first();

        if (!$feature) {
            return null;
        }

        return [
            'value' => $this->parseFeatureValue($feature),
            'name' => $feature['feature_name'],
            'type' => $feature['value_type'],
            'unlimited' => (bool)$feature['is_unlimited']
        ];
    }

    /**
     * Update or create plan feature
     */
    public function updatePlanFeature($planId, $featureKey, $featureName, $value, $valueType = 'string', $isUnlimited = false)
    {
        $existing = $this->where('plan_id', $planId)
                        ->where('feature_key', $featureKey)
                        ->first();

        $data = [
            'plan_id' => $planId,
            'feature_key' => $featureKey,
            'feature_name' => $featureName,
            'feature_value' => is_array($value) ? json_encode($value) : (string)$value,
            'value_type' => $valueType,
            'is_unlimited' => $isUnlimited ? 1 : 0
        ];

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Delete plan feature
     */
    public function deletePlanFeature($planId, $featureKey)
    {
        return $this->where('plan_id', $planId)
                   ->where('feature_key', $featureKey)
                   ->delete();
    }

    /**
     * Get all features grouped by plan
     */
    public function getAllFeaturesGroupedByPlan()
    {
        $features = $this->select('plan_features.*, plans.name as plan_name, plans.display_name')
                        ->join('plans', 'plans.id = plan_features.plan_id')
                        ->orderBy('plans.sort_order', 'ASC')
                        ->orderBy('plan_features.feature_key', 'ASC')
                        ->findAll();

        $grouped = [];
        foreach ($features as $feature) {
            $planId = $feature['plan_id'];
            if (!isset($grouped[$planId])) {
                $grouped[$planId] = [
                    'plan_name' => $feature['plan_name'],
                    'plan_display_name' => $feature['display_name'],
                    'features' => []
                ];
            }

            $grouped[$planId]['features'][$feature['feature_key']] = [
                'name' => $feature['feature_name'],
                'value' => $this->parseFeatureValue($feature),
                'type' => $feature['value_type'],
                'unlimited' => (bool)$feature['is_unlimited']
            ];
        }

        return $grouped;
    }
}
