<?php

namespace App\Models;

use CodeIgniter\Model;

class SubjectModel extends Model
{
    protected $table = 'subjects';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id',
        'name',
        'standards',
        'is_active'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [
        'standards' => 'json',
        'is_active' => 'boolean'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'name' => 'required|string|max_length[100]',
        'standards' => 'required'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get subjects by standard for a school
     */
    public function getSubjectsByStandard($schoolId, $standard)
    {
        $subjects = $this->where('school_id', $schoolId)
                        ->where('is_active', true)
                        ->findAll();

        $filteredSubjects = [];
        foreach ($subjects as $subject) {
            $standards = is_string($subject['standards']) ? 
                        json_decode($subject['standards'], true) : 
                        $subject['standards'];
            
            if (in_array((int)$standard, $standards)) {
                $filteredSubjects[] = $subject;
            }
        }

        return $filteredSubjects;
    }

    /**
     * Get subjects assigned to a staff member
     */
    public function getStaffSubjects($staffId, $schoolId)
    {
        return $this->select('subjects.*, staff_subject_assignments.standards as assigned_standards')
                    ->join('staff_subject_assignments', 'staff_subject_assignments.subject_id = subjects.id')
                    ->where('subjects.school_id', $schoolId)
                    ->where('staff_subject_assignments.staff_id', $staffId)
                    ->where('subjects.is_active', true)
                    ->findAll();
    }

    /**
     * Get all active subjects for a school
     */
    public function getSchoolSubjects($schoolId)
    {
        return $this->where('school_id', $schoolId)
                    ->where('is_active', true)
                    ->orderBy('name', 'ASC')
                    ->findAll();
    }

    /**
     * Check if staff can create questions for a subject and standard
     */
    public function canStaffCreateQuestion($staffId, $subjectName, $standard, $schoolId)
    {
        $subject = $this->where('school_id', $schoolId)
                       ->where('name', $subjectName)
                       ->where('is_active', true)
                       ->first();

        if (!$subject) {
            return false;
        }

        $assignment = $this->db->table('staff_subject_assignments')
                              ->where('staff_id', $staffId)
                              ->where('subject_id', $subject['id'])
                              ->get()
                              ->getRowArray();

        if (!$assignment) {
            return false;
        }

        $assignedStandards = json_decode($assignment['standards'], true);
        return in_array((int)$standard, $assignedStandards);
    }
}
