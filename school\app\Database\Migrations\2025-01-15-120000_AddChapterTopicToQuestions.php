<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddChapterTopicToQuestions extends Migration
{
    public function up()
    {
        // Add new fields to questions table
        $fields = [
            'chapter' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => true,
                'after' => 'subject'
            ],
            'chapter_name' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
                'after' => 'chapter'
            ],
            'topic_name' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
                'after' => 'chapter_name'
            ]
        ];

        $this->forge->addColumn('questions', $fields);

        // Add indexes for better performance
        $this->forge->addKey('chapter');
        $this->forge->addKey('topic_name');
    }

    public function down()
    {
        // Remove the added fields
        $this->forge->dropColumn('questions', ['chapter', 'chapter_name', 'topic_name']);
    }
}
