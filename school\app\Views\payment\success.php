<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }
        @keyframes successPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #f39c12;
            animation: confetti-fall 3s linear infinite;
        }
        @keyframes confetti-fall {
            0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
            100% { transform: translateY(100vh) rotate(720deg); opacity: 0; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-green-50 to-blue-50 min-h-screen">
    
    <!-- Confetti Animation -->
    <div class="fixed inset-0 pointer-events-none overflow-hidden">
        <div class="confetti" style="left: 10%; animation-delay: 0s; background: #e74c3c;"></div>
        <div class="confetti" style="left: 20%; animation-delay: 0.5s; background: #f39c12;"></div>
        <div class="confetti" style="left: 30%; animation-delay: 1s; background: #2ecc71;"></div>
        <div class="confetti" style="left: 40%; animation-delay: 1.5s; background: #3498db;"></div>
        <div class="confetti" style="left: 50%; animation-delay: 2s; background: #9b59b6;"></div>
        <div class="confetti" style="left: 60%; animation-delay: 2.5s; background: #e67e22;"></div>
        <div class="confetti" style="left: 70%; animation-delay: 3s; background: #1abc9c;"></div>
        <div class="confetti" style="left: 80%; animation-delay: 3.5s; background: #34495e;"></div>
        <div class="confetti" style="left: 90%; animation-delay: 4s; background: #e91e63;"></div>
    </div>

    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            
            <!-- Success Card -->
            <div class="bg-white rounded-2xl shadow-2xl p-8 text-center relative overflow-hidden">
                
                <!-- Background Pattern -->
                <div class="absolute inset-0 bg-gradient-to-br from-green-400/10 to-blue-400/10"></div>
                
                <!-- Content -->
                <div class="relative z-10">
                    
                    <!-- Success Icon -->
                    <div class="success-animation mx-auto w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mb-6">
                        <i class="fas fa-check text-3xl text-green-600"></i>
                    </div>

                    <!-- Success Message -->
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Payment Successful!</h1>
                    <p class="text-gray-600 mb-6">Your subscription has been activated successfully.</p>

                    <!-- Payment Details -->
                    <?php if (isset($payment_details)): ?>
                    <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
                        <h3 class="font-semibold text-gray-800 mb-3">Payment Details</h3>
                        
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Transaction ID:</span>
                                <span class="font-mono text-gray-800"><?= $payment_details['transaction_id'] ?? 'N/A' ?></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Plan:</span>
                                <span class="font-medium text-gray-800"><?= $payment_details['plan_name'] ?? 'N/A' ?></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Amount:</span>
                                <span class="font-medium text-green-600">₹<?= number_format($payment_details['amount'] ?? 0) ?></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Payment Method:</span>
                                <span class="font-medium text-gray-800 capitalize"><?= $payment_details['payment_method'] ?? 'N/A' ?></span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-gray-600">Date:</span>
                                <span class="font-medium text-gray-800"><?= date('M d, Y H:i', strtotime($payment_details['created_at'] ?? 'now')) ?></span>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Subscription Info -->
                    <?php if (isset($subscription)): ?>
                    <div class="bg-blue-50 rounded-lg p-4 mb-6 text-left">
                        <h3 class="font-semibold text-blue-800 mb-3">Subscription Details</h3>
                        
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-blue-600">Status:</span>
                                <span class="font-medium text-green-600 capitalize">
                                    <i class="fas fa-circle text-xs mr-1"></i><?= $subscription['status'] ?? 'Active' ?>
                                </span>
                            </div>
                            
                            <div class="flex justify-between">
                                <span class="text-blue-600">Billing Cycle:</span>
                                <span class="font-medium text-blue-800 capitalize"><?= $subscription['billing_cycle'] ?? 'Monthly' ?></span>
                            </div>
                            
                            <?php if (isset($subscription['expires_at']) && $subscription['expires_at']): ?>
                            <div class="flex justify-between">
                                <span class="text-blue-600">Next Billing:</span>
                                <span class="font-medium text-blue-800"><?= date('M d, Y', strtotime($subscription['expires_at'])) ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <a href="<?= base_url('schooladmin/dashboard') ?>" 
                           class="w-full bg-green-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-green-700 transition duration-300 inline-block">
                            <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
                        </a>
                        
                        <a href="<?= base_url('subscription/current') ?>" 
                           class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 transition duration-300 inline-block">
                            <i class="fas fa-credit-card mr-2"></i>View Subscription
                        </a>
                        
                        <a href="<?= base_url('payment/history') ?>" 
                           class="w-full border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-50 transition duration-300 inline-block">
                            <i class="fas fa-receipt mr-2"></i>Payment History
                        </a>
                    </div>

                    <!-- Support Info -->
                    <div class="mt-8 pt-6 border-t border-gray-200">
                        <p class="text-sm text-gray-500 mb-2">Need help? Contact our support team</p>
                        <div class="flex justify-center space-x-4 text-sm">
                            <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-envelope mr-1"></i>Email
                            </a>
                            <a href="tel:+91-XXXXXXXXXX" class="text-blue-600 hover:text-blue-800">
                                <i class="fas fa-phone mr-1"></i>Call
                            </a>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Additional Info -->
            <div class="text-center text-sm text-gray-500">
                <p>A confirmation email has been sent to your registered email address.</p>
            </div>

        </div>
    </div>

    <script>
        // Auto-redirect after 30 seconds
        setTimeout(function() {
            if (confirm('Would you like to go to your dashboard now?')) {
                window.location.href = '<?= base_url('schooladmin/dashboard') ?>';
            }
        }, 30000);

        // Prevent back button
        history.pushState(null, null, location.href);
        window.onpopstate = function () {
            history.go(1);
        };
    </script>
</body>
</html>
