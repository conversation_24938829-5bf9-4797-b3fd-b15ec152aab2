<?php

namespace App\Models;

use CodeIgniter\Model;

class SuperAdminLogModel extends Model
{
    protected $table = 'superadmin_logs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'admin_user_id', 'action', 'target_school_id', 'target_user_id',
        'description', 'data_before', 'data_after', 'ip_address',
        'user_agent', 'severity'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = null; // No updated_at for super admin logs

    // Validation
    protected $validationRules = [
        'action' => 'required|max_length[100]',
        'severity' => 'in_list[low,medium,high,critical]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Log super admin action
     */
    public function logSuperAdminAction($data)
    {
        // Ensure required fields
        if (!isset($data['action'])) {
            return false;
        }

        // Set defaults
        $logData = array_merge([
            'admin_user_id' => null,
            'target_school_id' => null,
            'target_user_id' => null,
            'description' => null,
            'data_before' => null,
            'data_after' => null,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $this->getUserAgent(),
            'severity' => 'medium'
        ], $data);

        // Convert arrays to JSON for data fields
        if (is_array($logData['data_before'])) {
            $logData['data_before'] = json_encode($logData['data_before']);
        }
        if (is_array($logData['data_after'])) {
            $logData['data_after'] = json_encode($logData['data_after']);
        }

        return $this->insert($logData);
    }

    /**
     * Get super admin logs with filters
     */
    public function getSuperAdminLogs($filters = [], $page = 1, $perPage = 50)
    {
        $builder = $this->select('
            superadmin_logs.*,
            admin_users.name as admin_name,
            admin_users.email as admin_email,
            target_schools.name as target_school_name,
            target_users.name as target_user_name,
            target_users.email as target_user_email
        ')
        ->join('users as admin_users', 'admin_users.id = superadmin_logs.admin_user_id', 'left')
        ->join('schools as target_schools', 'target_schools.id = superadmin_logs.target_school_id', 'left')
        ->join('users as target_users', 'target_users.id = superadmin_logs.target_user_id', 'left');

        // Apply filters
        if (!empty($filters['admin_user_id'])) {
            $builder->where('superadmin_logs.admin_user_id', $filters['admin_user_id']);
        }

        if (!empty($filters['action'])) {
            $builder->where('superadmin_logs.action', $filters['action']);
        }

        if (!empty($filters['target_school_id'])) {
            $builder->where('superadmin_logs.target_school_id', $filters['target_school_id']);
        }

        if (!empty($filters['severity'])) {
            $builder->where('superadmin_logs.severity', $filters['severity']);
        }

        if (!empty($filters['date_from'])) {
            $builder->where('superadmin_logs.created_at >=', $filters['date_from'] . ' 00:00:00');
        }

        if (!empty($filters['date_to'])) {
            $builder->where('superadmin_logs.created_at <=', $filters['date_to'] . ' 23:59:59');
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('superadmin_logs.description', $filters['search'])
                   ->orLike('superadmin_logs.action', $filters['search'])
                   ->orLike('admin_users.name', $filters['search'])
                   ->orLike('target_schools.name', $filters['search'])
                   ->orLike('target_users.name', $filters['search'])
                   ->groupEnd();
        }

        // Get total count
        $total = $builder->countAllResults(false);

        // Get paginated results
        $offset = ($page - 1) * $perPage;
        $logs = $builder->orderBy('superadmin_logs.created_at', 'DESC')
                       ->limit($perPage, $offset)
                       ->findAll();

        return [
            'logs' => $logs,
            'total' => $total,
            'page' => $page,
            'perPage' => $perPage,
            'totalPages' => ceil($total / $perPage)
        ];
    }

    /**
     * Get recent super admin logs
     */
    public function getRecentSuperAdminLogs($limit = 20)
    {
        return $this->select('
            superadmin_logs.*,
            admin_users.name as admin_name,
            target_schools.name as target_school_name,
            target_users.name as target_user_name
        ')
        ->join('users as admin_users', 'admin_users.id = superadmin_logs.admin_user_id', 'left')
        ->join('schools as target_schools', 'target_schools.id = superadmin_logs.target_school_id', 'left')
        ->join('users as target_users', 'target_users.id = superadmin_logs.target_user_id', 'left')
        ->orderBy('superadmin_logs.created_at', 'DESC')
        ->limit($limit)
        ->findAll();
    }

    /**
     * Get super admin statistics
     */
    public function getSuperAdminStats($dateFrom = null, $dateTo = null)
    {
        $builder = $this->db->table($this->table);

        if ($dateFrom) {
            $builder->where('created_at >=', $dateFrom . ' 00:00:00');
        }

        if ($dateTo) {
            $builder->where('created_at <=', $dateTo . ' 23:59:59');
        }

        $stats = [
            'total_actions' => $builder->countAllResults(false),
            'actions_by_type' => $builder->select('action, COUNT(*) as count')
                                       ->groupBy('action')
                                       ->orderBy('count', 'DESC')
                                       ->get()->getResultArray(),
            'actions_by_severity' => $builder->select('severity, COUNT(*) as count')
                                           ->groupBy('severity')
                                           ->get()->getResultArray(),
            'most_active_admins' => $builder->select('users.name, COUNT(*) as count')
                                          ->join('users', 'users.id = superadmin_logs.admin_user_id', 'left')
                                          ->where('superadmin_logs.admin_user_id IS NOT NULL')
                                          ->groupBy('superadmin_logs.admin_user_id')
                                          ->orderBy('count', 'DESC')
                                          ->limit(10)
                                          ->get()->getResultArray(),
            'affected_schools' => $builder->select('schools.name, COUNT(*) as count')
                                        ->join('schools', 'schools.id = superadmin_logs.target_school_id', 'left')
                                        ->where('superadmin_logs.target_school_id IS NOT NULL')
                                        ->groupBy('superadmin_logs.target_school_id')
                                        ->orderBy('count', 'DESC')
                                        ->limit(10)
                                        ->get()->getResultArray()
        ];

        return $stats;
    }

    /**
     * Clean old super admin logs
     */
    public function cleanOldLogs($daysToKeep = 365)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Get client IP address
     */
    private function getClientIP()
    {
        $request = \Config\Services::request();
        return $request->getIPAddress();
    }

    /**
     * Get user agent
     */
    private function getUserAgent()
    {
        $request = \Config\Services::request();
        return $request->getUserAgent()->getAgentString();
    }

    /**
     * Get distinct actions for filters
     */
    public function getDistinctActions()
    {
        return $this->select('action')
                   ->distinct()
                   ->orderBy('action', 'ASC')
                   ->findColumn('action');
    }
}
