<?php
/**
 * Test Reset Password API Endpoint
 * Run this from the school directory: php test_reset_password_api.php
 */

echo "=== Testing Reset Password API Endpoint ===\n\n";

// Use the actual token from the database
$actualToken = 'd5a270314427822b63f9357bf80cc4a7a6139e99b3e109732199fcb9a8fc83cd'; // From the check_reset_tokens.php output

echo "✅ Using actual token from database: " . substr($actualToken, 0, 20) . "...\n";
echo "   This is a valid token that should work.\n\n";

// Test the reset password API endpoint
$baseUrl = 'http://localhost/schoolquestionbank/school/public';
$endpoint = $baseUrl . '/forgot-password/reset';

// Test data - Now test with strong password
$testData = [
    'token' => $actualToken,
    'password' => 'MyNewPass123!', // Strong password - should succeed
    'confirm_password' => 'MyNewPass123!'
];

echo "1. Testing API endpoint: $endpoint\n";
echo "2. Test data: " . json_encode([
    'token' => substr($actualToken, 0, 10) . '...',
    'password' => '[HIDDEN]',
    'confirm_password' => '[HIDDEN]'
]) . "\n\n";

// Initialize cURL
$ch = curl_init();

// Set cURL options
curl_setopt_array($ch, [
    CURLOPT_URL => $endpoint,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => http_build_query($testData),
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTPHEADER => [
        'X-Requested-With: XMLHttpRequest',
        'Content-Type: application/x-www-form-urlencoded'
    ],
    CURLOPT_COOKIEJAR => 'cookies.txt',
    CURLOPT_COOKIEFILE => 'cookies.txt'
]);

// Execute request
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);

if (curl_error($ch)) {
    echo "❌ cURL Error: " . curl_error($ch) . "\n";
    curl_close($ch);
    exit(1);
}

curl_close($ch);

echo "3. Response Details:\n";
echo "   - HTTP Code: $httpCode\n";
echo "   - Content Type: $contentType\n";
echo "   - Response Length: " . strlen($response) . " bytes\n\n";

echo "4. Raw Response:\n";
echo "---START RESPONSE---\n";
echo $response;
echo "\n---END RESPONSE---\n\n";

// Try to parse as JSON
$jsonData = json_decode($response, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "✅ Valid JSON Response:\n";
    echo "   - Success: " . ($jsonData['success'] ? 'true' : 'false') . "\n";
    echo "   - Message: " . ($jsonData['message'] ?? 'N/A') . "\n";
    
    if (isset($jsonData['redirect'])) {
        echo "   - Redirect: " . $jsonData['redirect'] . "\n";
    }
    
    if ($jsonData['success']) {
        echo "✅ Password reset successful!\n";
    } else {
        echo "⚠️ Password reset failed: " . $jsonData['message'] . "\n";
    }
} else {
    echo "❌ Invalid JSON Response\n";
    echo "   - JSON Error: " . json_last_error_msg() . "\n";
    
    // Check if it's HTML (redirect or error page)
    if (strpos($response, '<html') !== false || strpos($response, '<!DOCTYPE') !== false) {
        echo "   - Response appears to be HTML (possible redirect or error page)\n";
        
        // Extract title if present
        if (preg_match('/<title>(.*?)<\/title>/i', $response, $matches)) {
            echo "   - Page Title: " . $matches[1] . "\n";
        }
        
        // Check for error messages
        if (preg_match('/error|exception|fatal/i', $response)) {
            echo "   - Response contains error indicators\n";
        }
    }
}

echo "\n=== Test Complete ===\n";

// Clean up
if (file_exists('cookies.txt')) {
    unlink('cookies.txt');
}

echo "\n📋 Troubleshooting Tips:\n";
echo "1. Check the latest log entries for specific errors\n";
echo "2. Verify the password_resets table has valid tokens\n";
echo "3. Check if the user exists in the schools or users table\n";
echo "4. Ensure foreign key constraints are properly set up\n";
