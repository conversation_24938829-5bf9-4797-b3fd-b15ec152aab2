<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddPendingStatusToSubscriptions extends Migration
{
    public function up()
    {
        // Add 'pending' to the subscription status enum
        $this->db->query("ALTER TABLE subscriptions MODIFY COLUMN status ENUM('active', 'expired', 'cancelled', 'suspended', 'trial', 'pending') DEFAULT 'trial'");
    }

    public function down()
    {
        // Remove 'pending' from the subscription status enum
        $this->db->query("ALTER TABLE subscriptions MODIFY COLUMN status ENUM('active', 'expired', 'cancelled', 'suspended', 'trial') DEFAULT 'trial'");
    }
}
