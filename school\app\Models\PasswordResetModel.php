<?php

namespace App\Models;

use CodeIgniter\Model;

class PasswordResetModel extends Model
{
    protected $table = 'password_resets';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'email', 'token', 'user_type', 'expires_at', 'used'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'email' => 'required|valid_email',
        'token' => 'required|max_length[255]',
        'user_type' => 'required|in_list[user,school]',
        'expires_at' => 'required'
    ];

    protected $validationMessages = [
        'email' => [
            'required' => 'Email is required',
            'valid_email' => 'Please provide a valid email address'
        ],
        'token' => [
            'required' => 'Token is required'
        ]
    ];

    /**
     * Create a password reset token
     */
    public function createResetToken($email, $userType = 'user')
    {
        // Delete any existing tokens for this email
        $this->where('email', $email)
             ->where('user_type', $userType)
             ->delete();

        // Generate a secure token
        $token = bin2hex(random_bytes(32));
        
        // Set expiration time (1 hour from now) - use database time to avoid timezone issues
        $expiresAt = $this->db->query("SELECT DATE_ADD(NOW(), INTERVAL 1 HOUR) as expires")->getRow()->expires;

        $data = [
            'email' => $email,
            'token' => $token,
            'user_type' => $userType,
            'expires_at' => $expiresAt,
            'used' => false
        ];

        if ($this->insert($data)) {
            return $token;
        }

        return false;
    }

    /**
     * Verify a reset token
     */
    public function verifyResetToken($token, $email = null)
    {
        $builder = $this->where('token', $token)
                       ->where('used', false)
                       ->where('expires_at >', date('Y-m-d H:i:s'));

        if ($email) {
            $builder->where('email', $email);
        }

        return $builder->first();
    }

    /**
     * Mark token as used
     */
    public function markTokenAsUsed($token)
    {
        return $this->where('token', $token)
                   ->set(['used' => true])
                   ->update();
    }

    /**
     * Clean up expired tokens (for cron job)
     */
    public function cleanupExpiredTokens()
    {
        return $this->where('expires_at <', date('Y-m-d H:i:s'))
                   ->orWhere('used', true)
                   ->delete();
    }

    /**
     * Get reset token info
     */
    public function getTokenInfo($token)
    {
        return $this->where('token', $token)->first();
    }

    /**
     * Check if email has recent reset request (to prevent spam)
     */
    public function hasRecentResetRequest($email, $userType = 'user', $minutes = 5)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$minutes} minutes"));
        
        return $this->where('email', $email)
                   ->where('user_type', $userType)
                   ->where('created_at >', $timeLimit)
                   ->countAllResults() > 0;
    }

    /**
     * Get reset attempts count for email in last hour
     */
    public function getResetAttemptsCount($email, $userType = 'user', $hours = 1)
    {
        $timeLimit = date('Y-m-d H:i:s', strtotime("-{$hours} hours"));
        
        return $this->where('email', $email)
                   ->where('user_type', $userType)
                   ->where('created_at >', $timeLimit)
                   ->countAllResults();
    }

    /**
     * Check if token is expired
     */
    public function isTokenExpired($token)
    {
        $resetData = $this->where('token', $token)->first();
        
        if (!$resetData) {
            return true;
        }

        return strtotime($resetData['expires_at']) < time();
    }

    /**
     * Get all active tokens for an email
     */
    public function getActiveTokensForEmail($email, $userType = 'user')
    {
        return $this->where('email', $email)
                   ->where('user_type', $userType)
                   ->where('used', false)
                   ->where('expires_at >', date('Y-m-d H:i:s'))
                   ->findAll();
    }

    /**
     * Invalidate all tokens for an email
     */
    public function invalidateAllTokensForEmail($email, $userType = 'user')
    {
        return $this->where('email', $email)
                   ->where('user_type', $userType)
                   ->set(['used' => true])
                   ->update();
    }
}
