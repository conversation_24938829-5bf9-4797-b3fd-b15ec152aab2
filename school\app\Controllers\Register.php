<?php

namespace App\Controllers;

use CodeIgniter\Controller;
use App\Services\AuditLogger;

class Register extends BaseController
{
    public function form()
    {
        return view('auth/register'); // Load your register.php form view
    }

    public function sendOtp()
{
    $request = $this->request->getJSON();
    $email = $request->email ?? '';

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return $this->response->setJSON(['status' => 'error', 'message' => 'Invalid email address.']);
    }

    // Check if email already exists
    $schoolModel = new \App\Models\SchoolModel();
    if ($schoolModel->where('email', $email)->first()) {
        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'This email is already registered.'
        ]);
    }

    $otp = rand(100000, 999999);

    session()->set('register_email', $email);
    session()->set('register_otp', $otp);

    // Send email using CI4 Email library
    $emailService = \Config\Services::email();

    $emailService->setTo($email);
    $emailService->setFrom('<EMAIL>', 'QuestionBank Pro');
    $emailService->setSubject('Your OTP Code');
    $emailService->setMessage("Your OTP is: $otp\n\nThis OTP is valid for 10 minutes.");

    if ($emailService->send()) {
        return $this->response->setJSON(['status' => 'success', 'message' => 'OTP sent successfully.']);
    } else {
        return $this->response->setJSON([
            'status' => 'error',
            'message' => 'Failed to send OTP.',
            'debug' => $emailService->printDebugger(['headers'])
        ]);
    }
}

public function verifyOtp()
{
    $request = $this->request->getJSON();
    $userOtp = $request->otp ?? '';
    $email   = $request->email ?? '';

    $sessionOtp   = session()->get('register_otp');
    $sessionEmail = session()->get('register_email');

    log_message('debug', "User OTP: $userOtp, Session OTP: $sessionOtp");
    log_message('debug', "User Email: $email, Session Email: $sessionEmail");

    if ((string) $userOtp === (string) $sessionOtp && strtolower($email) === strtolower($sessionEmail)) {
        session()->remove('register_otp');
        return $this->response->setJSON(['status' => 'success']);
    }

    return $this->response->setJSON(['status' => 'fail']);
}


}
