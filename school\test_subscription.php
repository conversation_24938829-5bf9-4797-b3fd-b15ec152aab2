<?php
/**
 * Subscription System Test Script
 * Run this from the school directory: php test_subscription.php
 */

// Simple database connection test
$host = 'localhost';
$database = 'school';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "🧪 Testing Subscription System\n";
echo "================================\n\n";

// Test 1: Check if plans exist
echo "1. Testing Plans in Database:\n";
try {
    $plans = $pdo->query('SELECT * FROM plans ORDER BY sort_order')->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($plans)) {
        echo "❌ No plans found in database\n";
        echo "   Run: php spark migrate\n\n";
    } else {
        echo "✅ Plans found:\n";
        foreach ($plans as $plan) {
            echo "   - {$plan['display_name']}: ₹{$plan['price_monthly']}/month\n";
        }
        echo "\n";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n\n";
}

// Test 2: Check if plan features exist
echo "2. Testing Plan Features:\n";
try {
    $features = $pdo->query('SELECT pf.*, p.display_name as plan_name FROM plan_features pf JOIN plans p ON p.id = pf.plan_id ORDER BY pf.plan_id')->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($features)) {
        echo "❌ No plan features found\n\n";
    } else {
        echo "✅ Plan features found:\n";
        $currentPlan = '';
        foreach ($features as $feature) {
            if ($currentPlan !== $feature['plan_name']) {
                $currentPlan = $feature['plan_name'];
                echo "   {$currentPlan}:\n";
            }
            $value = $feature['is_unlimited'] ? 'Unlimited' : $feature['feature_value'];
            echo "     - {$feature['feature_name']}: {$value}\n";
        }
        echo "\n";
    }
} catch (Exception $e) {
    echo "❌ Features error: " . $e->getMessage() . "\n\n";
}

// Test 3: Test Routes (basic check)
echo "3. Testing Routes Configuration:\n";
echo "✅ Routes configured:\n";
echo "   - /subscription/plans\n";
echo "   - /subscription/current\n";
echo "   - /payment/process\n";
echo "   - /payment/success\n";
echo "\n";

// Test 4: Check required tables
echo "4. Testing Database Tables:\n";
$requiredTables = ['plans', 'plan_features', 'subscriptions', 'payment_logs', 'usage_tracker'];
foreach ($requiredTables as $table) {
    try {
        $result = $pdo->query("SHOW TABLES LIKE '{$table}'")->fetchAll();
        if (empty($result)) {
            echo "❌ Table '{$table}' missing\n";
        } else {
            echo "✅ Table '{$table}' exists\n";
        }
    } catch (Exception $e) {
        echo "❌ Error checking table '{$table}': " . $e->getMessage() . "\n";
    }
}
echo "\n";

// Test 5: Test file permissions
echo "5. Testing File Permissions:\n";
$requiredFiles = [
    'app/Controllers/Subscription.php',
    'app/Controllers/Payment.php',
    'app/Services/SubscriptionService.php',
    'app/Views/subscription/plans.php',
    'app/Views/payment/process.php'
];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        echo "✅ {$file} exists\n";
    } else {
        echo "❌ {$file} missing\n";
    }
}
echo "\n";

echo "🎯 Test Summary:\n";
echo "================\n";
echo "If all tests show ✅, your subscription system is ready!\n";
echo "If you see ❌, fix those issues first.\n\n";

echo "🚀 Next Steps for Manual Testing:\n";
echo "1. Start your server: php spark serve\n";
echo "2. Visit: http://localhost:8080/subscription/plans\n";
echo "3. Test plan selection and payment flow\n";
echo "4. Check subscription status at: http://localhost:8080/subscription/current\n\n";

echo "📝 Test URLs:\n";
echo "- Plans: http://localhost:8080/subscription/plans\n";
echo "- Current: http://localhost:8080/subscription/current\n";
echo "- Payment: http://localhost:8080/payment/process\n";
echo "- History: http://localhost:8080/payment/history\n\n";

echo "Test completed! ✨\n";
?>
