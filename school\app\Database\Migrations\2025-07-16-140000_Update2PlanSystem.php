<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class Update2PlanSystem extends Migration
{
    public function up()
    {
        // Clear existing plans and features (handle foreign keys)
        $this->db->query('SET FOREIGN_KEY_CHECKS = 0');
        $this->db->table('plan_features')->truncate();
        $this->db->table('plans')->truncate();
        $this->db->query('SET FOREIGN_KEY_CHECKS = 1');

        // Insert 2-plan system data
        $this->db->table('plans')->insertBatch([
            [
                'name' => 'free',
                'display_name' => 'Free Trial',
                'description' => 'Perfect for getting started with basic question bank management',
                'price_monthly' => 0.00,
                'price_yearly' => 0.00,
                'currency' => 'INR',
                'billing_cycle' => 'monthly',
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'name' => 'professional',
                'display_name' => 'Professional',
                'description' => 'Advanced features for growing educational institutions',
                'price_monthly' => 999.00,
                'price_yearly' => 9990.00, // 2 months free (10 months price)
                'currency' => 'INR',
                'billing_cycle' => 'monthly',
                'is_active' => true,
                'is_popular' => true,
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);

        // Get plan IDs
        $freePlan = $this->db->table('plans')->where('name', 'free')->get()->getRowArray();
        $proPlan = $this->db->table('plans')->where('name', 'professional')->get()->getRowArray();

        // Insert plan features for Free Trial
        $this->db->table('plan_features')->insertBatch([
            // Free Plan Features
            [
                'plan_id' => $freePlan['id'],
                'feature_key' => 'max_users',
                'feature_name' => 'Maximum Users',
                'feature_value' => '5',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $freePlan['id'],
                'feature_key' => 'max_questions',
                'feature_name' => 'Maximum Questions',
                'feature_value' => '50',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $freePlan['id'],
                'feature_key' => 'max_subjects',
                'feature_name' => 'Maximum Subjects',
                'feature_value' => '5',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $freePlan['id'],
                'feature_key' => 'allow_export',
                'feature_name' => 'Export Questions',
                'feature_value' => 'false',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $freePlan['id'],
                'feature_key' => 'allow_print',
                'feature_name' => 'Print Questions',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $freePlan['id'],
                'feature_key' => 'support_level',
                'feature_name' => 'Support Level',
                'feature_value' => 'email',
                'value_type' => 'string',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],

            // Professional Plan Features
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'max_users',
                'feature_name' => 'Maximum Users',
                'feature_value' => '50',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'max_questions',
                'feature_name' => 'Maximum Questions',
                'feature_value' => '-1',
                'value_type' => 'integer',
                'is_unlimited' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'max_subjects',
                'feature_name' => 'Maximum Subjects',
                'feature_value' => '-1',
                'value_type' => 'integer',
                'is_unlimited' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'allow_export',
                'feature_name' => 'Export Questions',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'allow_print',
                'feature_name' => 'Print Questions',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'support_level',
                'feature_name' => 'Support Level',
                'feature_value' => 'priority',
                'value_type' => 'string',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'audit_logging',
                'feature_name' => 'Audit Logging',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => $proPlan['id'],
                'feature_key' => 'advanced_analytics',
                'feature_name' => 'Advanced Analytics',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ]);
    }

    public function down()
    {
        // Clear the tables
        $this->db->table('plan_features')->truncate();
        $this->db->table('plans')->truncate();
    }
}
