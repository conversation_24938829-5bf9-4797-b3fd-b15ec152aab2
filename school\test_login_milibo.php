<?php
// Simple database connection
$host = 'localhost';
$dbname = 'school';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get milibo user
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "User found: " . $user['email'] . "\n";
        echo "Status: " . $user['status'] . "\n";
        echo "Is Deleted: " . $user['is_deleted'] . "\n";
        echo "School ID: " . $user['school_id'] . "\n";
        echo "Password hash: " . $user['password'] . "\n\n";
        
        // Test common passwords
        $testPasswords = ['123456', 'password', 'admin', 'schooladmin', 'milibo', 'test123', 'admin123', 'milibo123'];
        
        foreach ($testPasswords as $testPassword) {
            if (password_verify($testPassword, $user['password'])) {
                echo "✅ Correct <NAME_EMAIL>: " . $testPassword . "\n";
                
                // Now test the login via curl
                echo "\nTesting login via curl...\n";
                
                $postData = http_build_query([
                    'email' => '<EMAIL>',
                    'password' => $testPassword
                ]);
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, 'http://localhost:12/10.1.0/school/staff/authenticate');
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'X-Requested-With: XMLHttpRequest',
                    'Content-Type: application/x-www-form-urlencoded'
                ]);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                echo "HTTP Code: " . $httpCode . "\n";
                echo "Response: " . $response . "\n";
                
                exit;
            } else {
                echo "❌ Wrong password: " . $testPassword . "\n";
            }
        }
        
        echo "\nNone of the test passwords worked. Setting new password to '123456'...\n";
        
        // Update password
        $newPassword = password_hash('123456', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE email = ?");
        $result = $stmt->execute([$newPassword, '<EMAIL>']);
        
        if ($result) {
            echo "✅ Password updated successfully!\n";
            echo "New password: 123456\n";
            
            // Test login again
            echo "\nTesting login with new password...\n";
            
            $postData = http_build_query([
                'email' => '<EMAIL>',
                'password' => '123456'
            ]);
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'http://localhost:12/10.1.0/school/staff/authenticate');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'X-Requested-With: XMLHttpRequest',
                'Content-Type: application/x-www-form-urlencoded'
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "HTTP Code: " . $httpCode . "\n";
            echo "Response: " . $response . "\n";
        }
        
    } else {
        echo "❌ User not found!\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
