<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PlanModel;
use App\Models\SubscriptionModel;
use App\Models\PaymentLogModel;
use App\Services\SubscriptionService;
use App\Services\RazorpayService;

class Payment extends BaseController
{
    protected $planModel;
    protected $subscriptionModel;
    protected $paymentLogModel;
    protected $subscriptionService;
    protected $emailService;
    protected $razorpayService;

    public function __construct()
    {
        $this->planModel = new PlanModel();
        $this->subscriptionModel = new SubscriptionModel();
        $this->paymentLogModel = new PaymentLogModel();
        $this->subscriptionService = new SubscriptionService();
        $this->emailService = new \App\Services\EmailService();
        $this->razorpayService = new RazorpayService();
    }

    /**
     * Show payment page
     */
    public function process()
    {
        // Check authentication
        $session = session();
        $isLoggedIn = $session->get('logged_in');
        $userRole = $session->get('role');
        $schoolId = $session->get('school_id');

        if (!$isLoggedIn || $userRole !== 'schooladmin') {
            return redirect()->to('/login/schooladmin')->with('error', 'Please login to continue with payment');
        }

        if (!$schoolId) {
            // Try to get school ID from database as fallback
            $schoolModel = new \App\Models\SchoolModel();
            $school = $schoolModel->where('email', $session->get('email'))->first();

            if ($school) {
                $schoolId = $school['id'];
                $session->set('school_id', $schoolId);
            } else {
                return redirect()->to('/login/schooladmin')->with('error', 'School information not found. Please login again');
            }
        }

        // Get plan details from session or request
        $planId = session()->get('selected_plan_id') ?? $this->request->getGet('plan_id');
        $billingCycle = session()->get('selected_billing_cycle') ?? $this->request->getGet('billing_cycle') ?? 'monthly';

        if (!$planId) {
            return redirect()->to('/subscription/plans')->with('error', 'Please select a plan first');
        }

        $plan = $this->planModel->find($planId);
        if (!$plan) {
            return redirect()->to('/subscription/plans')->with('error', 'Invalid plan selected');
        }

        // Calculate amount
        $amount = ($billingCycle === 'yearly') ? $plan['price_yearly'] : $plan['price_monthly'];

        // If amount is 0, redirect to free activation
        if ($amount == 0) {
            return $this->activateFreePlan($schoolId, $plan, $billingCycle);
        }

        $data = [
            'title' => 'Complete Payment',
            'plan' => $plan,
            'amount' => $amount,
            'billing_cycle' => $billingCycle,
            'school_id' => $schoolId
        ];

        return view('payment/process', $data);
    }

    /**
     * Initialize payment with Razorpay
     */
    public function initiate()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/payment/process');
        }

        // Check authentication
        $session = session();
        $isLoggedIn = $session->get('logged_in');
        $userRole = $session->get('role');
        $schoolId = $session->get('school_id');

        if (!$isLoggedIn || $userRole !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Authentication required. Please login again.'
            ]);
        }

        if (!$schoolId) {
            // Try to get school ID from database as fallback
            $schoolModel = new \App\Models\SchoolModel();
            $school = $schoolModel->where('email', $session->get('email'))->first();

            if ($school) {
                $schoolId = $school['id'];
                $session->set('school_id', $schoolId);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'School information not found. Please login again.'
                ]);
            }
        }

        $planId = $this->request->getPost('plan_id');
        $billingCycle = $this->request->getPost('billing_cycle') ?? 'monthly';
        $paymentGateway = $this->request->getPost('payment_gateway') ?? 'razorpay';

        // Validation
        if (!$planId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Plan ID is required'
            ]);
        }

        $plan = $this->planModel->find($planId);
        if (!$plan) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid plan selected'
            ]);
        }

        // Calculate amount
        $amount = ($billingCycle === 'yearly') ? $plan['price_yearly'] : $plan['price_monthly'];

        // For free plan, no payment needed
        if ($amount == 0) {
            $subscriptionId = $this->subscriptionService->createSchoolSubscription($schoolId, $plan['name'], $billingCycle, true);
            return $this->response->setJSON([
                'success' => true,
                'free_plan' => true,
                'message' => 'Free trial activated successfully'
            ]);
        }

        // Handle test payment in development mode
        if (ENVIRONMENT === 'development' && $paymentGateway === 'test') {
            // Create a test transaction ID
            $transactionId = 'test_' . $schoolId . '_' . time();

            // Create subscription record (pending payment)
            $subscriptionId = $this->subscriptionService->createSchoolSubscription($schoolId, $plan['name'], $billingCycle, false);

            // Create payment log for test payment
            $paymentData = [
                'school_id' => $schoolId,
                'subscription_id' => $subscriptionId,
                'transaction_id' => $transactionId,
                'amount' => $amount,
                'currency' => 'INR',
                'payment_gateway' => 'test',
                'gateway_order_id' => $transactionId,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];

            $this->paymentLogModel->insert($paymentData);

            return $this->response->setJSON([
                'success' => true,
                'gateway' => 'test',
                'transaction_id' => $transactionId,
                'payment_data' => [
                    'plan_name' => $plan['display_name'],
                    'amount' => $amount,
                    'currency' => 'INR'
                ]
            ]);
        }

        // Get school details for payment
        $schoolModel = new \App\Models\SchoolModel();
        $school = $schoolModel->find($schoolId);

        // Create Razorpay order
        $orderResult = $this->razorpayService->createOrder(
            $amount,
            'INR',
            'subscription_' . $schoolId . '_' . time(),
            [
                'school_id' => $schoolId,
                'plan_id' => $planId,
                'billing_cycle' => $billingCycle
            ]
        );

        if (!$orderResult['success']) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to create payment order: ' . $orderResult['error']
            ]);
        }

        // Create subscription record (pending payment)
        $subscriptionId = $this->subscriptionService->createSchoolSubscription($schoolId, $plan['name'], $billingCycle, false);

        // Create payment log
        $paymentData = [
            'school_id' => $schoolId,
            'subscription_id' => $subscriptionId,
            'transaction_id' => $orderResult['order_id'],
            'amount' => $amount,
            'currency' => 'INR',
            'payment_gateway' => 'razorpay',
            'gateway_order_id' => $orderResult['order_id'],
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->paymentLogModel->insert($paymentData);

        // Get Razorpay payment options for frontend
        $paymentOptions = $this->razorpayService->getPaymentOptions(
            $orderResult['order_id'],
            $amount,
            $school['name'],
            $school['email']
        );

        return $this->response->setJSON([
            'success' => true,
            'order_id' => $orderResult['order_id'],
            'payment_options' => $paymentOptions,
            'amount' => $amount,
            'plan_name' => $plan['display_name'],
            'gateway' => 'razorpay'
        ]);
    }

    /**
     * Handle payment success callback
     */
    public function success()
    {
        $transactionId = $this->request->getGet('transaction_id');
        $gatewayTransactionId = $this->request->getGet('gateway_transaction_id');
        $paymentGateway = $this->request->getGet('gateway') ?? 'razorpay';

        if (!$transactionId) {
            return redirect()->to('/subscription/plans')->with('error', 'Invalid payment transaction');
        }

        // Handle test payment in development mode
        if (ENVIRONMENT === 'development' && $paymentGateway === 'test') {
            // Complete test payment and activate subscription
            $result = $this->subscriptionService->completePayment(
                $transactionId,
                $gatewayTransactionId ?: 'test_' . time(),
                ['test_mode' => true, 'status' => 'success']
            );

            if ($result) {
                return redirect()->to('/subscription/current')->with('success', 'Test payment successful! Your subscription is now active.');
            } else {
                return redirect()->to('/payment/process')->with('error', 'Test payment processing failed. Please try again.');
            }
        }

        // Verify payment with gateway
        $verificationResult = $this->verifyPaymentWithGateway($paymentGateway, $transactionId, $gatewayTransactionId);

        if ($verificationResult['success']) {
            // Complete payment and activate subscription
            $result = $this->subscriptionService->completePayment(
                $transactionId,
                $gatewayTransactionId,
                $verificationResult['gateway_response']
            );

            if ($result) {
                // Send success email notification
                $transaction = $this->paymentLogModel->getPaymentByTransactionId($transactionId);
                if ($transaction) {
                    $this->sendPaymentSuccessNotification($transaction);
                }

                return redirect()->to('/subscription/current')->with('success', 'Payment successful! Your subscription is now active.');
            } else {
                return redirect()->to('/payment/process')->with('error', 'Payment verification failed. Please contact support.');
            }
        } else {
            // Handle failed payment
            $this->subscriptionService->failPayment($transactionId, $verificationResult['message'], $verificationResult['gateway_response']);

            // Send failure email notification
            $transaction = $this->paymentLogModel->getPaymentByTransactionId($transactionId);
            if ($transaction) {
                $this->sendPaymentFailureNotification($transaction);
            }

            return redirect()->to('/payment/process')->with('error', 'Payment failed: ' . $verificationResult['message']);
        }
    }

    /**
     * Handle payment verification (Razorpay callback)
     */
    public function verify()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/payment/process');
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Unauthorized'
            ]);
        }

        $orderId = $this->request->getPost('razorpay_order_id');
        $paymentId = $this->request->getPost('razorpay_payment_id');
        $signature = $this->request->getPost('razorpay_signature');

        if (!$orderId || !$paymentId || !$signature) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Missing payment verification data'
            ]);
        }

        // Verify payment signature
        $isValid = $this->razorpayService->verifyPaymentSignature($orderId, $paymentId, $signature);

        if (!$isValid) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Payment verification failed'
            ]);
        }

        // Get payment log
        $paymentLog = $this->paymentLogModel->where('gateway_order_id', $orderId)->first();

        if (!$paymentLog) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Payment record not found'
            ]);
        }

        // Update payment status
        $this->paymentLogModel->update($paymentLog['id'], [
            'gateway_payment_id' => $paymentId,
            'status' => 'completed',
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        // Activate subscription
        $this->subscriptionModel->update($paymentLog['subscription_id'], [
            'status' => 'active',
            'started_at' => date('Y-m-d H:i:s'),
            'expires_at' => date('Y-m-d H:i:s', strtotime('+1 month'))
        ]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Payment verified and subscription activated successfully'
        ]);
    }

    /**
     * Handle payment failure callback
     */
    public function failure()
    {
        $transactionId = $this->request->getGet('transaction_id');
        $reason = $this->request->getGet('reason') ?? 'Payment failed';

        if ($transactionId) {
            $this->subscriptionService->failPayment($transactionId, $reason);
        }

        return redirect()->to('/payment/process')->with('error', 'Payment failed. Please try again.');
    }

    /**
     * Handle payment gateway webhooks
     */
    public function webhook($gateway = 'razorpay')
    {
        // Get webhook payload
        $payload = $this->request->getJSON(true);
        
        if (!$payload) {
            return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid payload']);
        }

        // Process webhook based on gateway
        switch ($gateway) {
            case 'razorpay':
                return $this->handleRazorpayWebhook($payload);
            case 'stripe':
                return $this->handleStripeWebhook($payload);
            default:
                return $this->response->setStatusCode(400)->setJSON(['error' => 'Unsupported gateway']);
        }
    }

    /**
     * Get payment history for school
     */
    public function history()
    {
        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return redirect()->to('/login');
        }

        $schoolId = session()->get('school_id');

        // Get filter parameters
        $status = $this->request->getGet('status');
        $dateFrom = $this->request->getGet('date_from');
        $dateTo = $this->request->getGet('date_to');

        // Get payments with filters
        $payments = $this->paymentLogModel->getSchoolPaymentHistory($schoolId, $status, $dateFrom, $dateTo);

        // Get payment summary
        $summary = $this->paymentLogModel->getPaymentSummary($schoolId);

        // Get current subscription for next billing date
        $currentSubscription = $this->subscriptionModel->getCurrentSubscription($schoolId);
        if ($currentSubscription) {
            $summary['next_billing'] = $currentSubscription['expires_at'];
        }

        $data = [
            'title' => 'Payment History',
            'payments' => $payments,
            'summary' => $summary
        ];

        return view('payment/history', $data);
    }

    /**
     * Get transaction details for modal
     */
    public function transactionDetails($transactionId)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/payment/history');
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Unauthorized'
            ]);
        }

        $schoolId = session()->get('school_id');

        // Get transaction details
        $transaction = $this->paymentLogModel->getTransactionDetails($transactionId, $schoolId);

        if (!$transaction) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Transaction not found'
            ]);
        }

        // Generate HTML for transaction details
        $html = $this->generateTransactionDetailsHTML($transaction);

        return $this->response->setJSON([
            'success' => true,
            'html' => $html
        ]);
    }

    /**
     * Generate PDF invoice for completed payment
     */
    public function invoice($transactionId)
    {
        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return redirect()->to('/login');
        }

        $schoolId = session()->get('school_id');

        // Get transaction details
        $transaction = $this->paymentLogModel->getTransactionDetails($transactionId, $schoolId);

        if (!$transaction || $transaction['status'] !== 'completed') {
            return redirect()->to('/payment/history')->with('error', 'Invoice not available for this transaction');
        }

        // Generate PDF invoice
        return $this->generatePDFInvoice($transaction);
    }

    /**
     * Activate free plan without payment
     */
    private function activateFreePlan($schoolId, $plan, $billingCycle)
    {
        $subscriptionId = $this->subscriptionService->createSchoolSubscription($schoolId, $plan['name'], $billingCycle, true);
        
        if ($subscriptionId) {
            return redirect()->to('/subscription/current')->with('success', 'Free plan activated successfully!');
        } else {
            return redirect()->to('/subscription/plans')->with('error', 'Failed to activate free plan');
        }
    }

    /**
     * Initialize payment with selected gateway
     */
    private function initializePaymentGateway($gateway, $transactionId, $amount, $plan)
    {
        switch ($gateway) {
            case 'razorpay':
                return $this->initializeRazorpay($transactionId, $amount, $plan);
            case 'stripe':
                return $this->initializeStripe($transactionId, $amount, $plan);
            default:
                return ['success' => false, 'message' => 'Unsupported payment gateway'];
        }
    }

    /**
     * Initialize Razorpay payment
     */
    private function initializeRazorpay($transactionId, $amount, $plan)
    {
        try {
            // Get Razorpay credentials from environment
            $keyId = env('RAZORPAY_KEY_ID');
            $keySecret = env('RAZORPAY_KEY_SECRET');
            $paymentMode = env('PAYMENT_MODE', 'development');

            if (empty($keyId) || empty($keySecret)) {
                throw new \Exception('Razorpay credentials not configured');
            }

            // Validate key format
            if ($paymentMode === 'production' && !str_starts_with($keyId, 'rzp_live_')) {
                throw new \Exception('Live payment mode requires live API keys');
            }

            // Create actual Razorpay order for production
            if ($paymentMode === 'production') {
                $orderId = $this->createRazorpayOrder($keyId, $keySecret, $amount, $transactionId);
                if (!$orderId) {
                    throw new \Exception('Failed to create Razorpay order');
                }
            } else {
                // For development/testing
                $orderId = 'order_' . $transactionId;
            }

            return [
                'success' => true,
                'data' => [
                    'key' => $keyId,
                    'amount' => $amount * 100, // Razorpay expects amount in paise
                    'currency' => 'INR',
                    'order_id' => $orderId,
                    'name' => 'School Question Bank',
                    'description' => $plan['display_name'] . ' Plan',
                    'prefill' => [
                        'name' => session()->get('school_name'),
                        'email' => session()->get('email')
                    ],
                    'theme' => [
                        'color' => '#3399cc'
                    ],
                    'modal' => [
                        'ondismiss' => 'function(){console.log("Payment cancelled")}'
                    ],
                    'notes' => [
                        'school_id' => session()->get('school_id'),
                        'plan_id' => $plan['id'],
                        'transaction_id' => $transactionId
                    ]
                ]
            ];
        } catch (\Exception $e) {
            log_message('error', 'Razorpay initialization failed: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Payment gateway configuration error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create actual Razorpay order for production
     */
    private function createRazorpayOrder($keyId, $keySecret, $amount, $transactionId)
    {
        try {
            $url = 'https://api.razorpay.com/v1/orders';

            $data = [
                'amount' => $amount * 100, // Amount in paise
                'currency' => 'INR',
                'receipt' => $transactionId,
                'notes' => [
                    'school_id' => session()->get('school_id'),
                    'transaction_id' => $transactionId
                ]
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Basic ' . base64_encode($keyId . ':' . $keySecret)
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $orderData = json_decode($response, true);
                return $orderData['id'] ?? null;
            } else {
                log_message('error', 'Razorpay order creation failed: ' . $response);
                return null;
            }
        } catch (\Exception $e) {
            log_message('error', 'Razorpay order creation exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Initialize Stripe payment
     */
    private function initializeStripe($transactionId, $amount, $plan)
    {
        // This is a placeholder - you'll need to implement actual Stripe integration
        return [
            'success' => true,
            'data' => [
                'public_key' => env('STRIPE_PUBLIC_KEY', 'pk_test_'),
                'amount' => $amount,
                'currency' => 'inr',
                'transaction_id' => $transactionId,
                'description' => $plan['display_name'] . ' Plan'
            ]
        ];
    }

    /**
     * Verify payment with gateway
     */
    private function verifyPaymentWithGateway($gateway, $transactionId, $gatewayTransactionId)
    {
        switch ($gateway) {
            case 'razorpay':
                return $this->verifyRazorpayPayment($transactionId, $gatewayTransactionId);
            case 'stripe':
                return $this->verifyStripePayment($transactionId, $gatewayTransactionId);
            default:
                return ['success' => false, 'message' => 'Unsupported gateway'];
        }
    }

    /**
     * Verify Razorpay payment
     */
    private function verifyRazorpayPayment($transactionId, $gatewayTransactionId)
    {
        try {
            $keyId = env('RAZORPAY_KEY_ID');
            $keySecret = env('RAZORPAY_KEY_SECRET');
            $paymentMode = env('PAYMENT_MODE', 'development');

            // For development mode, return success
            if ($paymentMode === 'development') {
                return [
                    'success' => true,
                    'gateway_response' => ['status' => 'captured', 'payment_id' => $gatewayTransactionId]
                ];
            }

            // For production, verify with Razorpay API
            $url = "https://api.razorpay.com/v1/payments/{$gatewayTransactionId}";

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Basic ' . base64_encode($keyId . ':' . $keySecret)
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($httpCode === 200) {
                $paymentData = json_decode($response, true);

                // Verify payment status and amount
                if ($paymentData['status'] === 'captured') {
                    return [
                        'success' => true,
                        'gateway_response' => $paymentData
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Payment not captured: ' . $paymentData['status']
                    ];
                }
            } else {
                log_message('error', 'Razorpay verification failed: ' . $response);
                return [
                    'success' => false,
                    'message' => 'Payment verification failed'
                ];
            }
        } catch (\Exception $e) {
            log_message('error', 'Razorpay verification exception: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Payment verification error'
            ];
        }
    }

    /**
     * Verify Stripe payment
     */
    private function verifyStripePayment($transactionId, $gatewayTransactionId)
    {
        // Placeholder - implement actual Stripe verification
        return [
            'success' => true,
            'gateway_response' => ['status' => 'succeeded', 'payment_intent' => $gatewayTransactionId]
        ];
    }

    /**
     * Handle Razorpay webhook
     */
    private function handleRazorpayWebhook($payload)
    {
        try {
            // Verify webhook signature (important for security)
            $webhookSecret = env('RAZORPAY_WEBHOOK_SECRET');
            if ($webhookSecret) {
                $signature = $this->request->getHeaderLine('X-Razorpay-Signature');
                $body = $this->request->getBody();

                $expectedSignature = hash_hmac('sha256', $body, $webhookSecret);
                if (!hash_equals($expectedSignature, $signature)) {
                    log_message('error', 'Razorpay webhook signature verification failed');
                    return $this->response->setStatusCode(400)->setJSON(['error' => 'Invalid signature']);
                }
            }

            // Handle different webhook events
            $event = $payload['event'] ?? '';

            switch ($event) {
                case 'payment.captured':
                    return $this->handlePaymentCaptured($payload['payload']['payment']['entity']);

                case 'payment.failed':
                    return $this->handlePaymentFailed($payload['payload']['payment']['entity']);

                case 'order.paid':
                    return $this->handleOrderPaid($payload['payload']['order']['entity']);

                default:
                    log_message('info', 'Unhandled Razorpay webhook event: ' . $event);
                    return $this->response->setJSON(['status' => 'ok']);
            }
        } catch (\Exception $e) {
            log_message('error', 'Razorpay webhook error: ' . $e->getMessage());
            return $this->response->setStatusCode(500)->setJSON(['error' => 'Webhook processing failed']);
        }
    }

    /**
     * Handle payment captured webhook
     */
    private function handlePaymentCaptured($paymentData)
    {
        $paymentId = $paymentData['id'];
        $orderId = $paymentData['order_id'];

        // Find transaction by order ID or payment ID
        $transaction = $this->paymentLogModel->where('gateway_order_id', $orderId)
                                           ->orWhere('gateway_transaction_id', $paymentId)
                                           ->first();

        if ($transaction && $transaction['status'] === 'pending') {
            // Update transaction status
            $result = $this->subscriptionService->completePayment(
                $transaction['transaction_id'],
                $paymentId,
                $paymentData
            );

            if ($result) {
                // Send success email notification
                $this->sendPaymentSuccessNotification($transaction);
            }
        }

        return $this->response->setJSON(['status' => 'ok']);
    }

    /**
     * Handle payment failed webhook
     */
    private function handlePaymentFailed($paymentData)
    {
        $paymentId = $paymentData['id'];
        $orderId = $paymentData['order_id'];

        // Find and update transaction
        $transaction = $this->paymentLogModel->where('gateway_order_id', $orderId)
                                           ->orWhere('gateway_transaction_id', $paymentId)
                                           ->first();

        if ($transaction) {
            $this->paymentLogModel->update($transaction['id'], [
                'status' => 'failed',
                'gateway_response' => json_encode($paymentData),
                'notes' => $paymentData['error_description'] ?? 'Payment failed',
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // Send failure email notification
            $updatedTransaction = $this->paymentLogModel->find($transaction['id']);
            $this->sendPaymentFailureNotification($updatedTransaction);
        }

        return $this->response->setJSON(['status' => 'ok']);
    }

    /**
     * Handle order paid webhook
     */
    private function handleOrderPaid($orderData)
    {
        // This is called when an order is fully paid
        // Usually after payment.captured
        return $this->response->setJSON(['status' => 'ok']);
    }

    /**
     * Handle Stripe webhook
     */
    private function handleStripeWebhook($payload)
    {
        // Placeholder - implement actual Stripe webhook handling
        return $this->response->setJSON(['status' => 'ok']);
    }

    /**
     * Test webhook endpoint for development
     */
    public function testWebhook($gateway = 'razorpay')
    {
        if (ENVIRONMENT !== 'development') {
            return $this->response->setStatusCode(404);
        }

        // Sample webhook payloads for testing
        $samplePayloads = [
            'razorpay' => [
                'success' => [
                    'event' => 'payment.captured',
                    'payload' => [
                        'payment' => [
                            'entity' => [
                                'id' => 'pay_test_' . uniqid(),
                                'order_id' => 'order_test_' . uniqid(),
                                'amount' => 50000,
                                'currency' => 'INR',
                                'status' => 'captured',
                                'method' => 'card'
                            ]
                        ]
                    ]
                ],
                'failure' => [
                    'event' => 'payment.failed',
                    'payload' => [
                        'payment' => [
                            'entity' => [
                                'id' => 'pay_test_' . uniqid(),
                                'order_id' => 'order_test_' . uniqid(),
                                'amount' => 50000,
                                'currency' => 'INR',
                                'status' => 'failed',
                                'method' => 'card',
                                'error_description' => 'Payment failed due to insufficient funds'
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $type = $this->request->getGet('type') ?? 'success';
        $payload = $samplePayloads[$gateway][$type] ?? $samplePayloads[$gateway]['success'];

        // Process the test webhook
        switch ($gateway) {
            case 'razorpay':
                $result = $this->handleRazorpayWebhook($payload);
                break;
            default:
                return $this->response->setJSON(['error' => 'Unsupported gateway']);
        }

        return $this->response->setJSON([
            'message' => 'Test webhook processed',
            'gateway' => $gateway,
            'type' => $type,
            'payload' => $payload,
            'result' => $result
        ]);
    }

    /**
     * Generate HTML for transaction details modal
     */
    private function generateTransactionDetailsHTML($transaction)
    {
        $statusColors = [
            'completed' => 'bg-green-100 text-green-800',
            'pending' => 'bg-yellow-100 text-yellow-800',
            'failed' => 'bg-red-100 text-red-800',
            'refunded' => 'bg-blue-100 text-blue-800',
            'cancelled' => 'bg-gray-100 text-gray-800'
        ];

        $statusClass = $statusColors[$transaction['status']] ?? 'bg-gray-100 text-gray-800';

        $html = '
        <div class="space-y-6">
            <!-- Transaction Header -->
            <div class="text-center pb-4 border-b border-gray-200">
                <h4 class="text-xl font-semibold text-gray-900 mb-2">Transaction Details</h4>
                <span class="inline-flex px-3 py-1 text-sm font-semibold rounded-full ' . $statusClass . '">
                    ' . ucfirst($transaction['status']) . '
                </span>
            </div>

            <!-- Transaction Info -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-500">Transaction ID</label>
                    <p class="mt-1 text-sm font-mono text-gray-900">' . $transaction['transaction_id'] . '</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500">Gateway Transaction ID</label>
                    <p class="mt-1 text-sm font-mono text-gray-900">' . ($transaction['gateway_transaction_id'] ?? 'N/A') . '</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500">Amount</label>
                    <p class="mt-1 text-lg font-semibold text-gray-900">₹' . number_format($transaction['amount']) . '</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500">Payment Method</label>
                    <p class="mt-1 text-sm text-gray-900 capitalize">' . ($transaction['payment_method'] ?? 'N/A') . '</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500">Plan</label>
                    <p class="mt-1 text-sm text-gray-900">' . ($transaction['plan_name'] ?? 'N/A') . '</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500">Billing Cycle</label>
                    <p class="mt-1 text-sm text-gray-900 capitalize">' . ($transaction['billing_cycle'] ?? 'N/A') . '</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500">Created Date</label>
                    <p class="mt-1 text-sm text-gray-900">' . date('M d, Y H:i', strtotime($transaction['created_at'])) . '</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-500">Processed Date</label>
                    <p class="mt-1 text-sm text-gray-900">' . ($transaction['processed_at'] ? date('M d, Y H:i', strtotime($transaction['processed_at'])) : 'N/A') . '</p>
                </div>
            </div>';

        // Add gateway response if available
        if (!empty($transaction['gateway_response'])) {
            $gatewayResponse = json_decode($transaction['gateway_response'], true);
            if ($gatewayResponse) {
                $html .= '
                <div class="pt-4 border-t border-gray-200">
                    <label class="block text-sm font-medium text-gray-500 mb-2">Gateway Response</label>
                    <div class="bg-gray-50 rounded-lg p-3 text-xs font-mono text-gray-700 max-h-32 overflow-y-auto">
                        ' . htmlspecialchars(json_encode($gatewayResponse, JSON_PRETTY_PRINT)) . '
                    </div>
                </div>';
            }
        }

        // Add notes if available
        if (!empty($transaction['notes'])) {
            $html .= '
            <div class="pt-4 border-t border-gray-200">
                <label class="block text-sm font-medium text-gray-500 mb-2">Notes</label>
                <p class="text-sm text-gray-700">' . htmlspecialchars($transaction['notes']) . '</p>
            </div>';
        }

        $html .= '</div>';

        return $html;
    }

    /**
     * Generate PDF invoice
     */
    private function generatePDFInvoice($transaction)
    {
        // Load TCPDF library (you'll need to install this via Composer)
        // For now, we'll create a simple HTML invoice that can be printed as PDF

        $invoiceNumber = env('INVOICE_PREFIX', 'SQB') . str_pad($transaction['id'], env('INVOICE_NUMBER_LENGTH', 6), '0', STR_PAD_LEFT);

        $html = $this->generateInvoiceHTML($transaction, $invoiceNumber);

        // Set headers for PDF download
        $this->response->setHeader('Content-Type', 'text/html');
        $this->response->setHeader('Content-Disposition', 'inline; filename="invoice-' . $invoiceNumber . '.html"');

        return $this->response->setBody($html);
    }

    /**
     * Generate invoice HTML
     */
    private function generateInvoiceHTML($transaction, $invoiceNumber)
    {
        $companyName = env('COMPANY_NAME', 'School Question Bank');
        $companyAddress = env('COMPANY_ADDRESS', 'Your Company Address');
        $companyPhone = env('COMPANY_PHONE', '+91-XXXXXXXXXX');
        $companyEmail = env('COMPANY_EMAIL', '<EMAIL>');

        $gstEnabled = env('GST_ENABLED', true);
        $gstRate = env('GST_RATE', 18);
        $gstNumber = env('GST_NUMBER', 'YOUR_GST_NUMBER_HERE');

        $subtotal = $transaction['amount'];
        $gstAmount = $gstEnabled ? ($subtotal * $gstRate / 100) : 0;
        $total = $subtotal + $gstAmount;

        return '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Invoice ' . $invoiceNumber . '</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                .invoice-header { text-align: center; margin-bottom: 30px; }
                .company-info { margin-bottom: 20px; }
                .invoice-details { display: flex; justify-content: space-between; margin-bottom: 30px; }
                .customer-info, .invoice-info { width: 48%; }
                .invoice-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                .invoice-table th, .invoice-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
                .invoice-table th { background-color: #f5f5f5; }
                .total-section { text-align: right; margin-top: 20px; }
                .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="invoice-header">
                <h1>' . $companyName . '</h1>
                <p>' . $companyAddress . '</p>
                <p>Phone: ' . $companyPhone . ' | Email: ' . $companyEmail . '</p>
                ' . ($gstEnabled ? '<p>GST Number: ' . $gstNumber . '</p>' : '') . '
            </div>

            <div class="invoice-details">
                <div class="customer-info">
                    <h3>Bill To:</h3>
                    <p><strong>' . session()->get('school_name') . '</strong></p>
                    <p>Email: ' . session()->get('email') . '</p>
                    <p>School ID: ' . session()->get('school_id') . '</p>
                </div>

                <div class="invoice-info">
                    <h3>Invoice Details:</h3>
                    <p><strong>Invoice Number:</strong> ' . $invoiceNumber . '</p>
                    <p><strong>Invoice Date:</strong> ' . date('M d, Y', strtotime($transaction['created_at'])) . '</p>
                    <p><strong>Payment Date:</strong> ' . ($transaction['processed_at'] ? date('M d, Y', strtotime($transaction['processed_at'])) : 'N/A') . '</p>
                    <p><strong>Transaction ID:</strong> ' . $transaction['transaction_id'] . '</p>
                </div>
            </div>

            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>Description</th>
                        <th>Billing Cycle</th>
                        <th>Amount</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>' . ($transaction['plan_name'] ?? 'Subscription Plan') . '</td>
                        <td>' . ucfirst($transaction['billing_cycle'] ?? 'Monthly') . '</td>
                        <td>₹' . number_format($subtotal, 2) . '</td>
                    </tr>
                </tbody>
            </table>

            <div class="total-section">
                <p><strong>Subtotal: ₹' . number_format($subtotal, 2) . '</strong></p>
                ' . ($gstEnabled ? '<p>GST (' . $gstRate . '%): ₹' . number_format($gstAmount, 2) . '</p>' : '') . '
                <h3>Total: ₹' . number_format($total, 2) . '</h3>
                <p><strong>Payment Status: ' . ucfirst($transaction['status']) . '</strong></p>
            </div>

            <div class="footer">
                <p>Thank you for your business!</p>
                <p>This is a computer-generated invoice and does not require a signature.</p>
                <div class="no-print" style="margin-top: 20px;">
                    <button onclick="window.print()" style="padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer;">
                        Print Invoice
                    </button>
                </div>
            </div>
        </body>
        </html>';
    }

    /**
     * Send payment success notification
     */
    private function sendPaymentSuccessNotification($transaction)
    {
        try {
            // Get school details
            $schoolModel = new \App\Models\SchoolModel();
            $school = $schoolModel->find($transaction['school_id']);

            if (!$school) {
                return false;
            }

            // Get transaction details with plan info
            $transactionDetails = $this->paymentLogModel->getTransactionDetails($transaction['id'], $transaction['school_id']);

            if (!$transactionDetails) {
                return false;
            }

            // Send email
            return $this->emailService->sendPaymentSuccessEmail(
                $school['email'],
                $school['name'],
                $transactionDetails,
                $school['id']
            );

        } catch (\Exception $e) {
            log_message('error', 'Failed to send payment success notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Send payment failure notification
     */
    private function sendPaymentFailureNotification($transaction)
    {
        try {
            // Get school details
            $schoolModel = new \App\Models\SchoolModel();
            $school = $schoolModel->find($transaction['school_id']);

            if (!$school) {
                return false;
            }

            // Get transaction details with plan info
            $transactionDetails = $this->paymentLogModel->getTransactionDetails($transaction['id'], $transaction['school_id']);

            if (!$transactionDetails) {
                return false;
            }

            // Send email
            return $this->emailService->sendPaymentFailureEmail(
                $school['email'],
                $school['name'],
                $transactionDetails,
                $school['id']
            );

        } catch (\Exception $e) {
            log_message('error', 'Failed to send payment failure notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Debug method to complete pending payments (development only)
     */
    public function debugCompletePending()
    {
        if (ENVIRONMENT !== 'development') {
            return $this->response->setStatusCode(404);
        }

        $schoolId = session()->get('school_id');

        // If no school ID in session, try to get from request or use latest school with pending payments
        if (!$schoolId) {
            $schoolId = $this->request->getGet('school_id');

            if (!$schoolId) {
                // Get the school with the most recent pending payment
                $recentPayment = $this->paymentLogModel->where('status', 'pending')
                                                     ->orderBy('created_at', 'DESC')
                                                     ->first();
                if ($recentPayment) {
                    $schoolId = $recentPayment['school_id'];
                } else {
                    return $this->response->setJSON(['error' => 'No pending payments found']);
                }
            }
        }

        // Get pending payments for current school
        $pendingPayments = $this->paymentLogModel->where('school_id', $schoolId)
                                                ->where('status', 'pending')
                                                ->orderBy('created_at', 'DESC')
                                                ->limit(5)
                                                ->findAll();

        $completed = 0;
        foreach ($pendingPayments as $payment) {
            $result = $this->subscriptionService->completePayment(
                $payment['transaction_id'],
                'debug_' . time() . '_' . $payment['id'],
                ['debug_mode' => true, 'status' => 'success']
            );

            if ($result) {
                $completed++;
            }
        }

        return $this->response->setJSON([
            'message' => "Completed $completed pending payments",
            'total_pending' => count($pendingPayments)
        ]);
    }

    /**
     * Debug method to show session info (development only)
     */
    public function debugSession()
    {
        if (ENVIRONMENT !== 'development') {
            return $this->response->setStatusCode(404);
        }

        $session = session();
        return $this->response->setJSON([
            'logged_in' => $session->get('logged_in'),
            'school_id' => $session->get('school_id'),
            'school_name' => $session->get('school_name'),
            'email' => $session->get('email'),
            'role' => $session->get('role')
        ]);
    }
}
