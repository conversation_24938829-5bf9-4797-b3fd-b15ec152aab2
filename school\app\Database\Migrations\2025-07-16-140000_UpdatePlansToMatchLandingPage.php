<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UpdatePlansToMatchLandingPage extends Migration
{
    public function up()
    {
        // Clear existing plans and features (handle foreign key constraints)
        $this->db->query('SET FOREIGN_KEY_CHECKS = 0');
        $this->db->table('plan_features')->truncate();
        $this->db->table('plans')->truncate();
        $this->db->query('SET FOREIGN_KEY_CHECKS = 1');

        // Insert the 2 plans that match the landing page
        $plans = [
            [
                'id' => 1,
                'name' => 'trial',
                'display_name' => 'Free Trial',
                'description' => '30 days free trial to get started with basic features',
                'price_monthly' => 0.00,
                'price_yearly' => 0.00,
                'currency' => 'INR',
                'billing_cycle' => 'monthly',
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 2,
                'name' => 'professional',
                'display_name' => 'Professional',
                'description' => 'Full-featured plan for schools with unlimited access',
                'price_monthly' => 2999.00,
                'price_yearly' => 29990.00, // 10 months price for yearly
                'currency' => 'INR',
                'billing_cycle' => 'monthly',
                'is_active' => true,
                'is_popular' => true,
                'sort_order' => 2,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('plans')->insertBatch($plans);

        // Insert plan features that match the landing page
        $planFeatures = [
            // Trial Plan Features
            [
                'plan_id' => 1,
                'feature_key' => 'max_questions',
                'feature_name' => 'Maximum Questions',
                'feature_value' => '50',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 1,
                'feature_key' => 'max_subjects',
                'feature_name' => 'Maximum Subjects',
                'feature_value' => '5',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 1,
                'feature_key' => 'basic_user_roles',
                'feature_name' => 'Basic User Roles',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 1,
                'feature_key' => 'email_support',
                'feature_name' => 'Email Support',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 1,
                'feature_key' => 'trial_days',
                'feature_name' => 'Trial Period Days',
                'feature_value' => '30',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 1,
                'feature_key' => 'max_staff',
                'feature_name' => 'Maximum Staff Members',
                'feature_value' => '10',
                'value_type' => 'integer',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],

            // Professional Plan Features
            [
                'plan_id' => 2,
                'feature_key' => 'max_questions',
                'feature_name' => 'Unlimited Questions',
                'feature_value' => '0',
                'value_type' => 'integer',
                'is_unlimited' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'max_subjects',
                'feature_name' => 'Unlimited Subjects',
                'feature_value' => '0',
                'value_type' => 'integer',
                'is_unlimited' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'full_role_management',
                'feature_name' => 'Full Role Management',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'audit_logging',
                'feature_name' => 'Audit Logging',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'priority_support',
                'feature_name' => 'Priority Support',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'max_staff',
                'feature_name' => 'Unlimited Staff Members',
                'feature_value' => '0',
                'value_type' => 'integer',
                'is_unlimited' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'export_papers',
                'feature_name' => 'Export Question Papers',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'bulk_import',
                'feature_name' => 'Bulk Import Questions',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ],
            [
                'plan_id' => 2,
                'feature_key' => 'analytics',
                'feature_name' => 'Advanced Analytics',
                'feature_value' => 'true',
                'value_type' => 'boolean',
                'is_unlimited' => false,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]
        ];

        $this->db->table('plan_features')->insertBatch($planFeatures);
    }

    public function down()
    {
        // Clear the tables
        $this->db->table('plan_features')->truncate();
        $this->db->table('plans')->truncate();
    }
}
