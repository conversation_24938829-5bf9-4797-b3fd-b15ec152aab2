<!-- Staff Management Section -->
<div id="staff-section" class="animate-fade-in">
    <!-- Staff Management Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h3 class="text-xl font-bold text-gray-800">Staff Management</h3>
                    <p class="text-sm text-gray-600 mt-1">Manage teaching staff, assignments, and performance</p>
                </div>
                <div class="mt-4 sm:mt-0 flex space-x-3">
                    <button onclick="showUserModal()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-user-plus mr-2"></i>Add Staff
                    </button>
                    <button onclick="showBulkImportModal()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-upload mr-2"></i>Bulk Import
                    </button>
                </div>
            </div>
        </div>

        <!-- Staff Statistics -->
        <div class="px-6 py-4 bg-gray-50">
            <div class="grid grid-cols-1 sm:grid-cols-5 gap-4">
                <div class="text-center">
                    <p id="total-staff-count" class="text-2xl font-bold text-blue-600">0</p>
                    <p class="text-sm text-gray-600">Total Staff</p>
                </div>
                <div class="text-center">
                    <p id="active-staff-count" class="text-2xl font-bold text-green-600">0</p>
                    <p class="text-sm text-gray-600">Active</p>
                </div>
                <div class="text-center">
                    <p id="inactive-staff-count" class="text-2xl font-bold text-yellow-600">0</p>
                    <p class="text-sm text-gray-600">Inactive</p>
                </div>
                <div class="text-center">
                    <p id="teacher-count" class="text-2xl font-bold text-purple-600">0</p>
                    <p class="text-sm text-gray-600">Teachers</p>
                </div>
                <div class="text-center">
                    <p id="admin-staff-count" class="text-2xl font-bold text-orange-600">0</p>
                    <p class="text-sm text-gray-600">Admin Staff</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Staff Filters and Search -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Staff</label>
                <input type="text" id="staff-search" placeholder="Search by name, email..." 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                       onkeyup="filterStaff()">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <select id="department-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        onchange="filterStaff()">
                    <option value="">All Departments</option>
                    <option value="Mathematics">Mathematics</option>
                    <option value="Science">Science</option>
                    <option value="English">English</option>
                    <option value="Tamil">Tamil</option>
                    <option value="Social Science">Social Science</option>
                    <option value="Administration">Administration</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Designation</label>
                <select id="designation-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        onchange="filterStaff()">
                    <option value="">All Designations</option>
                    <option value="Principal">Principal</option>
                    <option value="Vice Principal">Vice Principal</option>
                    <option value="HOD">HOD</option>
                    <option value="Teacher">Teacher</option>
                    <option value="Assistant Teacher">Assistant Teacher</option>
                    <option value="Admin Staff">Admin Staff</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="status-filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        onchange="filterStaff()">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="on-leave">On Leave</option>
                    <option value="suspended">Suspended</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Actions</label>
                <div class="flex space-x-2">
                    <button onclick="clearStaffFilters()" class="flex-1 px-3 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-times mr-1"></i>Clear
                    </button>
                    <button onclick="exportStaff()" class="flex-1 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Staff Cards/Table Toggle -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h4 class="text-lg font-semibold text-gray-800">Staff Directory</h4>
                <div class="flex items-center space-x-2">
                    <button id="card-view-btn" onclick="toggleStaffView('cards')" class="px-3 py-2 bg-indigo-100 text-indigo-700 rounded-lg transition-colors">
                        <i class="fas fa-th-large mr-1"></i>Cards
                    </button>
                    <button id="table-view-btn" onclick="toggleStaffView('table')" class="px-3 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                        <i class="fas fa-list mr-1"></i>Table
                    </button>
                </div>
            </div>
        </div>

        <!-- Staff Cards View -->
        <div id="staff-cards-view" class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="staff-cards-container">
                <!-- Staff cards will be loaded dynamically -->
                <div class="col-span-full text-center py-12">
                    <i class="fas fa-spinner fa-spin text-3xl text-indigo-500 mb-4"></i>
                    <p class="text-lg font-medium text-gray-700">Loading staff...</p>
                    <p class="text-sm text-gray-500">Please wait while we fetch staff data</p>
                </div>
            </div>
        </div>

        <!-- Staff Table View (Hidden by default) -->
        <div id="staff-table-view" class="hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">
                                <input type="checkbox" id="select-all-staff" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                            </th>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">Staff Member</th>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">Designation</th>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">Department</th>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">Contact</th>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">Performance</th>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">Status</th>
                            <th class="text-left py-4 px-6 font-semibold text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="staff-table-body" class="divide-y divide-gray-200">
                        <!-- Table rows will be loaded dynamically -->
                        <tr>
                            <td colspan="8" class="text-center py-12">
                                <i class="fas fa-spinner fa-spin text-3xl text-indigo-500 mb-4"></i>
                                <p class="text-lg font-medium text-gray-700">Loading staff...</p>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- User Modal (for adding new staff) -->
<div id="userModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">Add New Staff Member</h3>
            <button class="text-gray-500 hover:text-gray-700" onclick="hideUserModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6">
            <form id="addUserForm" onsubmit="submitUserForm(event)">
                <div class="space-y-4">
                    <div>
                        <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                        <input type="text" id="fullName" name="name" required 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="John Doe">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                        <input type="email" id="email" name="email" required 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password *</label>
                        <input type="password" id="password" name="password" required 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="••••••••">
                    </div>
                    <div>
                        <label for="designation" class="block text-sm font-medium text-gray-700 mb-1">Designation *</label>
                        <select id="designation" name="designation" required
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                            <option value="">Select Designation</option>
                            <option value="Principal">Principal</option>
                            <option value="Vice Principal">Vice Principal</option>
                            <option value="HOD">HOD</option>
                            <option value="Teacher">Teacher</option>
                            <option value="Assistant Teacher">Assistant Teacher</option>
                            <option value="Admin Staff">Admin Staff</option>
                        </select>
                    </div>
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" id="phone" name="phone" 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                               placeholder="+91 98765 43210">
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" onclick="hideUserModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        Add Staff Member
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Import Modal -->
<div id="bulkImportModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 hidden">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-800">Bulk Import Staff</h3>
            <button class="text-gray-500 hover:text-gray-700" onclick="hideBulkImportModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-6">
            <div class="mb-4">
                <p class="text-sm text-gray-600">Upload a CSV file with staff details. The file should have the following columns: Name, Email, Designation, Phone.</p>
            </div>
            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <i class="fas fa-file-csv text-3xl text-gray-400 mb-2"></i>
                <p class="text-sm text-gray-600 mb-4">Drag and drop your CSV file here, or click to browse</p>
                <input type="file" id="csv-file" accept=".csv" class="hidden">
                <button onclick="document.getElementById('csv-file').click()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                    Browse Files
                </button>
            </div>
            <div class="mt-6 flex justify-end space-x-3">
                <button onclick="hideBulkImportModal()" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button onclick="processBulkImport()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                    Import Staff
                </button>
            </div>
        </div>
    </div>
</div>


