<?php

namespace App\Models;

use CodeIgniter\Model;

class SchoolSettingsModel extends Model
{
    protected $table = 'school_settings';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id',
        'setting_key',
        'setting_value',
        'setting_type',
        'category',
        'description'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    protected array $casts = [];
    protected array $castHandlers = [];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'setting_key' => 'required|max_length[255]',
        'setting_type' => 'required|in_list[string,integer,boolean,json]',
        'category' => 'required|max_length[100]'
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get all settings for a school organized by category
     */
    public function getSchoolSettings($schoolId)
    {
        $settings = $this->where('school_id', $schoolId)->findAll();
        
        $organized = [];
        foreach ($settings as $setting) {
            $value = $this->castValue($setting['setting_value'], $setting['setting_type']);
            $organized[$setting['category']][$setting['setting_key']] = $value;
        }
        
        return $organized;
    }

    /**
     * Get a specific setting value
     */
    public function getSetting($schoolId, $key, $default = null)
    {
        $setting = $this->where('school_id', $schoolId)
                        ->where('setting_key', $key)
                        ->first();
        
        if (!$setting) {
            return $default;
        }
        
        return $this->castValue($setting['setting_value'], $setting['setting_type']);
    }

    /**
     * Set a setting value
     */
    public function setSetting($schoolId, $key, $value, $type = 'string', $category = 'general')
    {
        $stringValue = $this->valueToString($value, $type);
        
        $data = [
            'school_id' => $schoolId,
            'setting_key' => $key,
            'setting_value' => $stringValue,
            'setting_type' => $type,
            'category' => $category
        ];

        // Check if setting exists
        $existing = $this->where('school_id', $schoolId)
                         ->where('setting_key', $key)
                         ->first();

        if ($existing) {
            return $this->update($existing['id'], $data);
        } else {
            return $this->insert($data);
        }
    }

    /**
     * Update multiple settings at once
     */
    public function updateSettings($schoolId, $settings)
    {
        $this->db->transStart();
        
        foreach ($settings as $category => $categorySettings) {
            foreach ($categorySettings as $key => $value) {
                // Determine type based on value
                $type = $this->determineType($value);
                $this->setSetting($schoolId, $key, $value, $type, $category);
            }
        }
        
        $this->db->transComplete();
        
        return $this->db->transStatus();
    }

    /**
     * Cast string value to appropriate type
     */
    private function castValue($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'json':
                return json_decode($value, true);
            case 'string':
            default:
                return $value;
        }
    }

    /**
     * Convert value to string for storage
     */
    private function valueToString($value, $type)
    {
        switch ($type) {
            case 'boolean':
                return $value ? 'true' : 'false';
            case 'integer':
                return (string) $value;
            case 'json':
                return json_encode($value);
            case 'string':
            default:
                return (string) $value;
        }
    }

    /**
     * Determine type from value
     */
    private function determineType($value)
    {
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_array($value) || is_object($value)) {
            return 'json';
        } else {
            return 'string';
        }
    }

    /**
     * Get settings by category
     */
    public function getSettingsByCategory($schoolId, $category)
    {
        $settings = $this->where('school_id', $schoolId)
                         ->where('category', $category)
                         ->findAll();
        
        $result = [];
        foreach ($settings as $setting) {
            $key = str_replace($category . '_', '', $setting['setting_key']);
            $result[$key] = $this->castValue($setting['setting_value'], $setting['setting_type']);
        }
        
        return $result;
    }

    /**
     * Reset settings to default for a school
     */
    public function resetToDefaults($schoolId)
    {
        // This would typically reload default settings
        // For now, we'll just delete all settings and let them be recreated
        return $this->where('school_id', $schoolId)->delete();
    }

    /**
     * Create default settings for a new school
     */
    public function createDefaultSettings($schoolId)
    {
        $defaultSettings = [
            // Question Bank Settings
            ['setting_key' => 'question_require_approval', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'question_bank'],
            ['setting_key' => 'question_auto_approve_senior', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'question_bank'],
            ['setting_key' => 'question_notification_enabled', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'question_bank'],
            ['setting_key' => 'question_auto_approval_days', 'setting_value' => '7', 'setting_type' => 'integer', 'category' => 'question_bank'],
            
            // User Management Settings
            ['setting_key' => 'user_registration_method', 'setting_value' => 'admin-only', 'setting_type' => 'string', 'category' => 'user_management'],
            ['setting_key' => 'user_email_verification', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'user_management'],
            ['setting_key' => 'user_self_profile_update', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'user_management'],
            ['setting_key' => 'user_default_role', 'setting_value' => 'teacher', 'setting_type' => 'string', 'category' => 'user_management'],
            
            // System Settings
            ['setting_key' => 'system_timezone', 'setting_value' => 'Asia/Kolkata', 'setting_type' => 'string', 'category' => 'system'],
            ['setting_key' => 'system_date_format', 'setting_value' => 'DD/MM/YYYY', 'setting_type' => 'string', 'category' => 'system'],
            ['setting_key' => 'system_language', 'setting_value' => 'en', 'setting_type' => 'string', 'category' => 'system'],
            ['setting_key' => 'system_maintenance_mode', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'system'],
        ];

        $settingsToInsert = [];
        foreach ($defaultSettings as $setting) {
            $settingsToInsert[] = array_merge($setting, [
                'school_id' => $schoolId,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        return $this->insertBatch($settingsToInsert);
    }
}
