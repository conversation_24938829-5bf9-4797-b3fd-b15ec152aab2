<?php

namespace App\Models;

use CodeIgniter\Model;

class QuestionPaperModel extends Model
{
    protected $table = 'question_papers';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'school_id', 'title', 'standard', 'subject', 'duration', 'total_marks',
        'status', 'academic_year', 'exam_type', 'exam_date', 'instructions', 'created_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'school_id' => 'required|integer',
        'title' => 'required|max_length[255]',
        'standard' => 'required|max_length[10]',
        'subject' => 'required|max_length[100]',
        'duration' => 'permit_empty|decimal',
        'total_marks' => 'permit_empty|integer',
        'status' => 'in_list[draft,published,archived]',
        'created_by' => 'required|integer'
    ];

    protected $validationMessages = [
        'title' => [
            'required' => 'Question paper title is required'
        ],
        'standard' => [
            'required' => 'Standard/Class is required'
        ],
        'subject' => [
            'required' => 'Subject is required'
        ]
    ];

    /**
     * Get question papers by school with pagination
     */
    public function getQuestionPapersBySchool($schoolId, $limit = 10, $offset = 0, $filters = [])
    {
        $builder = $this->select('
            question_papers.*,
            users.name as created_by_name,
            COUNT(paper_questions.id) as question_count
        ')
        ->join('users', 'users.id = question_papers.created_by', 'left')
        ->join('paper_questions', 'paper_questions.paper_id = question_papers.id', 'left')
        ->where('question_papers.school_id', $schoolId)
        ->groupBy('question_papers.id');

        // Apply filters
        if (!empty($filters['status'])) {
            $builder->where('question_papers.status', $filters['status']);
        }

        if (!empty($filters['standard'])) {
            $builder->where('question_papers.standard', $filters['standard']);
        }

        if (!empty($filters['subject'])) {
            $builder->where('question_papers.subject', $filters['subject']);
        }

        if (!empty($filters['search'])) {
            $builder->groupStart()
                   ->like('question_papers.title', $filters['search'])
                   ->orLike('question_papers.subject', $filters['search'])
                   ->groupEnd();
        }

        // Get total count
        $total = $builder->countAllResults(false);

        // Get paginated results
        $papers = $builder->orderBy('question_papers.created_at', 'DESC')
                         ->limit($limit, $offset)
                         ->findAll();

        return [
            'papers' => $papers,
            'total' => $total,
            'limit' => $limit,
            'offset' => $offset
        ];
    }

    /**
     * Get question paper with questions
     */
    public function getQuestionPaperWithQuestions($paperId, $schoolId)
    {
        // Get paper details
        $paper = $this->select('
            question_papers.*,
            users.name as created_by_name
        ')
        ->join('users', 'users.id = question_papers.created_by', 'left')
        ->where('question_papers.id', $paperId)
        ->where('question_papers.school_id', $schoolId)
        ->first();

        if (!$paper) {
            return null;
        }

        // Get questions
        $db = \Config\Database::connect();
        $questions = $db->table('paper_questions pq')
                       ->select('
                           pq.*,
                           q.question_text,
                           q.question_type,
                           q.difficulty,
                           q.option_a,
                           q.option_b,
                           q.option_c,
                           q.option_d,
                           q.correct_answer,
                           q.answer,
                           q.chapter_name,
                           q.topic_name,
                           q.marks as original_marks
                       ')
                       ->join('questions q', 'q.id = pq.question_id', 'left')
                       ->where('pq.paper_id', $paperId)
                       ->orderBy('pq.question_order', 'ASC')
                       ->get()
                       ->getResultArray();

        $paper['questions'] = $questions;
        return $paper;
    }

    /**
     * Create question paper with questions
     */
    public function createQuestionPaper($paperData, $questions = [])
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Insert question paper
            $paperId = $this->insert($paperData);
            
            if (!$paperId) {
                throw new \Exception('Failed to create question paper');
            }

            // Insert questions if provided
            if (!empty($questions)) {
                $paperQuestionModel = new PaperQuestionModel();
                foreach ($questions as $index => $questionData) {
                    $paperQuestionData = [
                        'paper_id' => $paperId,
                        'question_id' => $questionData['question_id'],
                        'question_order' => $index + 1,
                        'marks' => $questionData['marks'] ?? 1,
                        'section_name' => $questionData['section_name'] ?? 'Part I'
                    ];
                    
                    $paperQuestionModel->insert($paperQuestionData);
                }
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            return $paperId;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * Update question paper
     */
    public function updateQuestionPaper($paperId, $paperData, $questions = null)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Update paper data
            $this->update($paperId, $paperData);

            // Update questions if provided
            if ($questions !== null) {
                // Delete existing questions
                $db->table('paper_questions')->where('paper_id', $paperId)->delete();

                // Insert new questions
                $paperQuestionModel = new PaperQuestionModel();
                foreach ($questions as $index => $questionData) {
                    $paperQuestionData = [
                        'paper_id' => $paperId,
                        'question_id' => $questionData['question_id'],
                        'question_order' => $index + 1,
                        'marks' => $questionData['marks'] ?? 1,
                        'section_name' => $questionData['section_name'] ?? 'Part I'
                    ];
                    
                    $paperQuestionModel->insert($paperQuestionData);
                }
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            return true;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * Delete question paper
     */
    public function deleteQuestionPaper($paperId, $schoolId)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Verify ownership
            $paper = $this->where('id', $paperId)
                         ->where('school_id', $schoolId)
                         ->first();

            if (!$paper) {
                throw new \Exception('Question paper not found');
            }

            // Delete paper questions
            $db->table('paper_questions')->where('paper_id', $paperId)->delete();

            // Delete question paper
            $this->delete($paperId);

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Transaction failed');
            }

            return true;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * Get question paper statistics for school
     */
    public function getSchoolStatistics($schoolId)
    {
        $db = \Config\Database::connect();
        
        $stats = [
            'total_papers' => $this->where('school_id', $schoolId)->countAllResults(),
            'draft_papers' => $this->where('school_id', $schoolId)->where('status', 'draft')->countAllResults(),
            'published_papers' => $this->where('school_id', $schoolId)->where('status', 'published')->countAllResults(),
            'archived_papers' => $this->where('school_id', $schoolId)->where('status', 'archived')->countAllResults()
        ];

        // Get papers by subject
        $subjectStats = $db->table('question_papers')
                          ->select('subject, COUNT(*) as count')
                          ->where('school_id', $schoolId)
                          ->groupBy('subject')
                          ->orderBy('count', 'DESC')
                          ->get()
                          ->getResultArray();

        $stats['by_subject'] = $subjectStats;

        // Get recent papers
        $recentPapers = $this->select('id, title, subject, standard, status, created_at')
                            ->where('school_id', $schoolId)
                            ->orderBy('created_at', 'DESC')
                            ->limit(5)
                            ->findAll();

        $stats['recent_papers'] = $recentPapers;

        return $stats;
    }

    /**
     * Duplicate question paper
     */
    public function duplicateQuestionPaper($paperId, $schoolId, $newTitle = null)
    {
        $originalPaper = $this->getQuestionPaperWithQuestions($paperId, $schoolId);
        
        if (!$originalPaper) {
            throw new \Exception('Original question paper not found');
        }

        // Prepare new paper data
        $newPaperData = [
            'school_id' => $schoolId,
            'title' => $newTitle ?? ($originalPaper['title'] . ' (Copy)'),
            'standard' => $originalPaper['standard'],
            'subject' => $originalPaper['subject'],
            'duration' => $originalPaper['duration'],
            'total_marks' => $originalPaper['total_marks'],
            'status' => 'draft',
            'academic_year' => $originalPaper['academic_year'],
            'exam_type' => $originalPaper['exam_type'],
            'exam_date' => null,
            'instructions' => $originalPaper['instructions'],
            'created_by' => session()->get('user_id')
        ];

        // Prepare questions data
        $questionsData = [];
        foreach ($originalPaper['questions'] as $question) {
            $questionsData[] = [
                'question_id' => $question['question_id'],
                'marks' => $question['marks'],
                'section_name' => $question['section_name']
            ];
        }

        return $this->createQuestionPaper($newPaperData, $questionsData);
    }
}
