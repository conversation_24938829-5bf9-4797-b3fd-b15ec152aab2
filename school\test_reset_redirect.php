<?php
echo "=== Testing Reset Password Redirect ===\n\n";

// Test the redirect URL
$testToken = 'b7d7d73b730eefd04f65b6bf3e49b44e591e24a49f3d023853a40fac2d9c5e73';
$resetUrl = "http://localhost/schoolquestionbank/school/public/forgot-password/reset?token=" . $testToken;

echo "1. Original reset URL: $resetUrl\n\n";

// Use cURL to follow redirects
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $resetUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_NOBODY, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
$redirectCount = curl_getinfo($ch, CURLINFO_REDIRECT_COUNT);

curl_close($ch);

echo "2. HTTP Response Code: $httpCode\n";
echo "3. Number of redirects: $redirectCount\n";
echo "4. Final URL: $finalUrl\n\n";

// Parse the final URL to check parameters
$parsedUrl = parse_url($finalUrl);
if (isset($parsedUrl['query'])) {
    parse_str($parsedUrl['query'], $params);
    echo "5. URL Parameters:\n";
    foreach ($params as $key => $value) {
        if ($key === 'reset_token') {
            echo "   - $key: " . substr($value, 0, 20) . "...\n";
        } else {
            echo "   - $key: $value\n";
        }
    }
} else {
    echo "5. No URL parameters found\n";
}

echo "\n=== Test Complete ===\n";

if ($httpCode === 200 && isset($params['reset_token'])) {
    echo "✅ Reset password redirect is working correctly!\n";
    echo "✅ Token parameter is present in the final URL\n";
    echo "✅ The landing page should automatically open the reset password modal\n";
} else {
    echo "❌ There might be an issue with the redirect\n";
}
?>
