<?php
/**
 * Test PDF Generation for Question Papers
 * Run this from the school directory: php test_pdf_generation.php
 */

require_once 'vendor/autoload.php';

echo "=== Testing PDF Generation for Question Papers ===\n\n";

try {
    // Test 1: Check if DomPDF is available
    echo "1. Testing DomPDF availability:\n";
    if (class_exists('\Dompdf\Dompdf')) {
        echo "   - DomPDF library found ✓\n";
    } else {
        echo "   - DomPDF library not found ❌\n";
        echo "   - Run: composer install\n";
        exit(1);
    }

    // Test 2: Test basic PDF generation
    echo "\n2. Testing basic PDF generation:\n";

    $options = new \Dompdf\Options();
    $options->set('defaultFont', 'Times');
    $options->set('isRemoteEnabled', true);
    $options->set('isHtml5ParserEnabled', true);

    $dompdf = new \Dompdf\Dompdf($options);
    echo "   - DomPDF instance created successfully ✓\n";

    // Test 3: Test HTML to PDF conversion
    echo "\n3. Testing HTML to PDF conversion:\n";
    
    // Create sample HTML content
    $html = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Sample Question Paper</title>
    <style>
        body { font-family: "Times New Roman", serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 20px; }
        .school-name { font-size: 20px; font-weight: bold; text-transform: uppercase; }
        .paper-title { font-size: 18px; font-weight: bold; margin: 15px 0; text-decoration: underline; }
        .question { margin: 15px 0; padding: 10px 0; border-bottom: 1px solid #ddd; }
        .question-number { font-weight: bold; }
        .options { margin: 10px 0 10px 20px; }
    </style>
</head>
<body>
    <div class="header">
        <div class="school-name">Test School</div>
        <div class="paper-title">Sample Mathematics Question Paper</div>
        <div>Academic Year: 2024-2025 | Duration: 3 Hours | Max Marks: 100</div>
    </div>

    <div class="question">
        <div class="question-number">1. What is the value of 2 + 2? (1 mark)</div>
        <div class="options">
            <div>A) 3</div>
            <div>B) 4</div>
            <div>C) 5</div>
            <div>D) 6</div>
        </div>
    </div>

    <div class="question">
        <div class="question-number">2. Solve the equation: x + 5 = 10 (2 marks)</div>
    </div>

    <div class="question">
        <div class="question-number">3. Explain quadratic equations with examples. (5 marks)</div>
    </div>
</body>
</html>';

    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();

    $pdfContent = $dompdf->output();

    if ($pdfContent && strlen($pdfContent) > 0) {
        echo "   - PDF generated successfully ✓\n";
        echo "   - Content size: " . strlen($pdfContent) . " bytes\n";

        // Save test PDF to file
        $testFilename = 'test_question_paper.pdf';
        file_put_contents($testFilename, $pdfContent);
        echo "   - Test PDF saved as: $testFilename ✓\n";
    } else {
        echo "   - PDF generation failed ❌\n";
        exit(1);
    }

    // Test 4: Test answer key generation with answers
    echo "\n4. Testing answer key generation:\n";

    $answerKeyHtml = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Sample Answer Key</title>
    <style>
        body { font-family: "Times New Roman", serif; margin: 20px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 20px; }
        .school-name { font-size: 20px; font-weight: bold; text-transform: uppercase; }
        .paper-title { font-size: 18px; font-weight: bold; margin: 15px 0; text-decoration: underline; }
        .question { margin: 15px 0; padding: 10px 0; border-bottom: 1px solid #ddd; }
        .question-number { font-weight: bold; }
        .answer-section { margin-top: 10px; padding: 10px; background: #f0f8ff; border: 1px solid #007cba; }
        .answer-label { font-weight: bold; color: #007cba; }
    </style>
</head>
<body>
    <div class="header">
        <div class="school-name">Test School</div>
        <div class="paper-title">Sample Mathematics Question Paper - Answer Key</div>
        <div>Academic Year: 2024-2025 | Duration: 3 Hours | Max Marks: 100</div>
    </div>

    <div class="question">
        <div class="question-number">1. What is the value of 2 + 2? (1 mark)</div>
        <div class="answer-section">
            <div class="answer-label">Answer:</div>
            <div>Correct Answer: B) 4</div>
        </div>
    </div>

    <div class="question">
        <div class="question-number">2. Solve the equation: x + 5 = 10 (2 marks)</div>
        <div class="answer-section">
            <div class="answer-label">Answer:</div>
            <div>x = 5</div>
        </div>
    </div>

    <div class="question">
        <div class="question-number">3. Explain quadratic equations with examples. (5 marks)</div>
        <div class="answer-section">
            <div class="answer-label">Answer:</div>
            <div>A quadratic equation is a polynomial equation of degree 2 in the form ax² + bx + c = 0...</div>
        </div>
    </div>
</body>
</html>';

    $answerDompdf = new \Dompdf\Dompdf($options);
    $answerDompdf->loadHtml($answerKeyHtml);
    $answerDompdf->setPaper('A4', 'portrait');
    $answerDompdf->render();

    $answerPdfContent = $answerDompdf->output();

    if ($answerPdfContent && strlen($answerPdfContent) > 0) {
        echo "   - Answer key PDF generated successfully ✓\n";
        echo "   - Content size: " . strlen($answerPdfContent) . " bytes\n";

        // Save test answer key PDF to file
        $testAnswerFilename = 'test_answer_key.pdf';
        file_put_contents($testAnswerFilename, $answerPdfContent);
        echo "   - Test answer key PDF saved as: $testAnswerFilename ✓\n";
    } else {
        echo "   - Answer key PDF generation failed ❌\n";
        exit(1);
    }

    echo "\n=== PDF Generation Test Results ===\n";
    echo "✅ All tests passed successfully!\n";
    echo "✅ PDF generation is working correctly\n";
    echo "✅ Answer key generation is working correctly\n";
    echo "\nGenerated test files:\n";
    echo "- test_question_paper.pdf\n";
    echo "- test_answer_key.pdf\n";
    echo "\nYou can now test the PDF generation through the web interface!\n";
    echo "\nNext steps:\n";
    echo "1. Open your browser and go to the school admin dashboard\n";
    echo "2. Create a question paper\n";
    echo "3. Try downloading it as PDF\n";
    echo "4. Try downloading the answer key\n";

} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
