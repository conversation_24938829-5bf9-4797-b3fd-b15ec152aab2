{"url": "http://localhost:8080/index.php/schooladmin/dashboard", "method": "GET", "isAJAX": false, "startTime": **********.315305, "totalTime": 895.7, "totalMemory": "8.878", "segmentDuration": 130, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.351993, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.578687, "duration": 0.*****************}, {"name": "Routing", "component": "Timer", "start": **********.60135, "duration": 0.013710975646972656}, {"name": "Before Filters", "component": "Timer", "start": **********.61633, "duration": 6.508827209472656e-05}, {"name": "Controller", "component": "Timer", "start": **********.616401, "duration": 0.****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.616404, "duration": 0.***************}, {"name": "After Filters", "component": "Timer", "start": **********.200085, "duration": 2.2172927856445312e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.200291, "duration": 0.*****************}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(13 total Queries, 12 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "3.17 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>AND</strong> `schools`.`id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:210", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:613", "function": "        CodeIgniter\\Model->doFind()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:77", "function": "        CodeIgniter\\BaseModel->find()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\SchoolAdmin.php:77", "qid": "4761b3a2fc54ea697007426189cc5370"}, {"hover": "", "class": "", "duration": "4.32 ms", "sql": "<strong>SELECT</strong> `subscriptions`.*, `plans`.`name` as `plan_name`, `plans`.`display_name` as `plan_display_name`\n<strong>FROM</strong> `subscriptions`\n<strong>JOIN</strong> `plans` <strong>ON</strong> `plans`.`id` = `subscriptions`.`plan_id`\n<strong>WHERE</strong> `subscriptions`.`school_id` = &#039;1&#039;\n<strong>AND</strong> `subscriptions`.`status` <strong>IN</strong> (&#039;active&#039;,&#039;trial&#039;)\n<strong>ORDER</strong> <strong>BY</strong> `subscriptions`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH\\Models\\SubscriptionModel.php:66", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:84", "function": "        App\\Models\\SubscriptionModel->getCurrentSubscription()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\SubscriptionModel.php:66", "qid": "c5bbbbaf303c1730ad1cf5dea59214c0"}, {"hover": "", "class": "", "duration": "8.21 ms", "sql": "<strong>SELECT</strong> `payment_logs`.*, `plans`.`display_name` as `plan_name`\n<strong>FROM</strong> `payment_logs`\n<strong>LEFT</strong> <strong>JOIN</strong> `subscriptions` <strong>ON</strong> `subscriptions`.`id` = `payment_logs`.`subscription_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `plans` <strong>ON</strong> `plans`.`id` = `subscriptions`.`plan_id`\n<strong>WHERE</strong> `payment_logs`.`school_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_logs`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\PaymentLogModel.php:106", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:85", "function": "        App\\Models\\PaymentLogModel->getRecentTransactions()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\PaymentLogModel.php:106", "qid": "3e2d5b2a4f0353ac50d01b4838756e5d"}, {"hover": "", "class": "", "duration": "1.1 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\QuestionModel.php:271", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:762", "function": "        App\\Models\\QuestionModel->getAdminStats()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:91", "function": "        App\\Controllers\\SchoolAdmin->getDashboardStats()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\QuestionModel.php:271", "qid": "299ad3c64fb993dbfb63a6981e64f193"}, {"hover": "", "class": "", "duration": "0.97 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;pending&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\QuestionModel.php:274", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:762", "function": "        App\\Models\\QuestionModel->getAdminStats()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:91", "function": "        App\\Controllers\\SchoolAdmin->getDashboardStats()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\QuestionModel.php:274", "qid": "cd126b476c45ecd0693e02d53d466643"}, {"hover": "", "class": "", "duration": "1.1 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;approved&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\QuestionModel.php:277", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:762", "function": "        App\\Models\\QuestionModel->getAdminStats()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:91", "function": "        App\\Controllers\\SchoolAdmin->getDashboardStats()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\QuestionModel.php:277", "qid": "62e4b8c18728fc2f6b9352c0ada8c6df"}, {"hover": "", "class": "", "duration": "5.11 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;rejected&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Models\\QuestionModel.php:280", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:762", "function": "        App\\Models\\QuestionModel->getAdminStats()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:91", "function": "        App\\Controllers\\SchoolAdmin->getDashboardStats()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Models\\QuestionModel.php:280", "qid": "464ba0ca01bc5a9e34b2b215c1ee1c5c"}, {"hover": "", "class": "", "duration": "2.72 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `is_deleted` = 0", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:769", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:91", "function": "        App\\Controllers\\SchoolAdmin->getDashboardStats()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\SchoolAdmin.php:769", "qid": "07824de7fcc471cf49ef6d87190dead5"}, {"hover": "", "class": "", "duration": "1.32 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> (\n<strong>SELECT</strong> `subject`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>GROUP</strong> <strong>BY</strong> `subject`\n) CI_count_all_results", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:778", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:91", "function": "        App\\Controllers\\SchoolAdmin->getDashboardStats()", "index": "  4    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 11    "}], "trace-file": "APPPATH\\Controllers\\SchoolAdmin.php:778", "qid": "7cbd2d5ec655398d9c44682d86717dfa"}, {"hover": "", "class": "", "duration": "2.33 ms", "sql": "<strong>SELECT</strong> `questions`.*, `users`.`name` as `staff_name`\n<strong>FROM</strong> `questions`\n<strong>LEFT</strong> <strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `questions`.`staff_id`\n<strong>WHERE</strong> `questions`.`school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`status` <strong>IN</strong> (&#039;approved&#039;,&#039;rejected&#039;,&#039;pending&#039;)\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>ORDER</strong> <strong>BY</strong> `questions`.`updated_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 20", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:813", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:92", "function": "        App\\Controllers\\SchoolAdmin->getRecentActivity()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Controllers\\SchoolAdmin.php:813", "qid": "85d1f49e2a04deed58c660c47450fc83"}, {"hover": "", "class": "", "duration": "3.61 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `is_deleted` = 0\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 3", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:855", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:92", "function": "        App\\Controllers\\SchoolAdmin->getRecentActivity()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Controllers\\SchoolAdmin.php:855", "qid": "3337bb39d2e6c140e17e529144ac4633"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "1.51 ms", "sql": "<strong>SELECT</strong> `payment_logs`.*, `plans`.`display_name` as `plan_name`\n<strong>FROM</strong> `payment_logs`\n<strong>LEFT</strong> <strong>JOIN</strong> `subscriptions` <strong>ON</strong> `subscriptions`.`id` = `payment_logs`.`subscription_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `plans` <strong>ON</strong> `plans`.`id` = `subscriptions`.`plan_id`\n<strong>WHERE</strong> `payment_logs`.`school_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_logs`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Models\\PaymentLogModel.php:106", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:871", "function": "        App\\Models\\PaymentLogModel->getRecentTransactions()", "index": "  5    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:92", "function": "        App\\Controllers\\SchoolAdmin->getRecentActivity()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  8    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 10    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 11    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 12    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 13    "}], "trace-file": "APPPATH\\Models\\PaymentLogModel.php:106", "qid": "22fb89bc45b336fc55af72f27645fa44"}, {"hover": "", "class": "", "duration": "1.57 ms", "sql": "<strong>SELECT</strong> `subject`, <strong>COUNT</strong>(*) as total_questions, <strong>SUM</strong>(CASE WHEN status = &quot;approved&quot; THEN 1 ELSE 0 END) as approved_questions, <strong>SUM</strong>(CASE WHEN status = &quot;pending&quot; THEN 1 ELSE 0 END) as pending_questions, <strong>SUM</strong>(CASE WHEN status = &quot;rejected&quot; THEN 1 ELSE 0 END) as rejected_questions\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>GROUP</strong> <strong>BY</strong> `subject`\n<strong>ORDER</strong> <strong>BY</strong> `total_questions` <strong>DESC</strong>\n <strong>LIMIT</strong> 6", "trace": [{"file": "SYSTEMPATH\\Database\\BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH\\Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH\\BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:930", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH\\Controllers\\SchoolAdmin.php:93", "function": "        App\\Controllers\\SchoolAdmin->getSubjectPerformance()", "index": "  5    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:933", "function": "        App\\Controllers\\SchoolAdmin->dashboard()", "index": "  6    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH\\CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH\\Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH\\Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH\\index.php:59", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}, {"file": "SYSTEMPATH\\rewrite.php:44", "args": ["C:\\xampp\\htdocs\\school\\public\\index.php"], "function": "        require_once()", "index": " 12    "}], "trace-file": "APPPATH\\Controllers\\SchoolAdmin.php:930", "qid": "170cc163c3e9fa5a14b80521199a52fe"}]}, "badgeValue": 13, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.059339, "duration": "0.004626"}, {"name": "Query", "component": "Database", "start": **********.068087, "duration": "0.003170", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `schools`\n<strong>WHERE</strong> `schools`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>AND</strong> `schools`.`id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.080812, "duration": "0.004323", "query": "<strong>SELECT</strong> `subscriptions`.*, `plans`.`name` as `plan_name`, `plans`.`display_name` as `plan_display_name`\n<strong>FROM</strong> `subscriptions`\n<strong>JOIN</strong> `plans` <strong>ON</strong> `plans`.`id` = `subscriptions`.`plan_id`\n<strong>WHERE</strong> `subscriptions`.`school_id` = &#039;1&#039;\n<strong>AND</strong> `subscriptions`.`status` <strong>IN</strong> (&#039;active&#039;,&#039;trial&#039;)\n<strong>ORDER</strong> <strong>BY</strong> `subscriptions`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.087596, "duration": "0.008211", "query": "<strong>SELECT</strong> `payment_logs`.*, `plans`.`display_name` as `plan_name`\n<strong>FROM</strong> `payment_logs`\n<strong>LEFT</strong> <strong>JOIN</strong> `subscriptions` <strong>ON</strong> `subscriptions`.`id` = `payment_logs`.`subscription_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `plans` <strong>ON</strong> `plans`.`id` = `subscriptions`.`plan_id`\n<strong>WHERE</strong> `payment_logs`.`school_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_logs`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5"}, {"name": "Query", "component": "Database", "start": **********.096612, "duration": "0.001100", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.098439, "duration": "0.000970", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;pending&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.100038, "duration": "0.001104", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;approved&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.101834, "duration": "0.005109", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;rejected&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.108548, "duration": "0.002722", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `status` = &#039;active&#039;\n<strong>AND</strong> `is_deleted` = 0"}, {"name": "Query", "component": "Database", "start": **********.112133, "duration": "0.001321", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> (\n<strong>SELECT</strong> `subject`\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>GROUP</strong> <strong>BY</strong> `subject`\n) CI_count_all_results"}, {"name": "Query", "component": "Database", "start": **********.114769, "duration": "0.002332", "query": "<strong>SELECT</strong> `questions`.*, `users`.`name` as `staff_name`\n<strong>FROM</strong> `questions`\n<strong>LEFT</strong> <strong>JOIN</strong> `users` <strong>ON</strong> `users`.`id` = `questions`.`staff_id`\n<strong>WHERE</strong> `questions`.`school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`status` <strong>IN</strong> (&#039;approved&#039;,&#039;rejected&#039;,&#039;pending&#039;)\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>ORDER</strong> <strong>BY</strong> `questions`.`updated_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 20"}, {"name": "Query", "component": "Database", "start": **********.118219, "duration": "0.003612", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `users`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `is_deleted` = 0\n<strong>ORDER</strong> <strong>BY</strong> `created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 3"}, {"name": "Query", "component": "Database", "start": **********.123372, "duration": "0.001513", "query": "<strong>SELECT</strong> `payment_logs`.*, `plans`.`display_name` as `plan_name`\n<strong>FROM</strong> `payment_logs`\n<strong>LEFT</strong> <strong>JOIN</strong> `subscriptions` <strong>ON</strong> `subscriptions`.`id` = `payment_logs`.`subscription_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `plans` <strong>ON</strong> `plans`.`id` = `subscriptions`.`plan_id`\n<strong>WHERE</strong> `payment_logs`.`school_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_logs`.`created_at` <strong>DESC</strong>\n <strong>LIMIT</strong> 5"}, {"name": "Query", "component": "Database", "start": **********.129116, "duration": "0.001568", "query": "<strong>SELECT</strong> `subject`, <strong>COUNT</strong>(*) as total_questions, <strong>SUM</strong>(CASE WHEN status = &quot;approved&quot; THEN 1 ELSE 0 END) as approved_questions, <strong>SUM</strong>(CASE WHEN status = &quot;pending&quot; THEN 1 ELSE 0 END) as pending_questions, <strong>SUM</strong>(CASE WHEN status = &quot;rejected&quot; THEN 1 ELSE 0 END) as rejected_questions\n<strong>FROM</strong> `questions`\n<strong>WHERE</strong> `school_id` = &#039;1&#039;\n<strong>AND</strong> `questions`.`deleted_at` <strong>IS</strong> <strong>NULL</strong>\n<strong>GROUP</strong> <strong>BY</strong> `subject`\n<strong>ORDER</strong> <strong>BY</strong> `total_questions` <strong>DESC</strong>\n <strong>LIMIT</strong> 6"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": [{"level": "debug", "msg": "Session: Class initialized using 'CodeIgniter\\Session\\Handlers\\FileHandler' driver."}, {"level": "warning", "msg": "[DEPRECATED] Creation of dynamic property App\\Controllers\\SchoolAdmin::$session is deprecated in APPPATH\\Controllers\\BaseController.php on line 37.\n 1 SYSTEMPATH\\CodeIgniter.php(904): App\\Controllers\\BaseController->initController(Object(CodeIgniter\\HTTP\\IncomingRequest), Object(CodeIgniter\\HTTP\\Response), Object(CodeIgniter\\Log\\Logger))\n 2 SYSTEMPATH\\CodeIgniter.php(498): CodeIgniter\\CodeIgniter->createController()\n 3 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 4 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 5 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 6 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n 7 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\school\\\\public\\\\index.php')"}, {"level": "warning", "msg": "[DEPRECATED] Creation of dynamic property CodeIgniter\\HTTP\\IncomingRequest::$school_id is deprecated in APPPATH\\Controllers\\BaseController.php on line 41.\n 1 SYSTEMPATH\\CodeIgniter.php(904): App\\Controllers\\BaseController->initController(Object(CodeIgniter\\HTTP\\IncomingRequest), Object(CodeIgniter\\HTTP\\Response), Object(CodeIgniter\\Log\\Logger))\n 2 SYSTEMPATH\\CodeIgniter.php(498): CodeIgniter\\CodeIgniter->createController()\n 3 SYSTEMPATH\\CodeIgniter.php(354): CodeIgniter\\CodeIgniter->handleRequest(null, Object(Config\\Cache), false)\n 4 SYSTEMPATH\\Boot.php(334): CodeIgniter\\CodeIgniter->run()\n 5 SYSTEMPATH\\Boot.php(67): CodeIgniter\\Boot::runCodeIgniter(Object(CodeIgniter\\CodeIgniter))\n 6 FCPATH\\index.php(59): CodeIgniter\\Boot::bootWeb(Object(Config\\Paths))\n 7 SYSTEMPATH\\rewrite.php(44): require_once('C:\\\\xampp\\\\htdocs\\\\school\\\\public\\\\index.php')"}, {"level": "debug", "msg": "Dashboard: School ID = 1, Found 0 transactions"}]}, "badgeValue": null, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 1, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: schooladmin/dashboard.php", "component": "Views", "start": **********.159611, "duration": 0.03900790214538574}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 182 )", "display": {"coreFiles": [{"path": "SYSTEMPATH\\API\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\Autoloader\\Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH\\Autoloader\\FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH\\BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH\\Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH\\Cache\\CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH\\Cache\\CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Cache\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Cache\\ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH\\CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH\\Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH\\Config\\AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH\\Config\\BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH\\Config\\DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH\\Config\\Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH\\Config\\Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH\\Config\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH\\Cookie\\CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH\\Cookie\\CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH\\Database\\BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH\\Database\\BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH\\Database\\BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH\\Database\\Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH\\Database\\ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH\\Database\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH\\Database\\MySQLi\\Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH\\Database\\Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH\\Database\\QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH\\Database\\ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH\\Debug\\Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH\\Debug\\Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH\\Debug\\Toolbar\\Collectors\\Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH\\Email\\Email.php", "name": "Email.php"}, {"path": "SYSTEMPATH\\Events\\Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH\\Filters\\DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH\\Filters\\FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH\\Filters\\Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH\\Filters\\ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH\\Filters\\PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH\\Filters\\PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH\\HTTP\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH\\HTTP\\Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH\\HTTP\\IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH\\HTTP\\OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH\\HTTP\\ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH\\HTTP\\SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH\\HTTP\\URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH\\HTTP\\UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH\\Helpers\\array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\form_helper.php", "name": "form_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH\\Helpers\\url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH\\I18n\\Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH\\I18n\\TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Log\\Handlers\\HandlerInterface.php", "name": "HandlerInterface.php"}, {"path": "SYSTEMPATH\\Log\\Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH\\Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH\\Modules\\Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH\\Router\\RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH\\Router\\Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH\\Router\\RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH\\Security\\Security.php", "name": "Security.php"}, {"path": "SYSTEMPATH\\Security\\SecurityInterface.php", "name": "SecurityInterface.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH\\Session\\Handlers\\FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH\\Session\\Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH\\Session\\SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH\\Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Renderer\\TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init.php", "name": "init.php"}, {"path": "SYSTEMPATH\\ThirdParty\\Kint\\init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH\\Traits\\ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH\\Validation\\FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH\\View\\RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH\\View\\View.php", "name": "View.php"}, {"path": "SYSTEMPATH\\View\\ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}, {"path": "SYSTEMPATH\\rewrite.php", "name": "rewrite.php"}], "userFiles": [{"path": "APPPATH\\Common.php", "name": "Common.php"}, {"path": "APPPATH\\Config\\App.php", "name": "App.php"}, {"path": "APPPATH\\Config\\Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH\\Config\\Boot\\development.php", "name": "development.php"}, {"path": "APPPATH\\Config\\Cache.php", "name": "Cache.php"}, {"path": "APPPATH\\Config\\Constants.php", "name": "Constants.php"}, {"path": "APPPATH\\Config\\ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH\\Config\\Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH\\Config\\Database.php", "name": "Database.php"}, {"path": "APPPATH\\Config\\Email.php", "name": "Email.php"}, {"path": "APPPATH\\Config\\Events.php", "name": "Events.php"}, {"path": "APPPATH\\Config\\Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH\\Config\\Feature.php", "name": "Feature.php"}, {"path": "APPPATH\\Config\\Filters.php", "name": "Filters.php"}, {"path": "APPPATH\\Config\\Kint.php", "name": "Kint.php"}, {"path": "APPPATH\\Config\\Logger.php", "name": "Logger.php"}, {"path": "APPPATH\\Config\\Modules.php", "name": "Modules.php"}, {"path": "APPPATH\\Config\\Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH\\Config\\Paths.php", "name": "Paths.php"}, {"path": "APPPATH\\Config\\Routes.php", "name": "Routes.php"}, {"path": "APPPATH\\Config\\Routing.php", "name": "Routing.php"}, {"path": "APPPATH\\Config\\Security.php", "name": "Security.php"}, {"path": "APPPATH\\Config\\Services.php", "name": "Services.php"}, {"path": "APPPATH\\Config\\Session.php", "name": "Session.php"}, {"path": "APPPATH\\Config\\Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH\\Config\\UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH\\Config\\View.php", "name": "View.php"}, {"path": "APPPATH\\Controllers\\BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH\\Controllers\\SchoolAdmin.php", "name": "SchoolAdmin.php"}, {"path": "APPPATH\\Models\\EmailLogModel.php", "name": "EmailLogModel.php"}, {"path": "APPPATH\\Models\\PaperQuestionModel.php", "name": "PaperQuestionModel.php"}, {"path": "APPPATH\\Models\\PaymentLogModel.php", "name": "PaymentLogModel.php"}, {"path": "APPPATH\\Models\\QuestionModel.php", "name": "QuestionModel.php"}, {"path": "APPPATH\\Models\\QuestionPaperModel.php", "name": "QuestionPaperModel.php"}, {"path": "APPPATH\\Models\\SchoolModel.php", "name": "SchoolModel.php"}, {"path": "APPPATH\\Models\\SchoolSettingsModel.php", "name": "SchoolSettingsModel.php"}, {"path": "APPPATH\\Models\\SubscriptionModel.php", "name": "SubscriptionModel.php"}, {"path": "APPPATH\\Models\\UserModel.php", "name": "UserModel.php"}, {"path": "APPPATH\\Models\\UserProfileModel.php", "name": "UserProfileModel.php"}, {"path": "APPPATH\\Models\\UserRoleModel.php", "name": "UserRoleModel.php"}, {"path": "APPPATH\\Services\\EmailService.php", "name": "EmailService.php"}, {"path": "APPPATH\\Services\\PDFService.php", "name": "PDFService.php"}, {"path": "APPPATH\\Views\\schooladmin\\dashboard.php", "name": "dashboard.php"}, {"path": "FCPATH\\index.php", "name": "index.php"}, {"path": "VENDORPATH\\autoload.php", "name": "autoload.php"}, {"path": "VENDORPATH\\composer\\ClassLoader.php", "name": "ClassLoader.php"}, {"path": "VENDORPATH\\composer\\InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "VENDORPATH\\composer\\autoload_real.php", "name": "autoload_real.php"}, {"path": "VENDORPATH\\composer\\autoload_static.php", "name": "autoload_static.php"}, {"path": "VENDORPATH\\composer\\installed.php", "name": "installed.php"}, {"path": "VENDORPATH\\composer\\platform_check.php", "name": "platform_check.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\Escaper.php", "name": "Escaper.php"}, {"path": "VENDORPATH\\laminas\\laminas-escaper\\src\\EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "VENDORPATH\\myclabs\\deep-copy\\src\\DeepCopy\\deep_copy.php", "name": "deep_copy.php"}, {"path": "VENDORPATH\\phpunit\\phpunit\\src\\Framework\\Assert\\Functions.php", "name": "Functions.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LogLevel.php", "name": "LogLevel.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "VENDORPATH\\psr\\log\\src\\LoggerInterface.php", "name": "LoggerInterface.php"}, {"path": "VENDORPATH\\razorpay\\razorpay\\Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH\\rmccue\\requests\\library\\Deprecated.php", "name": "Deprecated.php"}, {"path": "VENDORPATH\\rmccue\\requests\\src\\Autoload.php", "name": "Autoload.php"}, {"path": "VENDORPATH\\symfony\\deprecation-contracts\\function.php", "name": "function.php"}]}, "badgeValue": 182, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\SchoolAdmin", "method": "dashboard", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Landing::index"}, {"method": "GET", "route": "register", "handler": "\\App\\Controllers\\Landing::register"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::schooladminLogin"}, {"method": "GET", "route": "login/superadmin", "handler": "\\App\\Controllers\\Auth::superadminLogin"}, {"method": "GET", "route": "login/schooladmin", "handler": "\\App\\Controllers\\Auth::schooladminLogin"}, {"method": "GET", "route": "logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "forgot-password/reset", "handler": "\\App\\Controllers\\ForgotPassword::resetFormFromUrl"}, {"method": "GET", "route": "forgot-password/([^/]+)", "handler": "\\App\\Controllers\\ForgotPassword::index/$1"}, {"method": "GET", "route": "forgot-password", "handler": "\\App\\Controllers\\ForgotPassword::index"}, {"method": "GET", "route": "auth/reset-password/([^/]+)", "handler": "\\App\\Controllers\\ForgotPassword::resetForm/$1"}, {"method": "GET", "route": "staff/reset-password/([^/]+)", "handler": "\\App\\Controllers\\ForgotPassword::resetForm/$1"}, {"method": "GET", "route": "superadmin/dashboard", "handler": "\\App\\Controllers\\SuperAdmin::dashboard"}, {"method": "GET", "route": "schooladmin/dashboard", "handler": "\\App\\Controllers\\SchoolAdmin::dashboard"}, {"method": "GET", "route": "schooladmin/question-papers", "handler": "\\App\\Controllers\\SchoolAdmin::questionPapers"}, {"method": "GET", "route": "subscription/plans", "handler": "\\App\\Controllers\\Subscription::plans"}, {"method": "GET", "route": "subscription/current", "handler": "\\App\\Controllers\\Subscription::current"}, {"method": "GET", "route": "subscription/upgrade", "handler": "\\App\\Controllers\\Subscription::upgrade"}, {"method": "GET", "route": "subscription/usage", "handler": "\\App\\Controllers\\Subscription::getUsage"}, {"method": "GET", "route": "payment/process", "handler": "\\App\\Controllers\\Payment::process"}, {"method": "GET", "route": "payment/success", "handler": "\\App\\Controllers\\Payment::success"}, {"method": "GET", "route": "payment/failure", "handler": "\\App\\Controllers\\Payment::failure"}, {"method": "GET", "route": "payment/history", "handler": "\\App\\Controllers\\Payment::history"}, {"method": "GET", "route": "payment/transaction-details/([0-9]+)", "handler": "\\App\\Controllers\\Payment::transactionDetails/$1"}, {"method": "GET", "route": "payment/invoice/([0-9]+)", "handler": "\\App\\Controllers\\Payment::invoice/$1"}, {"method": "GET", "route": "payment/test-webhook/([^/]+)", "handler": "\\App\\Controllers\\Payment::testWebhook/$1"}, {"method": "GET", "route": "payment/debugCompletePending", "handler": "\\App\\Controllers\\Payment::debugCompletePending"}, {"method": "GET", "route": "payment/debugSession", "handler": "\\App\\Controllers\\Payment::debugSession"}, {"method": "GET", "route": "superadmin/school/([0-9]+)/details", "handler": "\\App\\Controllers\\SuperAdmin::viewSchoolDetails/$1"}, {"method": "GET", "route": "superadmin/getUsers", "handler": "\\App\\Controllers\\SuperAdmin::getUsers"}, {"method": "GET", "route": "superadmin/getUserDetails/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::getUserDetails/$1"}, {"method": "GET", "route": "superadmin/getAuditLogs", "handler": "\\App\\Controllers\\SuperAdmin::getAuditLogs"}, {"method": "GET", "route": "superadmin/getAuditStats", "handler": "\\App\\Controllers\\SuperAdmin::getAuditStats"}, {"method": "GET", "route": "superadmin/getSuperAdminLogs", "handler": "\\App\\Controllers\\SuperAdmin::getSuperAdminLogs"}, {"method": "GET", "route": "superadmin/exportAuditLogs", "handler": "\\App\\Controllers\\SuperAdmin::exportAuditLogs"}, {"method": "GET", "route": "superadmin/getAuditFilterOptions", "handler": "\\App\\Controllers\\SuperAdmin::getAuditFilterOptions"}, {"method": "GET", "route": "superadmin/getRecentActivities", "handler": "\\App\\Controllers\\SuperAdmin::getRecentActivities"}, {"method": "GET", "route": "superadmin/getNotifications", "handler": "\\App\\Controllers\\SuperAdmin::getNotifications"}, {"method": "GET", "route": "superadmin/getSchoolDetails/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::getSchoolDetails/$1"}, {"method": "GET", "route": "superadmin/getSubscriptionData", "handler": "\\App\\Controllers\\SuperAdmin::getSubscriptionData"}, {"method": "GET", "route": "superadmin/getAnalyticsData", "handler": "\\App\\Controllers\\SuperAdmin::getAnalyticsData"}, {"method": "GET", "route": "superadmin/getSystemSettings", "handler": "\\App\\Controllers\\SuperAdmin::getSystemSettings"}, {"method": "GET", "route": "superadmin/testProfile", "handler": "\\App\\Controllers\\SuperAdmin::testProfile"}, {"method": "GET", "route": "superadmin/getProfile", "handler": "\\App\\Controllers\\SuperAdmin::getProfile"}, {"method": "GET", "route": "admin/schools/pending", "handler": "\\App\\Controllers\\SchoolController::pending"}, {"method": "GET", "route": "admin/schools/active", "handler": "\\App\\Controllers\\SchoolController::active"}, {"method": "GET", "route": "admin/schools/all", "handler": "\\App\\Controllers\\SchoolController::all"}, {"method": "GET", "route": "schooladmin/getUsers", "handler": "\\App\\Controllers\\SchoolAdmin::getUsers"}, {"method": "GET", "route": "schooladmin/getStaff", "handler": "\\App\\Controllers\\SchoolAdmin::getStaff"}, {"method": "GET", "route": "schooladmin/getDeletedUsers", "handler": "\\App\\Controllers\\SchoolAdmin::getDeletedUsers"}, {"method": "GET", "route": "schooladmin/getUserDetails/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getUserDetails/$1"}, {"method": "GET", "route": "schooladmin/getStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getStaffMember/$1"}, {"method": "GET", "route": "schooladmin/testSoftDelete", "handler": "\\App\\Controllers\\SchoolAdmin::testSoftDelete"}, {"method": "GET", "route": "schooladmin/settings", "handler": "\\App\\Controllers\\SchoolAdmin::settings"}, {"method": "GET", "route": "schooladmin/getSchoolSettings", "handler": "\\App\\Controllers\\SchoolAdmin::getSchoolSettings"}, {"method": "GET", "route": "schooladmin/getStaffMembers", "handler": "\\App\\Controllers\\SchoolAdmin::getStaffMembers"}, {"method": "GET", "route": "schooladmin/getQuestionsForReview", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionsForReview"}, {"method": "GET", "route": "schooladmin/getQuestionDetails/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionDetails/$1"}, {"method": "GET", "route": "schooladmin/getApprovedQuestions", "handler": "\\App\\Controllers\\SchoolAdmin::getApprovedQuestions"}, {"method": "GET", "route": "schooladmin/getQuestionPapers", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPapers"}, {"method": "GET", "route": "schooladmin/getQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPaper/$1"}, {"method": "GET", "route": "schooladmin/testSession", "handler": "\\App\\Controllers\\SchoolAdmin::testSession"}, {"method": "GET", "route": "schooladmin/downloadQuestionPaperPDF/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::downloadQuestionPaperPDF/$1"}, {"method": "GET", "route": "schooladmin/downloadAnswerKeyPDF/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::downloadAnswerKeyPDF/$1"}, {"method": "GET", "route": "schooladmin/getQuestionPaperForEdit/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::getQuestionPaperForEdit/$1"}, {"method": "GET", "route": "staff/login", "handler": "\\App\\Controllers\\Staff::login"}, {"method": "GET", "route": "staff/dashboard", "handler": "\\App\\Controllers\\Staff::dashboard"}, {"method": "GET", "route": "staff/profile", "handler": "\\App\\Controllers\\Staff::profile"}, {"method": "GET", "route": "staff/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "staff/getSubjects", "handler": "\\App\\Controllers\\Staff::getSubjects"}, {"method": "GET", "route": "staff/getQuestions", "handler": "\\App\\Controllers\\Staff::getQuestions"}, {"method": "GET", "route": "staff/getQuestionDetails/([0-9]+)", "handler": "\\App\\Controllers\\Staff::getQuestionDetails/$1"}, {"method": "GET", "route": "staff/getSubjectsWithCounts", "handler": "\\App\\Controllers\\Staff::getSubjectsWithCounts"}, {"method": "GET", "route": "staff/getQuestionsBySubject/([^/]+)", "handler": "\\App\\Controllers\\Staff::getQuestionsBySubject/$1"}, {"method": "GET", "route": "staff/getDetailedReports", "handler": "\\App\\Controllers\\Staff::getDetailedReports"}, {"method": "GET", "route": "staff/getRecentActivities", "handler": "\\App\\Controllers\\Staff::getRecentActivities"}, {"method": "GET", "route": "staff/testUpdate/([0-9]+)", "handler": "\\App\\Controllers\\Staff::testUpdate/$1"}, {"method": "GET", "route": "staff/getQuestionPapers", "handler": "\\App\\Controllers\\Staff::getQuestionPapers"}, {"method": "GET", "route": "staff/getQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\Staff::getQuestionPaper/$1"}, {"method": "GET", "route": "staff/downloadQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\Staff::downloadQuestionPaper/$1"}, {"method": "POST", "route": "school/register", "handler": "\\App\\Controllers\\SchoolController::register"}, {"method": "POST", "route": "register/send-otp", "handler": "\\App\\Controllers\\Register::sendOtp"}, {"method": "POST", "route": "register/verify-otp", "handler": "\\App\\Controllers\\Register::verifyOtp"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "forgot-password/send", "handler": "\\App\\Controllers\\ForgotPassword::sendResetLink"}, {"method": "POST", "route": "forgot-password/reset", "handler": "\\App\\Controllers\\ForgotPassword::resetPassword"}, {"method": "POST", "route": "subscription/select-plan", "handler": "\\App\\Controllers\\Subscription::selectPlan"}, {"method": "POST", "route": "subscription/upgrade", "handler": "\\App\\Controllers\\Subscription::processUpgrade"}, {"method": "POST", "route": "subscription/cancel", "handler": "\\App\\Controllers\\Subscription::cancel"}, {"method": "POST", "route": "payment/initiate", "handler": "\\App\\Controllers\\Payment::initiate"}, {"method": "POST", "route": "payment/verify", "handler": "\\App\\Controllers\\Payment::verify"}, {"method": "POST", "route": "payment/webhook/([^/]+)", "handler": "\\App\\Controllers\\Payment::webhook/$1"}, {"method": "POST", "route": "superadmin/approve/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::approveSchool/$1"}, {"method": "POST", "route": "superadmin/reject/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::rejectSchool/$1"}, {"method": "POST", "route": "superadmin/toggleUserStatus/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::toggleUserStatus/$1"}, {"method": "POST", "route": "superadmin/updateSystemSettings", "handler": "\\App\\Controllers\\SuperAdmin::updateSystemSettings"}, {"method": "POST", "route": "superadmin/updateSetting", "handler": "\\App\\Controllers\\SuperAdmin::updateSetting"}, {"method": "POST", "route": "superadmin/changePassword", "handler": "\\App\\Controllers\\SuperAdmin::changePassword"}, {"method": "POST", "route": "admin/schools/approve/([0-9]+)", "handler": "\\App\\Controllers\\SchoolController::approve/$1"}, {"method": "POST", "route": "admin/schools/superadmin/reject/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::rejectSchool/$1"}, {"method": "POST", "route": "schooladmin/addUser", "handler": "\\App\\Controllers\\SchoolAdmin::addUser"}, {"method": "POST", "route": "schooladmin/deleteUser/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteUser/$1"}, {"method": "POST", "route": "schooladmin/restoreUser/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::restoreUser/$1"}, {"method": "POST", "route": "schooladmin/updateStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::updateStaffMember/$1"}, {"method": "POST", "route": "schooladmin/updateStaff", "handler": "\\App\\Controllers\\SchoolAdmin::updateStaff"}, {"method": "POST", "route": "schooladmin/toggleStaffStatus/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::toggleStaffStatus/$1"}, {"method": "POST", "route": "schooladmin/updateSchoolProfile", "handler": "\\App\\Controllers\\SchoolAdmin::updateSchoolProfile"}, {"method": "POST", "route": "schooladmin/saveAllSettings", "handler": "\\App\\Controllers\\SchoolAdmin::saveAllSettings"}, {"method": "POST", "route": "schooladmin/approveQuestion/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::approveQuestion/$1"}, {"method": "POST", "route": "schooladmin/rejectQuestion/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::rejectQuestion/$1"}, {"method": "POST", "route": "schooladmin/createQuestionPaper", "handler": "\\App\\Controllers\\SchoolAdmin::createQuestionPaper"}, {"method": "POST", "route": "schooladmin/downloadQuestionPaperPDF", "handler": "\\App\\Controllers\\SchoolAdmin::downloadQuestionPaperPDF"}, {"method": "POST", "route": "schooladmin/downloadAnswerKeyPDF", "handler": "\\App\\Controllers\\SchoolAdmin::downloadAnswerKeyPDF"}, {"method": "POST", "route": "staff/authenticate", "handler": "\\App\\Controllers\\Staff::authenticate"}, {"method": "POST", "route": "staff/updateProfile", "handler": "\\App\\Controllers\\Staff::updateProfile"}, {"method": "POST", "route": "staff/createQuestion", "handler": "\\App\\Controllers\\Staff::createQuestion"}, {"method": "POST", "route": "staff/saveDraft", "handler": "\\App\\Controllers\\Staff::saveDraft"}, {"method": "POST", "route": "staff/updateQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::updateQuestion/$1"}, {"method": "POST", "route": "staff/createQuestionPaper", "handler": "\\App\\Controllers\\Staff::createQuestionPaper"}, {"method": "POST", "route": "school/check-email", "handler": "\\App\\Controllers\\SchoolController::checkEmail"}, {"method": "PUT", "route": "schooladmin/updateQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::updateQuestionPaper/$1"}, {"method": "PUT", "route": "staff/updateQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::updateQuestion/$1"}, {"method": "PUT", "route": "staff/updateQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\Staff::updateQuestionPaper/$1"}, {"method": "DELETE", "route": "superadmin/deleteUser/([0-9]+)", "handler": "\\App\\Controllers\\SuperAdmin::deleteUser/$1"}, {"method": "DELETE", "route": "schooladmin/deleteStaffMember/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteStaffMember/$1"}, {"method": "DELETE", "route": "schooladmin/deleteQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\SchoolAdmin::deleteQuestionPaper/$1"}, {"method": "DELETE", "route": "staff/deleteQuestion/([0-9]+)", "handler": "\\App\\Controllers\\Staff::deleteQuestion/$1"}, {"method": "DELETE", "route": "staff/deleteQuestionPaper/([0-9]+)", "handler": "\\App\\Controllers\\Staff::deleteQuestionPaper/$1"}]}, "badgeValue": 81, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "78.25", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "1.19", "count": 13}}}, "badgeValue": 14, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.500412, "duration": 0.07825303077697754}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.071283, "duration": 0.00010395050048828125}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.085152, "duration": 8.0108642578125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.095827, "duration": 0.00011587142944335938}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.097727, "duration": 6.198883056640625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.099424, "duration": 5.91278076171875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.101157, "duration": 0.00010204315185546875}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.106964, "duration": 0.00011396408081054688}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.111288, "duration": 0.0001068115234375}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.11347, "duration": 7.295608520507812e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.117116, "duration": 7.510185241699219e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.121866, "duration": 9.799003601074219e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.124902, "duration": 0.0001380443572998047}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.130696, "duration": 5.888938903808594e-05}]}], "vars": {"varData": {"View Data": {"school": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (12)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (31) \"St.Josephs matriculation school\"<div class=\"access-path\">$value['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value['email']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>password</dfn> =&gt; <var>string</var> (61) \"$2y$10$CLTW6ueBbtCYsSeTMRpbReCGhHu8xjAaewmweRa1SY88QrckC8vSy \"<div class=\"access-path\">$value['password']</div></dt><dd><pre>$2y$10$CLTW6ueBbtCYsSeTMRpbReCGhHu8xjAaewmweRa1SY88QrckC8vSy\n\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['plan_id']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (23) \"sathyamanagalam, Erode.\"<div class=\"access-path\">$value['address']</div></dt><dd><pre>sathyamanagalam,\nErode.\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"8778498541\"<div class=\"access-path\">$value['phone']</div></dt><dd><pre>2248-03-06T23:49:01+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejection_reason</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['rejection_reason']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-07-08 04:38:11\"<div class=\"access-path\">$value['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-07-15 09:13:34\"<div class=\"access-path\">$value['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>deleted_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['deleted_at']</div></dt></dl></dd></dl></div>", "stats": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (9)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_questions</dfn> =&gt; <var>integer</var> 14<div class=\"access-path\">$value['total_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_review</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['pending_review']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>approved_questions</dfn> =&gt; <var>integer</var> 12<div class=\"access-path\">$value['approved_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejected_questions</dfn> =&gt; <var>integer</var> 2<div class=\"access-path\">$value['rejected_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>active_users</dfn> =&gt; <var>integer</var> 10<div class=\"access-path\">$value['active_users']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>success_rate</dfn> =&gt; <var>double</var> 85.7<div class=\"access-path\">$value['success_rate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_staff</dfn> =&gt; <var>integer</var> 10<div class=\"access-path\">$value['total_staff']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_subjects</dfn> =&gt; <var>integer</var> 8<div class=\"access-path\">$value['total_subjects']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_reviews</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value['pending_reviews']</div></dt></dl></dd></dl></div>", "recent_activity": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (17)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (17)</li><li>Contents (17)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>type</th><th>status</th><th>title</th><th>description</th><th>time</th><th>icon</th><th>color</th></tr></thead><tbody><tr><th>0</th><td title=\"string (5)\">staff</td><td title=\"string (5)\">added</td><td title=\"string (18)\">Staff member added</td><td title=\"string (21)\">stark joined the team</td><td title=\"string (19)\">2025-07-22 09:07:58</td><td title=\"string (16)\">fas fa-user-plus</td><td title=\"string (4)\">blue</td></tr><tr><th>1</th><td title=\"string (5)\">staff</td><td title=\"string (5)\">added</td><td title=\"string (18)\">Staff member added</td><td title=\"string (21)\">steve joined the team</td><td title=\"string (19)\">2025-07-15 17:20:43</td><td title=\"string (16)\">fas fa-user-plus</td><td title=\"string (4)\">blue</td></tr><tr><th>2</th><td title=\"string (5)\">staff</td><td title=\"string (5)\">added</td><td title=\"string (18)\">Staff member added</td><td title=\"string (28)\">Test Teacher joined the team</td><td title=\"string (19)\">2025-07-15 17:09:07</td><td title=\"string (16)\">fas fa-user-plus</td><td title=\"string (4)\">blue</td></tr><tr><th>3</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (22)\">Mathematics - Class 10</td><td title=\"string (19)\">2025-07-15 05:32:14</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>4</th><td title=\"string (8)\">question</td><td title=\"string (8)\">rejected</td><td title=\"string (17)\">Question rejected</td><td title=\"string (17)\">Biology - Class 9</td><td title=\"string (19)\">2025-07-15 05:32:01</td><td title=\"string (12)\">fas fa-times</td><td title=\"string (3)\">red</td></tr><tr><th>5</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (27)\">Computer Science - Class 11</td><td title=\"string (19)\">2025-07-15 04:18:16</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>6</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (17)\">English - Class 8</td><td title=\"string (19)\">2025-07-15 02:13:30</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>7</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (21)\">Mathematics - Class 1</td><td title=\"string (19)\">2025-07-14 10:43:09</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>8</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (27)\">Computer Science - Class 12</td><td title=\"string (19)\">2025-07-14 10:13:51</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>9</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (27)\">Computer Science - Class 12</td><td title=\"string (19)\">2025-07-14 10:13:48</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>10</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (18)\">Biology - Class 12</td><td title=\"string (19)\">2025-07-14 10:13:44</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>11</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (24)\">Social Science - Class 8</td><td title=\"string (19)\">2025-07-14 09:50:05</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>12</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (21)\">Mathematics - Class 2</td><td title=\"string (19)\">2025-07-14 09:19:49</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>13</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (17)\">Science - Class 8</td><td title=\"string (19)\">2025-07-14 09:14:55</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>14</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (25)\">Social Science - Class 10</td><td title=\"string (19)\">2025-07-14 08:40:14</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>15</th><td title=\"string (8)\">question</td><td title=\"string (8)\">approved</td><td title=\"string (17)\">Question approved</td><td title=\"string (18)\">Physics - Class 11</td><td title=\"string (19)\">2025-07-14 05:13:30</td><td title=\"string (12)\">fas fa-check</td><td title=\"string (5)\">green</td></tr><tr><th>16</th><td title=\"string (8)\">question</td><td title=\"string (8)\">rejected</td><td title=\"string (17)\">Question rejected</td><td title=\"string (20)\">Chemistry - Class 12</td><td title=\"string (19)\">2025-07-14 05:13:30</td><td title=\"string (12)\">fas fa-times</td><td title=\"string (3)\">red</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (5) \"staff\"<div class=\"access-path\">$value[0]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (5) \"added\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (18) \"Staff member added\"<div class=\"access-path\">$value[0]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (21) \"stark joined the team\"<div class=\"access-path\">$value[0]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-22 09:07:58\"<div class=\"access-path\">$value[0]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (16) \"fas fa-user-plus\"<div class=\"access-path\">$value[0]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (4) \"blue\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 0, 255, 1)\"></div></div><div class=\"access-path\">$value[0]['color']</div></dt><dd><pre><dfn>blue</dfn>\n<dfn>#00F</dfn>\n<dfn>#0000FF</dfn>\n<dfn>rgb(0, 0, 255)</dfn>\n<dfn>hsl(240, 100%, 50%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (5) \"staff\"<div class=\"access-path\">$value[1]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (5) \"added\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (18) \"Staff member added\"<div class=\"access-path\">$value[1]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (21) \"steve joined the team\"<div class=\"access-path\">$value[1]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-15 17:20:43\"<div class=\"access-path\">$value[1]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (16) \"fas fa-user-plus\"<div class=\"access-path\">$value[1]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (4) \"blue\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 0, 255, 1)\"></div></div><div class=\"access-path\">$value[1]['color']</div></dt><dd><pre><dfn>blue</dfn>\n<dfn>#00F</dfn>\n<dfn>#0000FF</dfn>\n<dfn>rgb(0, 0, 255)</dfn>\n<dfn>hsl(240, 100%, 50%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (5) \"staff\"<div class=\"access-path\">$value[2]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (5) \"added\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (18) \"Staff member added\"<div class=\"access-path\">$value[2]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (28) \"Test Teacher joined the team\"<div class=\"access-path\">$value[2]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-15 17:09:07\"<div class=\"access-path\">$value[2]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (16) \"fas fa-user-plus\"<div class=\"access-path\">$value[2]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (4) \"blue\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 0, 255, 1)\"></div></div><div class=\"access-path\">$value[2]['color']</div></dt><dd><pre><dfn>blue</dfn>\n<dfn>#00F</dfn>\n<dfn>#0000FF</dfn>\n<dfn>rgb(0, 0, 255)</dfn>\n<dfn>hsl(240, 100%, 50%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[3]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[3]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (22) \"Mathematics - Class 10\"<div class=\"access-path\">$value[3]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-15 05:32:14\"<div class=\"access-path\">$value[3]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[3]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[3]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[4]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"rejected\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question rejected\"<div class=\"access-path\">$value[4]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (17) \"Biology - Class 9\"<div class=\"access-path\">$value[4]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-15 05:32:01\"<div class=\"access-path\">$value[4]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-times\"<div class=\"access-path\">$value[4]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (3) \"red\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 0, 0, 1)\"></div></div><div class=\"access-path\">$value[4]['color']</div></dt><dd><pre><dfn>red</dfn>\n<dfn>#F00</dfn>\n<dfn>#FF0000</dfn>\n<dfn>rgb(255, 0, 0)</dfn>\n<dfn>hsl(0, 100%, 50%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[5]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[5]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (27) \"Computer Science - Class 11\"<div class=\"access-path\">$value[5]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-15 04:18:16\"<div class=\"access-path\">$value[5]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[5]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[5]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[6]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[6]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (17) \"English - Class 8\"<div class=\"access-path\">$value[6]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-15 02:13:30\"<div class=\"access-path\">$value[6]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[6]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[6]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[7]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[7]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (21) \"Mathematics - Class 1\"<div class=\"access-path\">$value[7]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 10:43:09\"<div class=\"access-path\">$value[7]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[7]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[7]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[8]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[8]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (27) \"Computer Science - Class 12\"<div class=\"access-path\">$value[8]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 10:13:51\"<div class=\"access-path\">$value[8]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[8]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[8]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[9]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[9]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (27) \"Computer Science - Class 12\"<div class=\"access-path\">$value[9]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 10:13:48\"<div class=\"access-path\">$value[9]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[9]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[9]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[10]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[10]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[10]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (18) \"Biology - Class 12\"<div class=\"access-path\">$value[10]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 10:13:44\"<div class=\"access-path\">$value[10]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[10]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[10]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[11]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[11]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[11]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (24) \"Social Science - Class 8\"<div class=\"access-path\">$value[11]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 09:50:05\"<div class=\"access-path\">$value[11]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[11]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[11]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[12]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[12]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[12]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (21) \"Mathematics - Class 2\"<div class=\"access-path\">$value[12]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 09:19:49\"<div class=\"access-path\">$value[12]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[12]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[12]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[13]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[13]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[13]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (17) \"Science - Class 8\"<div class=\"access-path\">$value[13]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 09:14:55\"<div class=\"access-path\">$value[13]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[13]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[13]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[14]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[14]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[14]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (25) \"Social Science - Class 10\"<div class=\"access-path\">$value[14]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 08:40:14\"<div class=\"access-path\">$value[14]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[14]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[14]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[15]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"approved\"<div class=\"access-path\">$value[15]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question approved\"<div class=\"access-path\">$value[15]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (18) \"Physics - Class 11\"<div class=\"access-path\">$value[15]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 05:13:30\"<div class=\"access-path\">$value[15]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-check\"<div class=\"access-path\">$value[15]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[15]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>type</dfn> =&gt; <var>string</var> (8) \"question\"<div class=\"access-path\">$value[16]['type']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (8) \"rejected\"<div class=\"access-path\">$value[16]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>title</dfn> =&gt; <var>string</var> (17) \"Question rejected\"<div class=\"access-path\">$value[16]['title']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>description</dfn> =&gt; <var>string</var> (20) \"Chemistry - Class 12\"<div class=\"access-path\">$value[16]['description']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>time</dfn> =&gt; <var>string</var> (19) \"2025-07-14 05:13:30\"<div class=\"access-path\">$value[16]['time']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (12) \"fas fa-times\"<div class=\"access-path\">$value[16]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (3) \"red\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 0, 0, 1)\"></div></div><div class=\"access-path\">$value[16]['color']</div></dt><dd><pre><dfn>red</dfn>\n<dfn>#F00</dfn>\n<dfn>#FF0000</dfn>\n<dfn>rgb(255, 0, 0)</dfn>\n<dfn>hsl(0, 100%, 50%)</dfn>\n</pre></dd></dl></dd></dl></li></ul></dd></dl></div>", "subject_performance": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (6)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (6)</li><li>Contents (6)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>name</th><th>total_questions</th><th>approved_questions</th><th>pending_questions</th><th>rejected_questions</th><th>icon</th><th>color</th></tr></thead><tbody><tr><th>0</th><td title=\"string (16)\">Computer Science</td><td title=\"integer\">3</td><td title=\"integer\">3</td><td title=\"integer\">0</td><td title=\"integer\">0</td><td title=\"string (18)\">fas fa-laptop-code</td><td title=\"string (4)\">gray</td></tr><tr><th>1</th><td title=\"string (11)\">Mathematics</td><td title=\"integer\">3</td><td title=\"integer\">3</td><td title=\"integer\">0</td><td title=\"integer\">0</td><td title=\"string (17)\">fas fa-calculator</td><td title=\"string (4)\">blue</td></tr><tr><th>2</th><td title=\"string (14)\">Social Science</td><td title=\"integer\">2</td><td title=\"integer\">2</td><td title=\"integer\">0</td><td title=\"integer\">0</td><td title=\"string (11)\">fas fa-book</td><td title=\"string (4)\">gray</td></tr><tr><th>3</th><td title=\"string (7)\">Biology</td><td title=\"integer\">2</td><td title=\"integer\">1</td><td title=\"integer\">0</td><td title=\"integer\">1</td><td title=\"string (10)\">fas fa-dna</td><td title=\"string (5)\">green</td></tr><tr><th>4</th><td title=\"string (7)\">English</td><td title=\"integer\">1</td><td title=\"integer\">1</td><td title=\"integer\">0</td><td title=\"integer\">0</td><td title=\"string (11)\">fas fa-book</td><td title=\"string (6)\">indigo</td></tr><tr><th>5</th><td title=\"string (9)\">Chemistry</td><td title=\"integer\">1</td><td title=\"integer\">0</td><td title=\"integer\">0</td><td title=\"integer\">1</td><td title=\"string (11)\">fas fa-vial</td><td title=\"string (3)\">red</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Computer Science\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_questions</dfn> =&gt; <var>integer</var> 3<div class=\"access-path\">$value[0]['total_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>approved_questions</dfn> =&gt; <var>integer</var> 3<div class=\"access-path\">$value[0]['approved_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[0]['pending_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejected_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[0]['rejected_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (18) \"fas fa-laptop-code\"<div class=\"access-path\">$value[0]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (4) \"gray\"<div class=\"kint-color-preview\"><div style=\"background:rgba(128, 128, 128, 1)\"></div></div><div class=\"access-path\">$value[0]['color']</div></dt><dd><pre><dfn>gray</dfn>\n<dfn>#808080</dfn>\n<dfn>rgb(128, 128, 128)</dfn>\n<dfn>hsl(0, 0%, 50%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Mathematics\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_questions</dfn> =&gt; <var>integer</var> 3<div class=\"access-path\">$value[1]['total_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>approved_questions</dfn> =&gt; <var>integer</var> 3<div class=\"access-path\">$value[1]['approved_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[1]['pending_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejected_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[1]['rejected_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (17) \"fas fa-calculator\"<div class=\"access-path\">$value[1]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (4) \"blue\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 0, 255, 1)\"></div></div><div class=\"access-path\">$value[1]['color']</div></dt><dd><pre><dfn>blue</dfn>\n<dfn>#00F</dfn>\n<dfn>#0000FF</dfn>\n<dfn>rgb(0, 0, 255)</dfn>\n<dfn>hsl(240, 100%, 50%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Social Science\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_questions</dfn> =&gt; <var>integer</var> 2<div class=\"access-path\">$value[2]['total_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>approved_questions</dfn> =&gt; <var>integer</var> 2<div class=\"access-path\">$value[2]['approved_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[2]['pending_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejected_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[2]['rejected_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (11) \"fas fa-book\"<div class=\"access-path\">$value[2]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (4) \"gray\"<div class=\"kint-color-preview\"><div style=\"background:rgba(128, 128, 128, 1)\"></div></div><div class=\"access-path\">$value[2]['color']</div></dt><dd><pre><dfn>gray</dfn>\n<dfn>#808080</dfn>\n<dfn>rgb(128, 128, 128)</dfn>\n<dfn>hsl(0, 0%, 50%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (7) \"Biology\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_questions</dfn> =&gt; <var>integer</var> 2<div class=\"access-path\">$value[3]['total_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>approved_questions</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[3]['approved_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[3]['pending_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejected_questions</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[3]['rejected_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (10) \"fas fa-dna\"<div class=\"access-path\">$value[3]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (5) \"green\"<div class=\"kint-color-preview\"><div style=\"background:rgba(0, 128, 0, 1)\"></div></div><div class=\"access-path\">$value[3]['color']</div></dt><dd><pre><dfn>green</dfn>\n<dfn>#008000</dfn>\n<dfn>rgb(0, 128, 0)</dfn>\n<dfn>hsl(120, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (7) \"English\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_questions</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[4]['total_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>approved_questions</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[4]['approved_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[4]['pending_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejected_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[4]['rejected_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (11) \"fas fa-book\"<div class=\"access-path\">$value[4]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (6) \"indigo\"<div class=\"kint-color-preview\"><div style=\"background:rgba(75, 0, 130, 1)\"></div></div><div class=\"access-path\">$value[4]['color']</div></dt><dd><pre><dfn>indigo</dfn>\n<dfn>#4B0082</dfn>\n<dfn>rgb(75, 0, 130)</dfn>\n<dfn>hsl(274, 100%, 25%)</dfn>\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (7)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (9) \"Chemistry\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_questions</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[5]['total_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>approved_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[5]['approved_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>pending_questions</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[5]['pending_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>rejected_questions</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value[5]['rejected_questions']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>icon</dfn> =&gt; <var>string</var> (11) \"fas fa-vial\"<div class=\"access-path\">$value[5]['icon']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>color</dfn> =&gt; <var>string</var> (3) \"red\"<div class=\"kint-color-preview\"><div style=\"background:rgba(255, 0, 0, 1)\"></div></div><div class=\"access-path\">$value[5]['color']</div></dt><dd><pre><dfn>red</dfn>\n<dfn>#F00</dfn>\n<dfn>#FF0000</dfn>\n<dfn>rgb(255, 0, 0)</dfn>\n<dfn>hsl(0, 100%, 50%)</dfn>\n</pre></dd></dl></dd></dl></li></ul></dd></dl></div>", "school_name": "<PERSON><PERSON> matriculation school", "school_email": "<EMAIL>", "school_phone": "8778498541", "school_address": "<PERSON><PERSON><PERSON><PERSON><PERSON>,\nE<PERSON>e.", "school_status": "active", "current_subscription": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (18)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value['id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>school_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['school_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value['plan_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>billing_cycle</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value['billing_cycle']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>currency</dfn> =&gt; <var>string</var> (3) \"INR\"<div class=\"access-path\">$value['currency']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>started_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['started_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>expires_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['expires_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>trial_ends_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['trial_ends_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>cancelled_at</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['cancelled_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_gateway</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['payment_gateway']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>gateway_subscription_id</dfn> =&gt; <var>null</var><div class=\"access-path\">$value['gateway_subscription_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-06-18 17:48:21\"<div class=\"access-path\">$value['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-06-18 17:48:21\"<div class=\"access-path\">$value['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_name</dfn> =&gt; <var>string</var> (5) \"trial\"<div class=\"access-path\">$value['plan_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>plan_display_name</dfn> =&gt; <var>string</var> (10) \"Free Trial\"<div class=\"access-path\">$value['plan_display_name']</div></dt></dl></dd></dl></div>", "recent_transactions": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>array</var> (0)</dt></dl></div>"}}, "session": {"_ci_previous_url": "http://localhost:8080/index.php/schooladmin/dashboard", "school_id": "1", "school_name": "<PERSON><PERSON> matriculation school", "email": "<EMAIL>", "role": "<PERSON><PERSON><PERSON>", "logged_in": "<pre>1</pre>", "user_id": "1", "success": "Login successful.", "__ci_vars": "<pre>Array\n(\n    [success] =&gt; old\n)\n</pre>"}, "headers": {"Host": "localhost:8080", "Connection": "keep-alive", "Sec-Ch-Ua": "&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;Windows&quot;", "Upgrade-Insecure-Requests": "1", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Referer": "http://localhost:8080/index.php/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "en-GB,en;q=0.9,en-US;q=0.8,en-IN;q=0.7", "Cookie": "csrf_cookie_name=04240c5a87edf67dc22c467d8b6902c1; ci_session=029de215395ed719571f8e2d82e836a1"}, "cookies": {"csrf_cookie_name": "04240c5a87edf67dc22c467d8b6902c1", "ci_session": "029de215395ed719571f8e2d82e836a1"}, "request": "HTTP/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.12", "phpSAPI": "cli-server", "environment": "development", "baseURL": "http://localhost:8080/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}