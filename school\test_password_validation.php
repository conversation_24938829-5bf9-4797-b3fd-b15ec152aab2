<?php
echo "=== Testing Password Validation ===\n\n";

// Test passwords
$testPasswords = [
    'weak' => '123456',
    'short' => 'Abc1!',
    'no_upper' => 'abc123!@',
    'no_lower' => 'ABC123!@',
    'no_number' => 'Abcdef!@',
    'no_special' => 'Abc12345',
    'valid' => 'MyPass123!',
    'valid2' => 'SecureP@ss1'
];

function validatePasswordStrength($password)
{
    $errors = [];

    // Check minimum length
    if (strlen($password) < 8) {
        $errors[] = 'at least 8 characters';
    }

    // Check for uppercase letter
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'one uppercase letter';
    }

    // Check for lowercase letter
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'one lowercase letter';
    }

    // Check for number
    if (!preg_match('/\d/', $password)) {
        $errors[] = 'one number';
    }

    // Check for special character
    if (!preg_match('/[@$!%*?&]/', $password)) {
        $errors[] = 'one special character (@$!%*?&)';
    }

    return $errors;
}

foreach ($testPasswords as $type => $password) {
    echo "Testing '$type' password: '$password'\n";
    $errors = validatePasswordStrength($password);
    
    if (empty($errors)) {
        echo "   ✅ VALID - Password meets all requirements\n";
    } else {
        echo "   ❌ INVALID - Missing: " . implode(', ', $errors) . "\n";
    }
    echo "\n";
}

echo "=== Password Validation Test Complete ===\n";
?>
