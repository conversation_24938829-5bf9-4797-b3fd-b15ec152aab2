<?php
// Test routes functionality
echo "<h1>🛣️ Routes Test</h1>";

// Test URLs
$testUrls = [
    'SuperAdmin Dashboard' => 'http://localhost:3000/m-dev.php/superadmin/dashboard',
    'Get Users API' => 'http://localhost:3000/m-dev.php/superadmin/getUsers',
    'Get User Details (ID 1)' => 'http://localhost:3000/m-dev.php/superadmin/getUserDetails/1',
    'Toggle User Status (ID 1)' => 'http://localhost:3000/m-dev.php/superadmin/toggleUserStatus/1',
    'Delete User (ID 1)' => 'http://localhost:3000/m-dev.php/superadmin/deleteUser/1',
];

echo "<h2>📋 Available Routes</h2>";
echo "<table border='1' cellpadding='5' cellspacing='0'>";
echo "<tr><th>Route Name</th><th>URL</th><th>Method</th><th>Test</th></tr>";

foreach ($testUrls as $name => $url) {
    $method = 'GET';
    if (strpos($name, 'Toggle') !== false) $method = 'POST';
    if (strpos($name, 'Delete') !== false) $method = 'DELETE';
    
    echo "<tr>";
    echo "<td>$name</td>";
    echo "<td><code>$url</code></td>";
    echo "<td><strong>$method</strong></td>";
    echo "<td><a href='$url' target='_blank'>Test</a></td>";
    echo "</tr>";
}
echo "</table>";

echo "<br><h2>🔧 Manual Testing</h2>";
echo "<p>Use the following JavaScript in the browser console to test the API endpoints:</p>";

echo "<h3>1. Test Get Users</h3>";
echo "<pre><code>
fetch('http://localhost:3000/m-dev.php/superadmin/getUsers', {
    method: 'GET',
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content')
    }
})
.then(response => response.json())
.then(data => console.log('Users:', data))
.catch(error => console.error('Error:', error));
</code></pre>";

echo "<h3>2. Test Toggle User Status (replace USER_ID)</h3>";
echo "<pre><code>
fetch('http://localhost:3000/m-dev.php/superadmin/toggleUserStatus/USER_ID', {
    method: 'POST',
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content')
    }
})
.then(response => response.json())
.then(data => console.log('Toggle result:', data))
.catch(error => console.error('Error:', error));
</code></pre>";

echo "<h3>3. Test Delete User (replace USER_ID)</h3>";
echo "<pre><code>
fetch('http://localhost:3000/m-dev.php/superadmin/deleteUser/USER_ID', {
    method: 'DELETE',
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'X-CSRF-TOKEN': document.querySelector('meta[name=\"csrf-token\"]').getAttribute('content')
    }
})
.then(response => response.json())
.then(data => console.log('Delete result:', data))
.catch(error => console.error('Error:', error));
</code></pre>";

echo "<br><h2>📝 Notes</h2>";
echo "<ul>";
echo "<li>Make sure you're logged in as SuperAdmin first</li>";
echo "<li>The CSRF token is required for POST/DELETE requests</li>";
echo "<li>Replace USER_ID with an actual user ID from the database</li>";
echo "<li>Check the browser console for detailed error messages</li>";
echo "</ul>";

echo "<br><h2>🔗 Quick Links</h2>";
echo "<a href='http://localhost:3000/m-dev.php/login/superadmin' target='_blank'>🔐 SuperAdmin Login</a><br>";
echo "<a href='http://localhost:3000/m-dev.php/superadmin/dashboard' target='_blank'>📊 Dashboard</a><br>";
echo "<a href='http://localhost:3000/m-dev.php/test_api.html' target='_blank'>🧪 API Test Page</a><br>";
echo "<a href='http://localhost:3000/m-dev.php/test_user_management.php' target='_blank'>👥 User Management Test</a><br>";
?>
