<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PlanModel;
use App\Models\SubscriptionModel;
use App\Services\SubscriptionService;

class Subscription extends BaseController
{
    protected $planModel;
    protected $subscriptionModel;
    protected $subscriptionService;

    public function __construct()
    {
        $this->planModel = new PlanModel();
        $this->subscriptionModel = new SubscriptionModel();
        $this->subscriptionService = new SubscriptionService();
    }

    /**
     * Show available plans for selection
     */
    public function plans()
    {
        // Get all active plans with features
        $plans = $this->planModel->getActivePlansWithFeatures();

        $data = [
            'title' => 'Choose Your Plan',
            'plans' => $plans
        ];

        return view('subscription/plans', $data);
    }

    /**
     * Handle plan selection during school registration
     */
    public function selectPlan()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/');
        }

        $planId = $this->request->getPost('plan_id');
        $billingCycle = $this->request->getPost('billing_cycle') ?? 'monthly';

        // Validation
        if (!$planId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please select a plan'
            ]);
        }

        // Get plan details
        $plan = $this->planModel->find($planId);
        if (!$plan) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid plan selected'
            ]);
        }

        // Store plan selection in session for registration process
        session()->set([
            'selected_plan_id' => $planId,
            'selected_billing_cycle' => $billingCycle,
            'selected_plan_name' => $plan['name'],
            'selected_plan_display_name' => $plan['display_name']
        ]);

        // Calculate amount based on billing cycle
        $amount = ($billingCycle === 'yearly') ? $plan['price_yearly'] : $plan['price_monthly'];

        return $this->response->setJSON([
            'success' => true,
            'plan' => $plan,
            'amount' => $amount,
            'billing_cycle' => $billingCycle,
            'message' => 'Plan selected successfully'
        ]);
    }

    /**
     * Show current subscription details
     */
    public function current()
    {
        // Check authentication
        $session = session();
        $isLoggedIn = $session->get('logged_in');
        $userRole = $session->get('role');
        $schoolId = $session->get('school_id');

        if (!$isLoggedIn || $userRole !== 'schooladmin') {
            return redirect()->to('/login/schooladmin')->with('error', 'Please login to view subscription details');
        }

        if (!$schoolId) {
            // Try to get school ID from database as fallback
            $schoolModel = new \App\Models\SchoolModel();
            $school = $schoolModel->where('email', $session->get('email'))->first();

            if ($school) {
                $schoolId = $school['id'];
                $session->set('school_id', $schoolId);
            } else {
                return redirect()->to('/login/schooladmin')->with('error', 'School information not found. Please login again');
            }
        }

        // Get current subscription
        $subscription = $this->subscriptionModel->getCurrentSubscription($schoolId);
        
        if (!$subscription) {
            return redirect()->to('/subscription/plans');
        }

        // Get usage summary
        $usageSummary = $this->subscriptionService->getUsageSummary($schoolId);

        $data = [
            'title' => 'Current Subscription',
            'subscription' => $subscription,
            'usage' => $usageSummary
        ];

        return view('subscription/current', $data);
    }

    /**
     * Show upgrade options
     */
    public function upgrade()
    {
        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return redirect()->to('/login');
        }

        $schoolId = session()->get('school_id');
        $currentSubscription = $this->subscriptionModel->getCurrentSubscription($schoolId);

        // Get available plans for upgrade
        $plans = $this->planModel->getActivePlansWithFeatures();

        $data = [
            'title' => 'Upgrade Your Plan',
            'current_subscription' => $currentSubscription,
            'plans' => $plans
        ];

        return view('subscription/upgrade', $data);
    }

    /**
     * Process plan upgrade
     */
    public function processUpgrade()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/subscription/upgrade');
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Unauthorized'
            ]);
        }

        $schoolId = session()->get('school_id');
        $newPlanId = $this->request->getPost('plan_id');
        $billingCycle = $this->request->getPost('billing_cycle') ?? 'monthly';

        // Validation
        if (!$newPlanId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Please select a plan'
            ]);
        }

        // Get new plan details
        $newPlan = $this->planModel->find($newPlanId);
        if (!$newPlan) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid plan selected'
            ]);
        }

        // Calculate amount
        $amount = ($billingCycle === 'yearly') ? $newPlan['price_yearly'] : $newPlan['price_monthly'];

        // If it's a free plan, upgrade immediately
        if ($amount == 0) {
            $subscriptionId = $this->subscriptionService->changeSchoolPlan($schoolId, $newPlan['name'], $billingCycle);
            
            if ($subscriptionId) {
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Plan upgraded successfully',
                    'redirect' => '/subscription/current'
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to upgrade plan'
                ]);
            }
        }

        // For paid plans, redirect to payment
        return $this->response->setJSON([
            'success' => true,
            'requires_payment' => true,
            'plan' => $newPlan,
            'amount' => $amount,
            'billing_cycle' => $billingCycle,
            'redirect' => '/payment/process'
        ]);
    }

    /**
     * Cancel subscription
     */
    public function cancel()
    {
        if (!$this->request->isAJAX()) {
            return redirect()->to('/subscription/current');
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Unauthorized'
            ]);
        }

        $schoolId = session()->get('school_id');
        $reason = $this->request->getPost('reason') ?? 'User requested cancellation';

        // Cancel subscription
        $result = $this->subscriptionService->cancelSubscription($schoolId, $reason);

        if ($result) {
            return $this->response->setJSON([
                'success' => true,
                'message' => 'Subscription cancelled successfully'
            ]);
        } else {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Failed to cancel subscription'
            ]);
        }
    }

    /**
     * Get usage data for AJAX requests
     */
    public function getUsage()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(404);
        }

        // Check authentication
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Unauthorized'
            ]);
        }

        $schoolId = session()->get('school_id');
        $usageSummary = $this->subscriptionService->getUsageSummary($schoolId);

        return $this->response->setJSON([
            'success' => true,
            'usage' => $usageSummary
        ]);
    }
}
