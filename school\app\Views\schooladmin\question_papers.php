<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Question Papers - School Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .template-card {
            transition: all 0.3s ease;
        }
        .template-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .template-card:hover .template-icon {
            transform: scale(1.1);
        }
        .template-icon {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 w-64 bg-white shadow-lg z-40">
        <!-- Logo -->
        <div class="flex items-center justify-center h-16 px-4 gradient-bg">
            <h1 class="text-xl font-bold text-white">School Admin</h1>
        </div>

        <!-- Navigation -->
        <nav class="mt-8 px-4 space-y-2">
            <a href="<?= site_url('schooladmin/dashboard') ?>" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium hover:bg-gray-100">
                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-tachometer-alt text-blue-600 text-sm"></i>
                </div>
                <span>Dashboard</span>
            </a>

            <a href="<?= site_url('schooladmin/dashboard') ?>#questions" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium hover:bg-gray-100">
                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-clipboard-question text-yellow-600 text-sm"></i>
                </div>
                <span>Question Review</span>
            </a>

            <a href="<?= site_url('schooladmin/question-papers') ?>" class="nav-item flex items-center px-4 py-3 text-white bg-indigo-600 rounded-lg font-medium">
                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-file-alt text-white text-sm"></i>
                </div>
                <span>Question Papers</span>
            </a>

            <a href="<?= site_url('schooladmin/dashboard') ?>#staff" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium hover:bg-gray-100">
                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-chalkboard-teacher text-green-600 text-sm"></i>
                </div>
                <span>Staff Management</span>
            </a>

            <a href="<?= site_url('schooladmin/dashboard') ?>#subscription" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium hover:bg-gray-100">
                <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-credit-card text-orange-600 text-sm"></i>
                </div>
                <span>Subscription</span>
            </a>

            <a href="<?= site_url('schooladmin/settings') ?>" class="nav-item flex items-center px-4 py-3 text-gray-700 rounded-lg font-medium hover:bg-gray-100">
                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-cog text-purple-600 text-sm"></i>
                </div>
                <span>Settings</span>
            </a>
        </nav>

        <!-- User Profile Section -->
        <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 gradient-bg rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm">A</span>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate"><?= isset($school_name) ? esc($school_name) : 'Admin User' ?></p>
                    <p class="text-xs text-gray-500 truncate">School Administrator</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64 min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center">
                    <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 gradient-bg rounded-full flex items-center justify-center">
                            <i class="fas fa-school text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 id="page-title" class="text-2xl font-bold text-gray-800">Question Papers</h1>
                            <p id="page-subtitle" class="text-sm text-gray-600 mt-1">
                                <?= isset($school_name) ? esc($school_name) : 'School' ?> - Create and manage question papers
                            </p>
                        </div>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <button class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Create New Paper
                    </button>
                </div>
            </div>
        </header>

        <!-- Page Content -->
        <main class="p-6">
            <div class="animate-fade-in">
                <!-- Welcome Banner -->
                <div class="gradient-bg rounded-xl p-6 mb-8 text-white relative overflow-hidden">
                    <div class="absolute inset-0 bg-black opacity-10"></div>
                    <div class="relative">
                        <div class="flex items-center justify-between">
                            <div>
                                <h1 class="text-2xl font-bold mb-2">Question Papers Management</h1>
                                <p class="text-white text-opacity-90">Create professional question papers for <?= isset($school_name) ? esc($school_name) : 'your school' ?></p>
                            </div>
                            <div class="hidden md:block">
                                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-file-alt text-2xl"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200 card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Total Papers</p>
                                <p class="text-3xl font-bold text-gray-800">12</p>
                            </div>
                            <div class="bg-blue-100 p-3 rounded-full">
                                <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200 card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Published</p>
                                <p class="text-3xl font-bold text-gray-800">8</p>
                            </div>
                            <div class="bg-green-100 p-3 rounded-full">
                                <i class="fas fa-check-circle text-2xl text-green-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200 card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Draft</p>
                                <p class="text-3xl font-bold text-gray-800">3</p>
                            </div>
                            <div class="bg-yellow-100 p-3 rounded-full">
                                <i class="fas fa-edit text-2xl text-yellow-600"></i>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200 card-hover">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-gray-500 text-sm">Templates</p>
                                <p class="text-3xl font-bold text-gray-800">5</p>
                            </div>
                            <div class="bg-purple-100 p-3 rounded-full">
                                <i class="fas fa-layer-group text-2xl text-purple-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Question Paper Templates -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 mb-6">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">Question Paper Templates</h3>
                        <p class="text-sm text-gray-600 mt-1">Choose from pre-designed templates to create question papers quickly</p>
                    </div>

                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <!-- Tamil Nadu SSLC Template -->
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-indigo-400 hover:bg-indigo-50 transition-all cursor-pointer template-card" onclick="selectTemplate('tn-sslc')">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-indigo-100 rounded-lg flex items-center justify-center mx-auto mb-4 template-icon">
                                        <i class="fas fa-graduation-cap text-indigo-600 text-2xl"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-800 mb-2">Tamil Nadu SSLC</h4>
                                    <p class="text-sm text-gray-600 mb-4">Official TN Board Class 10 format with subject-specific marking schemes</p>
                                    <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                        <span><i class="fas fa-clock mr-1"></i>2.5 Hours</span>
                                        <span><i class="fas fa-star mr-1"></i>100 Marks</span>
                                    </div>
                                    <div class="space-y-2 text-xs text-gray-600">
                                        <div class="text-center mb-2 font-medium">Subject Options:</div>
                                        <div class="flex justify-between">
                                            <span>Mathematics:</span>
                                            <span>100 Marks</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Science:</span>
                                            <span>100 Marks</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Social Science:</span>
                                            <span>100 Marks</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Tamil/English:</span>
                                            <span>100 Marks</span>
                                        </div>
                                    </div>
                                    <button class="mt-4 w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors">
                                        Use SSLC Template
                                    </button>
                                </div>
                            </div>

                            <!-- Tamil Nadu HSC 1 Template -->
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-green-400 hover:bg-green-50 transition-all cursor-pointer template-card" onclick="selectTemplate('tn-hsc1')">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4 template-icon">
                                        <i class="fas fa-university text-green-600 text-2xl"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-800 mb-2">Tamil Nadu HSC 1</h4>
                                    <p class="text-sm text-gray-600 mb-4">Plus One (Standard 11) format with group-specific and common subjects</p>
                                    <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                        <span><i class="fas fa-clock mr-1"></i>3 Hours</span>
                                        <span><i class="fas fa-star mr-1"></i>100 Marks</span>
                                        <span><i class="fas fa-graduation-cap mr-1"></i>Std 11</span>
                                    </div>
                                    <div class="space-y-2 text-xs text-gray-600">
                                        <div class="text-center mb-2 font-medium">Available Groups:</div>
                                        <div class="flex justify-between">
                                            <span>Common:</span>
                                            <span>Tamil, English</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Science:</span>
                                            <span>Phy, Che, Bio, Math</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Commerce:</span>
                                            <span>Acc, Com, Eco, B.Math</span>
                                        </div>
                                    </div>
                                    <button class="mt-4 w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors">
                                        Use HSC 1 Template
                                    </button>
                                </div>
                            </div>

                            <!-- Tamil Nadu HSC 2 Template -->
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-emerald-400 hover:bg-emerald-50 transition-all cursor-pointer template-card" onclick="selectTemplate('tn-hsc2')">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-emerald-100 rounded-lg flex items-center justify-center mx-auto mb-4 template-icon">
                                        <i class="fas fa-university text-emerald-600 text-2xl"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-800 mb-2">Tamil Nadu HSC 2</h4>
                                    <p class="text-sm text-gray-600 mb-4">Plus Two (Standard 12) format with group-specific and common subjects</p>
                                    <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                        <span><i class="fas fa-clock mr-1"></i>3 Hours</span>
                                        <span><i class="fas fa-star mr-1"></i>100 Marks</span>
                                        <span><i class="fas fa-graduation-cap mr-1"></i>Std 12</span>
                                    </div>
                                    <div class="space-y-2 text-xs text-gray-600">
                                        <div class="text-center mb-2 font-medium">Available Groups:</div>
                                        <div class="flex justify-between">
                                            <span>Common:</span>
                                            <span>Tamil, English</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Science:</span>
                                            <span>Phy, Che, Bio, Math</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Commerce:</span>
                                            <span>Acc, Com, Eco, B.Math</span>
                                        </div>
                                    </div>
                                    <button class="mt-4 w-full bg-emerald-600 text-white py-2 px-4 rounded-lg hover:bg-emerald-700 transition-colors">
                                        Use HSC 2 Template
                                    </button>
                                </div>
                            </div>

                            <!-- Custom Template -->
                            <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-purple-400 hover:bg-purple-50 transition-all template-card">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4 template-icon">
                                        <i class="fas fa-cog text-purple-600 text-2xl"></i>
                                    </div>
                                    <h4 class="text-lg font-semibold text-gray-800 mb-2">Custom Template</h4>
                                    <p class="text-sm text-gray-600 mb-4">Create your own template with custom sections, timing, and marking scheme</p>
                                    <div class="flex items-center justify-center space-x-4 text-xs text-gray-500 mb-4">
                                        <span><i class="fas fa-cog mr-1"></i>Customizable</span>
                                        <span><i class="fas fa-palette mr-1"></i>Flexible</span>
                                    </div>
                                    <div class="space-y-2 text-xs text-gray-600 mb-4">
                                        <div class="flex justify-between">
                                            <span>Duration:</span>
                                            <span>Your Choice</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Sections:</span>
                                            <span>Custom</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Marks:</span>
                                            <span>Flexible</span>
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <button onclick="selectTemplate('custom')" class="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors">
                                            <i class="fas fa-plus mr-2"></i>Create New
                                        </button>
                                        <button onclick="showSavedCustomTemplates()" class="w-full bg-purple-100 text-purple-700 py-2 px-4 rounded-lg hover:bg-purple-200 transition-colors">
                                            <i class="fas fa-folder-open mr-2"></i>Saved Templates
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Question Papers -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">Recent Question Papers</h3>
                                <p class="text-sm text-gray-600 mt-1">View and manage your created question papers</p>
                            </div>
                            <div class="flex space-x-2">
                                <select class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                    <option>All Papers</option>
                                    <option>Published</option>
                                    <option>Draft</option>
                                    <option>Archived</option>
                                </select>
                                <button class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50">
                                    <i class="fas fa-filter mr-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-file-alt text-2xl text-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Question Papers Yet</h3>
                            <p class="text-gray-500 mb-6">Start by selecting a template above to create your first question paper.</p>
                            <button onclick="selectTemplate('custom')" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                                <i class="fas fa-plus mr-2"></i>Create First Paper
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        console.log('Question Papers module loaded and ready for development');

        // Load question papers when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadQuestionPapersList();
        });

        // Basic initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Question Papers page initialized');
        });

        // Template Selection Functions
        function selectTemplate(templateType) {
            console.log('Selected template:', templateType);

            // Show notification based on template type
            switch(templateType) {
                case 'tn-sslc':
                    showNotification('Tamil Nadu SSLC template selected! Ready for question paper creation.', 'success');
                    loadSSLCTemplate();
                    break;
                case 'tn-hsc1':
                    showNotification('Tamil Nadu HSC 1 (Plus One) template selected! Ready for question paper creation.', 'success');
                    loadHSC1Template();
                    break;
                case 'tn-hsc2':
                    showNotification('Tamil Nadu HSC 2 (Plus Two) template selected! Ready for question paper creation.', 'success');
                    loadHSC2Template();
                    break;
                case 'custom':
                    showNotification('Custom template selected! You can design your own format.', 'info');
                    loadCustomTemplate();
                    break;
                default:
                    showNotification('Please select a valid template.', 'error');
            }
        }

        function loadSSLCTemplate(skipModal = false) {
            console.log('Loading SSLC Template...');

            // Subject-specific templates for Tamil Nadu SSLC
            window.sslcTemplates = {
                mathematics: {
                    duration: '2.5 Hours',
                    totalMarks: 100,
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 14, marksEach: 1, totalMarks: 14, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Short Answer II', questions: 10, marksEach: 5, totalMarks: 50, instruction: 'Answer any 10 questions' },
                        { name: 'Part IV', type: 'Essay Type', questions: 4, marksEach: 8, totalMarks: 32, instruction: 'Answer all questions' }
                    ]
                },
                science: {
                    duration: '2.5 Hours',
                    totalMarks: 100,
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 12, marksEach: 1, totalMarks: 12, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 12, marksEach: 2, totalMarks: 24, instruction: 'Answer any 7 questions' },
                        { name: 'Part III', type: 'Short Answer II', questions: 12, marksEach: 4, totalMarks: 48, instruction: 'Answer any 7 questions' },
                        { name: 'Part IV', type: 'Long Answer', questions: 4, marksEach: 7, totalMarks: 28, instruction: 'Answer all questions' }
                    ]
                },
                socialScience: {
                    duration: '2.5 Hours',
                    totalMarks: 100,
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 12, marksEach: 1, totalMarks: 12, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 12, marksEach: 2, totalMarks: 24, instruction: 'Answer any 7 questions' },
                        { name: 'Part III', type: 'Short Answer II', questions: 12, marksEach: 4, totalMarks: 48, instruction: 'Answer any 7 questions' },
                        { name: 'Part IV', type: 'Essay Type', questions: 4, marksEach: 7, totalMarks: 28, instruction: 'Answer all questions' }
                    ]
                },
                tamil: {
                    duration: '2.5 Hours',
                    totalMarks: 100,
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Creative', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                },
                english: {
                    duration: '2.5 Hours',
                    totalMarks: 100,
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Letter', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                }
            };

            // Here you would typically show a subject selection modal
            console.log('SSLC Templates by Subject:', window.sslcTemplates);
            if (!skipModal) {
                showSubjectSelectionModal('sslc', window.sslcTemplates);
            }
        }

        function loadHSC1Template(skipModal = false) {
            console.log('Loading HSC 1 (Plus One) Template...');

            // Complete subject-specific templates for Tamil Nadu HSC 1 (Plus One - Standard 11)
            // Based on different group combinations available
            window.hsc1Templates = {
                // Science Stream Subjects
                mathematics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 20, marksEach: 1, totalMarks: 20, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 7, marksEach: 2, totalMarks: 14, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part III', type: 'Short Answer', questions: 7, marksEach: 3, totalMarks: 21, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part IV', type: 'Long Answer', questions: 7, marksEach: 5, totalMarks: 35, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part V', type: 'Long Answer', questions: 2, marksEach: 10, totalMarks: 20, instruction: 'Answer any 2 questions out of 3' }
                    ]
                },
                physics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                chemistry: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                biology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                statistics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science/Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                computerScience: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science/Commerce/Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                biochemistry: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                botany: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                zoology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                // Commerce Stream Subjects
                accountancy: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                commerce: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                economics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                businessMaths: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 20, marksEach: 1, totalMarks: 20, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 7, marksEach: 2, totalMarks: 14, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part III', type: 'Short Answer', questions: 7, marksEach: 3, totalMarks: 21, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part IV', type: 'Long Answer', questions: 7, marksEach: 5, totalMarks: 35, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part V', type: 'Long Answer', questions: 2, marksEach: 10, totalMarks: 20, instruction: 'Answer any 2 questions out of 3' }
                    ]
                },
                communicativeEnglish: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Letter', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                },
                // Arts & Humanities Stream Subjects
                history: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts/Commerce',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                geography: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                politicalScience: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                advancedTamil: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Creative', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                },
                // Vocational Stream Subjects
                homeScience: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                basicMechanicalEngineering: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                computerTechnology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                nursing: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                textileAndDressDesigning: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                officeManagementAndSecretaryship: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                // Common/Compulsory Subjects (for all groups)
                tamil: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Common/Compulsory',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Creative', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                },
                english: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Common/Compulsory',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Letter', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                }
            };

            // Here you would typically show a subject selection modal
            console.log('HSC 1 Templates by Subject:', window.hsc1Templates);
            if (!skipModal) {
                showSubjectSelectionModal('hsc1', window.hsc1Templates);
            }
        }

        function loadHSC2Template(skipModal = false) {
            console.log('Loading HSC 2 (Plus Two) Template...');

            // Complete subject-specific templates for Tamil Nadu HSC 2 (Plus Two - Standard 12)
            // Based on different group combinations available
            window.hsc2Templates = {
                // Science Stream Subjects
                mathematics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 20, marksEach: 1, totalMarks: 20, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 7, marksEach: 2, totalMarks: 14, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part III', type: 'Short Answer', questions: 7, marksEach: 3, totalMarks: 21, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part IV', type: 'Long Answer', questions: 7, marksEach: 5, totalMarks: 35, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part V', type: 'Essay Type', questions: 2, marksEach: 5, totalMarks: 10, instruction: 'Answer all questions' }
                    ]
                },
                physics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 6, marksEach: 2, totalMarks: 12, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part III', type: 'Short Answer', questions: 6, marksEach: 3, totalMarks: 18, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Essay Type', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                chemistry: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 6, marksEach: 2, totalMarks: 12, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part III', type: 'Short Answer', questions: 6, marksEach: 3, totalMarks: 18, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Essay Type', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                biology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 6, marksEach: 2, totalMarks: 12, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part III', type: 'Short Answer', questions: 6, marksEach: 3, totalMarks: 18, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Essay Type', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                biology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 6, marksEach: 2, totalMarks: 12, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part III', type: 'Short Answer', questions: 6, marksEach: 3, totalMarks: 18, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Essay Type', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                statistics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science/Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                computerScience: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science/Commerce/Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                biochemistry: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                botany: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                zoology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Science',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                // Commerce Stream Subjects
                accountancy: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                commerce: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                businessStudies: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 6, marksEach: 2, totalMarks: 12, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part III', type: 'Short Answer', questions: 6, marksEach: 3, totalMarks: 18, instruction: 'Answer any 6 questions out of 9' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Essay Type', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                economics: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                businessMaths: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 20, marksEach: 1, totalMarks: 20, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 7, marksEach: 2, totalMarks: 14, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part III', type: 'Short Answer', questions: 7, marksEach: 3, totalMarks: 21, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part IV', type: 'Long Answer', questions: 7, marksEach: 5, totalMarks: 35, instruction: 'Answer any 7 questions out of 10' },
                        { name: 'Part V', type: 'Long Answer', questions: 2, marksEach: 10, totalMarks: 20, instruction: 'Answer any 2 questions out of 3' }
                    ]
                },
                communicativeEnglish: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Commerce/Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Letter', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                },
                // Arts Stream Subjects
                history: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                geography: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                politicalScience: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                psychology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                sociology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                advancedTamil: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Arts',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Creative', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                },
                // Vocational Subjects
                homeScience: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                basicMechanicalEngineering: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                computerTechnology: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                nursing: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                textileAndDressDesigning: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                officeManagementAndSecretaryship: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Vocational',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer I', questions: 9, marksEach: 2, totalMarks: 18, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part III', type: 'Short Answer II', questions: 9, marksEach: 3, totalMarks: 27, instruction: 'Answer any 9 questions out of 12' },
                        { name: 'Part IV', type: 'Long Answer', questions: 5, marksEach: 5, totalMarks: 25, instruction: 'Answer any 5 questions out of 7' },
                        { name: 'Part V', type: 'Long Answer', questions: 3, marksEach: 7, totalMarks: 21, instruction: 'Answer any 3 questions out of 4' }
                    ]
                },
                // Language Subjects
                tamil: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Language',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Letter', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                },
                english: {
                    duration: '3 Hours',
                    totalMarks: 100,
                    group: 'Language',
                    sections: [
                        { name: 'Part I', type: 'Multiple Choice', questions: 15, marksEach: 1, totalMarks: 15, instruction: 'Choose the correct answer' },
                        { name: 'Part II', type: 'Short Answer', questions: 10, marksEach: 2, totalMarks: 20, instruction: 'Answer any 10 questions' },
                        { name: 'Part III', type: 'Paragraph', questions: 8, marksEach: 5, totalMarks: 40, instruction: 'Answer any 8 questions' },
                        { name: 'Part IV', type: 'Essay/Letter', questions: 3, marksEach: 10, totalMarks: 30, instruction: 'Answer all questions' }
                    ]
                }
            };

            // Here you would typically show a subject selection modal
            console.log('HSC 2 Templates by Subject:', window.hsc2Templates);
            if (!skipModal) {
                showSubjectSelectionModal('hsc2', window.hsc2Templates);
            }
        }

        function loadCustomTemplate() {
            console.log('Loading Custom Template...');
            showCustomTemplateBuilder();
        }

        function showCustomTemplateBuilder() {
            let modalHTML = `
                <div id="customTemplateModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-y-auto">
                        <div class="px-6 py-4 border-b border-gray-200 gradient-bg text-white">
                            <h3 class="text-xl font-bold">Create Custom Template</h3>
                            <p class="text-white text-opacity-90 text-sm">Design your own question paper format with custom sections and marking scheme</p>
                        </div>

                        <div class="p-6">
                            <!-- Template Basic Info -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Template Name</label>
                                    <input type="text" id="customTemplateName" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                           placeholder="e.g., My School Format" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Duration (Hours)</label>
                                    <input type="number" id="customDuration" step="0.5" min="0.5" max="6" value="3"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" required>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Total Marks</label>
                                    <input type="number" id="customTotalMarks" min="1" max="200" value="100"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" required>
                                </div>
                            </div>

                            <!-- Sections Builder -->
                            <div class="mb-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-semibold text-gray-800">Template Sections</h4>
                                    <button type="button" onclick="addCustomSection()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                                        <i class="fas fa-plus mr-2"></i>Add Section
                                    </button>
                                </div>

                                <div id="customSections" class="space-y-4">
                                    <!-- Default sections will be added here -->
                                </div>
                            </div>

                            <!-- Template Preview -->
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-gray-800 mb-3">Template Preview</h4>
                                <div id="templatePreview" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                                    <!-- Preview will be generated here -->
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex justify-end space-x-3">
                                <button type="button" onclick="closeCustomTemplateModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    Cancel
                                </button>
                                <button type="button" onclick="saveCustomTemplate()" class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                    <i class="fas fa-save mr-2"></i>Create Template
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Initialize with default sections
            addCustomSection('Section A', 'multiple_choice', 20, 1, 'Choose the correct answer');
            addCustomSection('Section B', 'short_answer', 30, 2, 'Answer briefly');
            addCustomSection('Section C', 'long_answer', 50, 5, 'Answer in detail');

            updateTemplatePreview();
        }

        function addCustomSection(name = '', type = 'multiple_choice', totalMarks = 20, marksEach = 1, instructions = '') {
            const sectionsContainer = document.getElementById('customSections');
            const sectionIndex = sectionsContainer.children.length;

            const sectionHTML = `
                <div class="border border-gray-200 rounded-lg p-4 custom-section" data-section-index="${sectionIndex}">
                    <div class="flex items-center justify-between mb-4">
                        <h5 class="font-medium text-gray-800">Section ${sectionIndex + 1}</h5>
                        <button type="button" onclick="removeCustomSection(${sectionIndex})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Section Name</label>
                            <input type="text" class="section-name w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                   value="${name}" placeholder="e.g., Section A" onchange="updateTemplatePreview()" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Question Type</label>
                            <select class="section-type w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500" onchange="updateTemplatePreview()">
                                <option value="multiple_choice" ${type === 'multiple_choice' ? 'selected' : ''}>Multiple Choice</option>
                                <option value="short_answer" ${type === 'short_answer' ? 'selected' : ''}>Short Answer</option>
                                <option value="long_answer" ${type === 'long_answer' ? 'selected' : ''}>Long Answer</option>
                                <option value="essay" ${type === 'essay' ? 'selected' : ''}>Essay</option>
                                <option value="fill_blank" ${type === 'fill_blank' ? 'selected' : ''}>Fill in the Blanks</option>
                                <option value="true_false" ${type === 'true_false' ? 'selected' : ''}>True/False</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Total Marks</label>
                            <input type="number" class="section-total-marks w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                   value="${totalMarks}" min="1" max="100" onchange="updateTemplatePreview()" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Marks Each</label>
                            <input type="number" class="section-marks-each w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                   value="${marksEach}" min="1" max="20" onchange="updateTemplatePreview()" required>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Instructions</label>
                        <input type="text" class="section-instructions w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                               value="${instructions}" placeholder="Instructions for this section" onchange="updateTemplatePreview()">
                    </div>
                </div>
            `;

            sectionsContainer.insertAdjacentHTML('beforeend', sectionHTML);
            updateTemplatePreview();
        }

        function removeCustomSection(sectionIndex) {
            const section = document.querySelector(`[data-section-index="${sectionIndex}"]`);
            if (section) {
                section.remove();
                // Reindex remaining sections
                const sections = document.querySelectorAll('.custom-section');
                sections.forEach((section, index) => {
                    section.setAttribute('data-section-index', index);
                    const title = section.querySelector('h5');
                    if (title) title.textContent = `Section ${index + 1}`;
                    const removeBtn = section.querySelector('button[onclick*="removeCustomSection"]');
                    if (removeBtn) removeBtn.setAttribute('onclick', `removeCustomSection(${index})`);
                });
                updateTemplatePreview();
            }
        }

        function updateTemplatePreview() {
            const previewContainer = document.getElementById('templatePreview');
            if (!previewContainer) return;

            const templateName = document.getElementById('customTemplateName')?.value || 'Custom Template';
            const duration = document.getElementById('customDuration')?.value || '3';
            const totalMarks = document.getElementById('customTotalMarks')?.value || '100';

            const sections = [];
            let calculatedTotalMarks = 0;

            document.querySelectorAll('.custom-section').forEach((sectionEl, index) => {
                const name = sectionEl.querySelector('.section-name')?.value || `Section ${index + 1}`;
                const type = sectionEl.querySelector('.section-type')?.value || 'multiple_choice';
                const sectionTotalMarks = parseInt(sectionEl.querySelector('.section-total-marks')?.value) || 20;
                const marksEach = parseInt(sectionEl.querySelector('.section-marks-each')?.value) || 1;
                const instructions = sectionEl.querySelector('.section-instructions')?.value || '';

                calculatedTotalMarks += sectionTotalMarks;

                sections.push({
                    name,
                    type,
                    totalMarks: sectionTotalMarks,
                    marksEach,
                    instructions,
                    questionsCount: Math.ceil(sectionTotalMarks / marksEach)
                });
            });

            let previewHTML = `
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h5 class="font-semibold text-gray-800">${templateName}</h5>
                        <div class="text-sm text-gray-600">
                            <span class="mr-4">Duration: ${duration} Hours</span>
                            <span>Total Marks: ${totalMarks}</span>
                        </div>
                    </div>

                    ${calculatedTotalMarks !== parseInt(totalMarks) ?
                        `<div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                                <span class="text-sm text-yellow-800">
                                    Warning: Section marks total (${calculatedTotalMarks}) doesn't match template total marks (${totalMarks})
                                </span>
                            </div>
                        </div>` : ''
                    }

                    <div class="space-y-3">
            `;

            sections.forEach((section, index) => {
                previewHTML += `
                    <div class="bg-white border border-gray-200 rounded-lg p-3">
                        <div class="flex items-center justify-between mb-2">
                            <h6 class="font-medium text-gray-800">${section.name}</h6>
                            <span class="text-sm text-gray-600">${section.totalMarks} marks</span>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>Type: ${section.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}</div>
                            <div>Questions: ~${section.questionsCount} (${section.marksEach} mark${section.marksEach > 1 ? 's' : ''} each)</div>
                            ${section.instructions ? `<div>Instructions: ${section.instructions}</div>` : ''}
                        </div>
                    </div>
                `;
            });

            previewHTML += `
                    </div>
                </div>
            `;

            previewContainer.innerHTML = previewHTML;
        }

        function saveCustomTemplate() {
            const templateName = document.getElementById('customTemplateName')?.value?.trim();
            const duration = document.getElementById('customDuration')?.value;
            const totalMarks = document.getElementById('customTotalMarks')?.value;

            if (!templateName) {
                showNotification('Please enter a template name', 'error');
                return;
            }

            const sections = [];
            let isValid = true;

            document.querySelectorAll('.custom-section').forEach((sectionEl, index) => {
                const name = sectionEl.querySelector('.section-name')?.value?.trim();
                const type = sectionEl.querySelector('.section-type')?.value;
                const sectionTotalMarks = parseInt(sectionEl.querySelector('.section-total-marks')?.value);
                const marksEach = parseInt(sectionEl.querySelector('.section-marks-each')?.value);
                const instructions = sectionEl.querySelector('.section-instructions')?.value?.trim();

                if (!name || !sectionTotalMarks || !marksEach) {
                    isValid = false;
                    return;
                }

                sections.push({
                    name,
                    type,
                    totalMarks: sectionTotalMarks,
                    marksEach,
                    instructions,
                    questionsCount: Math.ceil(sectionTotalMarks / marksEach)
                });
            });

            if (!isValid) {
                showNotification('Please fill in all required section fields', 'error');
                return;
            }

            if (sections.length === 0) {
                showNotification('Please add at least one section', 'error');
                return;
            }

            // Create the custom template object
            const customTemplate = {
                name: templateName,
                duration: `${duration} Hours`,
                totalMarks: parseInt(totalMarks),
                sections: sections,
                type: 'custom',
                created: new Date().toISOString()
            };

            // Store in localStorage for persistence
            let savedTemplates = JSON.parse(localStorage.getItem('customTemplates') || '[]');
            savedTemplates.push(customTemplate);
            localStorage.setItem('customTemplates', JSON.stringify(savedTemplates));

            // Set as selected template
            window.selectedTemplate = {
                subject: 'custom',
                type: 'custom',
                template: customTemplate
            };

            closeCustomTemplateModal();
            showNotification(`Custom template "${templateName}" created successfully!`, 'success');

            // Show question paper creation form
            showQuestionPaperForm('custom', 'custom');
        }

        function closeCustomTemplateModal() {
            const modal = document.getElementById('customTemplateModal');
            if (modal) {
                modal.remove();
            }
        }

        function loadSavedCustomTemplates() {
            return JSON.parse(localStorage.getItem('customTemplates') || '[]');
        }

        function showSavedCustomTemplates() {
            const savedTemplates = loadSavedCustomTemplates();

            if (savedTemplates.length === 0) {
                showNotification('No saved custom templates found', 'info');
                return;
            }

            let modalHTML = `
                <div id="savedTemplatesModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                        <div class="px-6 py-4 border-b border-gray-200 gradient-bg text-white">
                            <h3 class="text-xl font-bold">Saved Custom Templates</h3>
                            <p class="text-white text-opacity-90 text-sm">Choose from your previously created templates</p>
                        </div>

                        <div class="p-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            `;

            savedTemplates.forEach((template, index) => {
                modalHTML += `
                    <div class="border border-gray-200 rounded-lg p-4 hover:border-purple-500 hover:bg-purple-50 cursor-pointer transition-all" onclick="selectSavedTemplate(${index})">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">${template.name}</h4>
                            <button onclick="event.stopPropagation(); deleteSavedTemplate(${index})" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        </div>
                        <div class="text-sm text-gray-600 space-y-1 mb-3">
                            <div>Duration: ${template.duration}</div>
                            <div>Total Marks: ${template.totalMarks}</div>
                            <div>Sections: ${template.sections.length}</div>
                            <div>Created: ${new Date(template.created).toLocaleDateString()}</div>
                        </div>
                        <div class="space-y-1">
                            ${template.sections.map(section =>
                                `<div class="text-xs text-gray-500 flex justify-between">
                                    <span>${section.name}</span>
                                    <span>${section.totalMarks} marks</span>
                                </div>`
                            ).join('')}
                        </div>
                    </div>
                `;
            });

            modalHTML += `
                            </div>
                            <div class="flex justify-end mt-6">
                                <button onclick="closeSavedTemplatesModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function selectSavedTemplate(index) {
            const savedTemplates = loadSavedCustomTemplates();
            const template = savedTemplates[index];

            if (!template) {
                showNotification('Template not found', 'error');
                return;
            }

            // Set as selected template
            window.selectedTemplate = {
                subject: 'custom',
                type: 'custom',
                template: template
            };

            closeSavedTemplatesModal();
            showNotification(`Template "${template.name}" selected!`, 'success');

            // Show question paper creation form
            showQuestionPaperForm('custom', 'custom');
        }

        function deleteSavedTemplate(index) {
            if (confirm('Are you sure you want to delete this template?')) {
                let savedTemplates = loadSavedCustomTemplates();
                const templateName = savedTemplates[index]?.name;
                savedTemplates.splice(index, 1);
                localStorage.setItem('customTemplates', JSON.stringify(savedTemplates));

                showNotification(`Template "${templateName}" deleted`, 'success');

                // Refresh the modal
                closeSavedTemplatesModal();
                setTimeout(() => showSavedCustomTemplates(), 100);
            }
        }

        function closeSavedTemplatesModal() {
            const modal = document.getElementById('savedTemplatesModal');
            if (modal) {
                modal.remove();
            }
        }

        // Subject Selection Modal
        function showSubjectSelectionModal(templateType, templates) {
            // Group subjects by category for HSC1 and HSC2
            const groupedSubjects = {};
            if (templateType === 'hsc' || templateType === 'hsc1' || templateType === 'hsc2') {
                Object.keys(templates).forEach(subject => {
                    const group = templates[subject].group || 'Other';
                    if (!groupedSubjects[group]) {
                        groupedSubjects[group] = [];
                    }
                    groupedSubjects[group].push(subject);
                });
            } else {
                groupedSubjects['All Subjects'] = Object.keys(templates);
            }

            let modalHTML = `
                <div id="subjectModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
                        <div class="px-6 py-4 border-b border-gray-200 gradient-bg text-white">
                            <h3 class="text-xl font-bold">Select Subject for ${templateType.toUpperCase()} Template</h3>
                            <p class="text-white text-opacity-90 text-sm">Choose the subject to see specific question paper format</p>
                        </div>
                        <div class="p-6">
            `;

            // Create tabs for different groups (HSC, HSC1, HSC2)
            if (templateType === 'hsc' || templateType === 'hsc1' || templateType === 'hsc2') {
                modalHTML += `
                    <div class="mb-6">
                        <div class="flex flex-wrap gap-2 border-b border-gray-200">
                `;

                Object.keys(groupedSubjects).forEach((group, index) => {
                    const isActive = index === 0 ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700';
                    modalHTML += `
                        <button class="px-4 py-2 border-b-2 font-medium text-sm ${isActive}" onclick="showGroupTab('${group}', this)">
                            ${group} Group
                        </button>
                    `;
                });

                modalHTML += `
                        </div>
                    </div>
                `;
            }

            // Create subject cards for each group
            Object.keys(groupedSubjects).forEach((group, groupIndex) => {
                const isVisible = groupIndex === 0 ? '' : 'hidden';
                modalHTML += `
                    <div id="group-${group}" class="group-content ${isVisible}">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                `;

                groupedSubjects[group].forEach(subject => {
                    const template = templates[subject];
                    const subjectName = subject ? (subject.charAt(0).toUpperCase() + subject.slice(1).replace(/([A-Z])/g, ' $1')) : 'Unknown Subject';
                    const groupBadgeColor = getGroupBadgeColor(template.group);

                    modalHTML += `
                        <div class="border border-gray-300 rounded-lg p-4 hover:border-indigo-500 hover:bg-indigo-50 cursor-pointer transition-all" onclick="selectSubjectTemplate('${subject}', '${templateType}')">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-semibold text-gray-800">${subjectName}</h4>
                                ${template.group ? `<span class="px-2 py-1 text-xs rounded-full ${groupBadgeColor}">${template.group}</span>` : ''}
                            </div>
                            <div class="text-sm text-gray-600 space-y-1 mb-3">
                                <p><strong>Duration:</strong> ${template.duration}</p>
                                <p><strong>Total Marks:</strong> ${template.totalMarks}</p>
                                <p><strong>Sections:</strong> ${template.sections.length}</p>
                            </div>
                            <div class="space-y-1 text-xs text-gray-500 mb-3">
                    `;

                    template.sections.forEach(section => {
                        modalHTML += `<div class="flex justify-between"><span>${section.name}:</span><span>${section.totalMarks} marks</span></div>`;
                    });

                    modalHTML += `
                            </div>
                            <button class="w-full bg-indigo-600 text-white py-2 px-3 rounded text-sm hover:bg-indigo-700 transition-colors">
                                Select ${subjectName}
                            </button>
                        </div>
                    `;
                });

                modalHTML += `
                        </div>
                    </div>
                `;
            });

            modalHTML += `
                            <div class="mt-6 text-center">
                                <button onclick="closeSubjectModal()" class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function getGroupBadgeColor(group) {
            switch(group) {
                case 'Science': return 'bg-green-100 text-green-800';
                case 'Commerce': return 'bg-blue-100 text-blue-800';
                case 'Arts': return 'bg-purple-100 text-purple-800';
                case 'Vocational': return 'bg-orange-100 text-orange-800';
                case 'Common/Compulsory': return 'bg-red-100 text-red-800';
                case 'Language': return 'bg-yellow-100 text-yellow-800';
                case 'Science/Arts': return 'bg-emerald-100 text-emerald-800';
                case 'Science/Commerce': return 'bg-teal-100 text-teal-800';
                case 'Commerce/Arts': return 'bg-indigo-100 text-indigo-800';
                case 'Arts/Commerce': return 'bg-violet-100 text-violet-800';
                case 'Commerce/Vocational': return 'bg-cyan-100 text-cyan-800';
                case 'Science/Commerce/Vocational': return 'bg-pink-100 text-pink-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        }

        function showGroupTab(groupName, tabElement) {
            // Hide all group contents
            document.querySelectorAll('.group-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Show selected group content
            document.getElementById(`group-${groupName}`).classList.remove('hidden');

            // Update tab styles
            document.querySelectorAll('#subjectModal button[onclick*="showGroupTab"]').forEach(tab => {
                tab.classList.remove('border-indigo-500', 'text-indigo-600');
                tab.classList.add('border-transparent', 'text-gray-500');
            });

            tabElement.classList.remove('border-transparent', 'text-gray-500');
            tabElement.classList.add('border-indigo-500', 'text-indigo-600');
        }

        function selectSubjectTemplate(subject, templateType) {
            console.log(`Selected ${subject} template for ${templateType}`);
            closeSubjectModal();
            showNotification(`${subject ? (subject.charAt(0).toUpperCase() + subject.slice(1)) : 'Subject'} template selected! Ready to create question paper.`, 'success');

            // Store selected template data
            let template;
            if (templateType === 'sslc') {
                template = window.sslcTemplates[subject];
            } else if (templateType === 'hsc1') {
                template = window.hsc1Templates[subject];
            } else if (templateType === 'hsc2') {
                template = window.hsc2Templates[subject];
            } else {
                template = window.hscTemplates[subject]; // fallback for old hsc
            }

            window.selectedTemplate = {
                subject: subject,
                type: templateType,
                template: template
            };

            // Show question paper creation form
            showQuestionPaperForm(subject, templateType);
        }

        function showQuestionPaperForm(subject, templateType) {
            const template = window.selectedTemplate.template;
            let subjectName, displayTitle;

            if (templateType === 'custom') {
                subjectName = template.name || 'Custom Template';
                displayTitle = `Create Question Paper - ${subjectName}`;
            } else {
                subjectName = subject ? (subject.charAt(0).toUpperCase() + subject.slice(1).replace(/([A-Z])/g, ' $1')) : 'Unknown Subject';
                displayTitle = `Create ${templateType.toUpperCase()} Question Paper - ${subjectName}`;
            }

            let formHTML = `
                <div id="questionPaperForm" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[95vh] overflow-y-auto">
                        <div class="px-6 py-4 border-b border-gray-200 gradient-bg text-white">
                            <h3 class="text-xl font-bold">${displayTitle}</h3>
                            <p class="text-white text-opacity-90 text-sm">Duration: ${template.duration} | Total Marks: ${template.totalMarks}</p>
                        </div>

                        <form id="createQuestionPaperForm" class="p-6">
                            <!-- Basic Information -->
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4">Basic Information</h4>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Paper Title</label>
                                        <input type="text" id="paperTitle" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                               value="${templateType === 'custom' ? subjectName + ' - Question Paper' : templateType.toUpperCase() + ' ' + subjectName + ' - Question Paper'}" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                                        <input type="text" id="academicYear" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                               value="2024-2025" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Exam Type</label>
                                        <select id="examType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                                            <option value="quarterly">Quarterly Exam</option>
                                            <option value="half-yearly">Half Yearly Exam</option>
                                            <option value="annual">Annual Exam</option>
                                            <option value="model">Model Exam</option>
                                            <option value="practice">Practice Test</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                                        <input type="date" id="examDate" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" value="<?= date('Y-m-d', strtotime('+7 days')) ?>" required>
                                    </div>
                                    ${templateType === 'custom' ? `
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Standard</label>
                                        <select id="standardSelect" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500" required>
                                            <option value="1">Standard 1</option>
                                            <option value="2">Standard 2</option>
                                            <option value="3">Standard 3</option>
                                            <option value="4">Standard 4</option>
                                            <option value="5">Standard 5</option>
                                            <option value="6">Standard 6</option>
                                            <option value="7">Standard 7</option>
                                            <option value="8">Standard 8</option>
                                            <option value="9">Standard 9</option>
                                            <option value="10">Standard 10</option>
                                            <option value="11">Standard 11</option>
                                            <option value="12" selected>Standard 12</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                                        <input type="text" id="subjectInput" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                               placeholder="Enter subject name" required>
                                    </div>
                                    ` : ''}
                                </div>
                            </div>

                            <!-- Question Selection by Section -->
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4">Question Selection</h4>
                                <div class="space-y-6">
            `;

            // Generate sections
            template.sections.forEach((section, index) => {
                formHTML += `
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-4">
                            <h5 class="text-md font-semibold text-gray-800">${section.name} - ${section.type}</h5>
                            <div class="text-sm text-gray-600">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">${section.questions} Questions</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded ml-2">${section.totalMarks} Marks</span>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4"><strong>Instructions:</strong> ${section.instruction}</p>

                        <div class="grid grid-cols-1 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Select Questions for ${section.name}</label>
                                <div class="border border-gray-300 rounded-lg p-4 bg-gray-50">
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-sm font-medium text-gray-700">Available Questions</span>
                                        <button type="button" onclick="loadQuestionsForSection('${subject}', '${section.name}', ${index})"
                                                class="px-3 py-1 bg-indigo-600 text-white text-sm rounded hover:bg-indigo-700">
                                            Load Questions
                                        </button>
                                    </div>
                                    <div id="questions-section-${index}" class="space-y-2">
                                        <p class="text-sm text-gray-500 text-center py-4">Click "Load Questions" to see available questions for this section</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            formHTML += `
                                </div>
                            </div>

                            <!-- Question Paper Summary -->
                            <div class="mb-6">
                                <div class="bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-200 rounded-lg p-4">
                                    <h4 class="text-lg font-semibold text-gray-800 mb-3">Question Paper Summary</h4>
                                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-indigo-600" id="total-questions-display">0</div>
                                            <div class="text-sm text-gray-600">Total Questions</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-green-600" id="total-marks-display">0</div>
                                            <div class="text-sm text-gray-600">Total Marks</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-blue-600" id="duration-display">${template.duration}</div>
                                            <div class="text-sm text-gray-600">Duration</div>
                                        </div>
                                        <div class="text-center">
                                            <div class="text-2xl font-bold text-purple-600">${template.totalMarks}</div>
                                            <div class="text-sm text-gray-600">Template Max</div>
                                        </div>
                                    </div>
                                    <div class="mt-3 text-center">
                                        <div class="text-sm text-gray-600">
                                            <span class="inline-block px-3 py-1 bg-white rounded-full shadow-sm">
                                                Progress: <span id="marks-percentage">0</span>% of template marks
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Paper Settings -->
                            <div class="mb-6">
                                <h4 class="text-lg font-semibold text-gray-800 mb-4">Paper Settings</h4>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Exam Duration (Hours)</label>
                                        <input type="number" id="examDuration" step="0.5" min="0.5" max="6"
                                               value="${parseFloat(template.duration.replace(' Hours', ''))}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                               placeholder="Enter duration in hours" onchange="updateDurationDisplay()">
                                        <p class="text-xs text-gray-500 mt-1">Default: ${template.duration}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                                        <input type="text" id="academicYear"
                                               value="${new Date().getFullYear()}-${new Date().getFullYear() + 1}"
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                               placeholder="e.g., 2024-2025">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Exam Type</label>
                                        <select id="examType" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                            <option value="Unit Test">Unit Test</option>
                                            <option value="Quarterly Exam">Quarterly Exam</option>
                                            <option value="Half Yearly Exam">Half Yearly Exam</option>
                                            <option value="Annual Exam" selected>Annual Exam</option>
                                            <option value="Model Exam">Model Exam</option>
                                            <option value="Practice Test">Practice Test</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Instructions to Students</label>
                                        <textarea id="instructions" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                                  placeholder="Enter general instructions for students...">1. All questions are compulsory.
2. Read the questions carefully before answering.
3. Time allowed: ${template.duration}
4. Maximum marks: ${template.totalMarks}</textarea>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Additional Notes</label>
                                        <textarea id="additionalNotes" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                                  placeholder="Any additional notes or instructions..."></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                                <button type="button" onclick="closeQuestionPaperForm()"
                                        class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    Cancel
                                </button>
                                <div class="space-x-3">
                                    <button type="button" onclick="previewQuestionPaper()"
                                            class="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                                        Preview
                                    </button>
                                    <button type="submit"
                                            class="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                                        Create Question Paper
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', formHTML);

            // Add form submission handler
            document.getElementById('createQuestionPaperForm').addEventListener('submit', handleQuestionPaperSubmission);
        }

        function closeSubjectModal() {
            const modal = document.getElementById('subjectModal');
            if (modal) {
                modal.remove();
            }
        }

        function closeQuestionPaperForm() {
            const form = document.getElementById('questionPaperForm');
            if (form) {
                // Reset editing mode
                window.editingPaperId = null;

                // Clear selected template
                window.selectedTemplate = null;

                // Remove editing indicator if it exists
                const editingIndicator = document.getElementById('editing-indicator');
                if (editingIndicator) {
                    editingIndicator.remove();
                }

                form.remove();
            }
        }

        function loadQuestionsForSection(subject, sectionName, sectionIndex) {
            console.log('Loading questions for section:', {
                subject,
                sectionName,
                sectionIndex
            });

            // Convert template subject key to database subject name
            const subjectMapping = {
                'mathematics': 'Mathematics',
                'science': 'Science',
                'socialScience': 'Social Science',
                'tamil': 'Tamil',
                'english': 'English',
                'computerScience': 'Computer Science',
                'computerTechnology': 'Computer Technology',
                'physics': 'Physics',
                'chemistry': 'Chemistry',
                'biology': 'Biology',
                'statistics': 'Statistics',
                'biochemistry': 'Biochemistry',
                'botany': 'Botany',
                'zoology': 'Zoology',
                'accountancy': 'Accountancy',
                'commerce': 'Commerce',
                'businessStudies': 'Business Studies',
                'economics': 'Economics',
                'businessMaths': 'Business Mathematics',
                'communicativeEnglish': 'Communicative English',
                'history': 'History',
                'geography': 'Geography',
                'politicalScience': 'Political Science',
                'psychology': 'Psychology',
                'sociology': 'Sociology',
                'advancedTamil': 'Advanced Tamil',
                'homeScience': 'Home Science',
                'basicMechanicalEngineering': 'Basic Mechanical Engineering',
                'nursing': 'Nursing',
                'textileAndDressDesigning': 'Textile and Dress Designing',
                'officeManagementAndSecretaryship': 'Office Management and Secretaryship'
            };

            const dbSubject = subjectMapping[subject] || subject;
            console.log('Mapped subject:', subject, '->', dbSubject);

            const container = document.getElementById(`questions-section-${sectionIndex}`);
            const template = window.selectedTemplate.template;
            const section = template.sections[sectionIndex];

            // Show loading state
            container.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-indigo-500 text-3xl mb-3"></i>
                    <p class="text-gray-600">Loading approved questions...</p>
                </div>
            `;

            // Determine question type and marks based on section
            let questionType = '';
            let marks = section.marksEach;

            // Map section types to question types
            switch(section.type.toLowerCase()) {
                case 'multiple choice':
                    questionType = 'multiple_choice';
                    break;
                case 'short answer':
                case 'short answer i':
                case 'short answer ii':
                    questionType = 'short_answer';
                    break;
                case 'long answer':
                case 'essay type':
                case 'essay/creative':
                case 'essay/letter':
                    questionType = 'long_answer';
                    break;
                case 'paragraph':
                    questionType = 'paragraph';
                    break;
                default:
                    questionType = 'short_answer';
            }

            // Determine standard based on template type
            let standard;
            if (window.selectedTemplate.type === 'sslc') {
                standard = 10;
            } else if (window.selectedTemplate.type === 'hsc1') {
                standard = 11;
            } else if (window.selectedTemplate.type === 'hsc2') {
                standard = 12;
            } else {
                standard = 12; // fallback for old hsc
            }

            // Build API URL with filters (remove strict marks filtering for more flexibility)
            const params = new URLSearchParams({
                subject: dbSubject,
                standard: standard,
                question_type: questionType
                // Note: Removed marks filter to show all questions of the correct type
            });

            // Fetch approved questions from API
            const apiUrl = `<?= base_url() ?>schooladmin/getApprovedQuestions?${params}`;
            console.log('API URL:', apiUrl);

            fetch(apiUrl)
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Response data:', data);
                    if (data.success) {
                        displayQuestionsInSection(data.questions, sectionIndex, sectionName, dbSubject, section);
                    } else {
                        displayNoQuestionsMessage(sectionIndex, sectionName, dbSubject, data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading questions:', error);
                    displayErrorMessage(sectionIndex, sectionName, dbSubject);
                });
        }

        function displayQuestionsInSection(questions, sectionIndex, sectionName, subject, section) {
            const container = document.getElementById(`questions-section-${sectionIndex}`);

            if (questions.length === 0) {
                displayNoQuestionsMessage(sectionIndex, sectionName, subject, 'No approved questions found for this section');
                return;
            }

            const expectedMarks = section.marksEach;
            let questionsHTML = `
                <div class="mb-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <span class="text-sm font-medium text-gray-700">Select questions for ${sectionName} (${questions.length} available)</span>
                            <div class="text-xs text-gray-500 mt-1">Expected marks per question: ${expectedMarks}</div>
                        </div>
                        <div class="space-x-2">
                            <button type="button" onclick="selectAllQuestions(${sectionIndex})"
                                    class="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700">
                                Select All
                            </button>
                            <button type="button" onclick="clearAllQuestions(${sectionIndex})"
                                    class="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700">
                                Clear All
                            </button>
                        </div>
                    </div>
                </div>
                <div class="max-h-60 overflow-y-auto space-y-2">
            `;

            questions.forEach((question, qIndex) => {
                const questionText = question.question_text;
                const difficulty = question.difficulty || 'Medium';
                const marks = question.marks;
                const chapter = question.chapter_name || question.chapter || '';
                const staffName = question.staff_name || 'Unknown';

                // Highlight questions that match expected marks
                const isRecommended = marks == expectedMarks;
                const borderClass = isRecommended ? 'border-green-300 bg-green-50' : 'border-gray-200';
                const recommendedBadge = isRecommended ? '<span class="inline-block px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full ml-2">Recommended</span>' : '';

                questionsHTML += `
                    <div class="flex items-start space-x-3 p-3 border ${borderClass} rounded-lg hover:bg-gray-50">
                        <input type="checkbox" id="question-${sectionIndex}-${qIndex}"
                               class="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                               data-question-id="${question.id}"
                               data-question-text="${questionText.replace(/"/g, '&quot;')}"
                               data-difficulty="${difficulty}"
                               data-marks="${marks}"
                               data-chapter="${chapter}"
                               data-staff="${staffName}"
                               onchange="updateSelectedQuestions(${sectionIndex})">
                        <div class="flex-1">
                            <label for="question-${sectionIndex}-${qIndex}" class="text-sm text-gray-800 cursor-pointer">
                                <strong>Q${qIndex + 1}:</strong> ${questionText}${recommendedBadge}
                            </label>
                            <div class="mt-1 text-xs text-gray-500 space-x-1">
                                <span class="bg-gray-100 px-2 py-1 rounded">Difficulty: ${difficulty}</span>
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">Marks: ${marks}</span>
                                ${chapter ? `<span class="bg-green-100 text-green-800 px-2 py-1 rounded">Chapter: ${chapter}</span>` : ''}
                                <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">By: ${staffName}</span>
                            </div>
                        </div>
                    </div>
                `;
            });

            questionsHTML += `
                </div>
                <div class="mt-3 p-2 bg-blue-50 rounded">
                    <div class="flex justify-between items-center text-sm text-blue-800">
                        <span>
                            <span id="selected-count-${sectionIndex}">0</span> questions selected out of ${questions.length} available
                        </span>
                        <span class="font-semibold">
                            Section Marks: <span id="selected-marks-${sectionIndex}">0</span>
                        </span>
                    </div>
                </div>
            `;

            container.innerHTML = questionsHTML;

            // Initialize the total marks calculation
            updateTotalMarks();
        }

        function displayNoQuestionsMessage(sectionIndex, sectionName, subject, message) {
            const container = document.getElementById(`questions-section-${sectionIndex}`);
            container.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-triangle text-yellow-500 text-3xl mb-3"></i>
                    <p class="text-gray-600 mb-2">${message}</p>
                    <p class="text-sm text-gray-500 mb-4">You need approved questions for this section to create a question paper.</p>
                    <button type="button" onclick="redirectToQuestionCreation('${subject}', '${sectionName}')"
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        Create Questions First
                    </button>
                </div>
            `;
        }

        function displayErrorMessage(sectionIndex, sectionName, subject) {
            const container = document.getElementById(`questions-section-${sectionIndex}`);
            container.innerHTML = `
                <div class="text-center py-8">
                    <i class="fas fa-exclamation-circle text-red-500 text-3xl mb-3"></i>
                    <p class="text-gray-600 mb-4">Error loading questions for this section.</p>
                    <button type="button" onclick="loadQuestionsForSection('${subject}', '${sectionName}', ${sectionIndex})"
                            class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                        Try Again
                    </button>
                </div>
            `;
        }



        function selectAllQuestions(sectionIndex) {
            const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedQuestions(sectionIndex);
        }

        function clearAllQuestions(sectionIndex) {
            const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedQuestions(sectionIndex);
        }

        function updateSelectedQuestions(sectionIndex) {
            const checkboxes = document.querySelectorAll(`#questions-section-${sectionIndex} input[type="checkbox"]:checked`);
            const countElement = document.getElementById(`selected-count-${sectionIndex}`);
            const marksElement = document.getElementById(`selected-marks-${sectionIndex}`);

            // Calculate total marks for this section
            let sectionMarks = 0;
            checkboxes.forEach(checkbox => {
                const marks = parseInt(checkbox.getAttribute('data-marks')) || 0;
                sectionMarks += marks;
            });

            // Update section display
            if (countElement) {
                countElement.textContent = checkboxes.length;
            }
            if (marksElement) {
                marksElement.textContent = sectionMarks;
            }

            // Update overall total marks
            updateTotalMarks();
        }

        function updateTotalMarks() {
            let totalMarks = 0;
            let totalQuestions = 0;

            // Calculate total across all sections
            const template = window.selectedTemplate.template;
            template.sections.forEach((section, index) => {
                const checkboxes = document.querySelectorAll(`#questions-section-${index} input[type="checkbox"]:checked`);
                checkboxes.forEach(checkbox => {
                    const marks = parseInt(checkbox.getAttribute('data-marks')) || 0;
                    totalMarks += marks;
                    totalQuestions++;
                });
            });

            // Update total marks display
            const totalMarksElement = document.getElementById('total-marks-display');
            const totalQuestionsElement = document.getElementById('total-questions-display');
            const percentageElement = document.getElementById('marks-percentage');

            if (totalMarksElement) {
                const previousMarks = parseInt(totalMarksElement.textContent) || 0;
                totalMarksElement.textContent = totalMarks;

                // Add visual feedback when marks change
                if (totalMarks !== previousMarks && previousMarks > 0) {
                    totalMarksElement.classList.add('animate-pulse');
                    setTimeout(() => {
                        totalMarksElement.classList.remove('animate-pulse');
                    }, 1000);

                    // Show subtle notification for significant changes
                    const difference = totalMarks - previousMarks;
                    if (Math.abs(difference) >= 5) {
                        const message = difference > 0
                            ? `Total marks increased to ${totalMarks} (+${difference})`
                            : `Total marks decreased to ${totalMarks} (${difference})`;
                        console.log(message); // Could be replaced with a toast notification
                    }
                }
            }
            if (totalQuestionsElement) {
                totalQuestionsElement.textContent = totalQuestions;
            }

            // Calculate and update percentage
            if (percentageElement && window.selectedTemplate) {
                const template = window.selectedTemplate.template;
                const templateMaxMarks = template.totalMarks || 100;
                const percentage = templateMaxMarks > 0 ? Math.round((totalMarks / templateMaxMarks) * 100) : 0;
                percentageElement.textContent = percentage;

                // Update color based on percentage
                const progressSpan = percentageElement.parentElement;
                if (percentage >= 90) {
                    progressSpan.className = 'inline-block px-3 py-1 bg-green-100 text-green-800 rounded-full shadow-sm';
                } else if (percentage >= 70) {
                    progressSpan.className = 'inline-block px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full shadow-sm';
                } else {
                    progressSpan.className = 'inline-block px-3 py-1 bg-red-100 text-red-800 rounded-full shadow-sm';
                }
            }

            // Update the header display as well
            const headerMarksElement = document.querySelector('.gradient-bg p');
            if (headerMarksElement && window.selectedTemplate) {
                const template = window.selectedTemplate.template;
                const subjectName = window.selectedTemplate.subject ? (window.selectedTemplate.subject.charAt(0).toUpperCase() + window.selectedTemplate.subject.slice(1).replace(/([A-Z])/g, ' $1')) : 'Unknown Subject';
                const templateType = window.selectedTemplate.type ? window.selectedTemplate.type.toUpperCase() : 'UNKNOWN';
                const durationInput = document.getElementById('examDuration');
                const currentDuration = durationInput ? parseFloat(durationInput.value) + ' Hours' : template.duration;
                headerMarksElement.innerHTML = `Duration: ${currentDuration} | Total Marks: <span class="font-bold">${totalMarks}</span> (Selected: ${totalQuestions} questions)`;
            }

            // Update instructions with dynamic marks
            const instructionsTextarea = document.getElementById('instructions');
            if (instructionsTextarea && window.selectedTemplate) {
                const template = window.selectedTemplate.template;
                const currentInstructions = instructionsTextarea.value;

                // Update the maximum marks line in instructions
                const updatedInstructions = currentInstructions.replace(
                    /4\. Maximum marks: \d+/,
                    `4. Maximum marks: ${totalMarks}`
                );

                if (updatedInstructions !== currentInstructions) {
                    instructionsTextarea.value = updatedInstructions;
                }
            }
        }

        function updateDurationDisplay() {
            const durationInput = document.getElementById('examDuration');
            const durationDisplay = document.getElementById('duration-display');

            if (durationInput && durationDisplay) {
                const duration = parseFloat(durationInput.value) || 3.0;
                durationDisplay.textContent = duration + ' Hours';

                // Update instructions with new duration
                const instructionsTextarea = document.getElementById('instructions');
                if (instructionsTextarea) {
                    const currentInstructions = instructionsTextarea.value;
                    const updatedInstructions = currentInstructions.replace(/3\. Time allowed: [0-9.]+ Hours/, `3. Time allowed: ${duration} Hours`);
                    instructionsTextarea.value = updatedInstructions;
                }

                // Update header display
                const headerMarksElement = document.querySelector('.gradient-bg p');
                if (headerMarksElement && window.selectedTemplate) {
                    const template = window.selectedTemplate.template;
                    const totalMarks = calculateTotalMarks();
                    const totalQuestions = calculateTotalQuestions();
                    headerMarksElement.innerHTML = `Duration: ${duration} Hours | Total Marks: <span class="font-bold">${totalMarks}</span> (Selected: ${totalQuestions} questions)`;
                }
            }
        }

        function redirectToQuestionCreation(subject, sectionName) {
            showNotification(`Redirecting to create questions for ${subject} - ${sectionName}`, 'info');
            // Redirect to staff dashboard questions section with pre-filled subject
            setTimeout(() => {
                window.location.href = `<?= base_url() ?>staff/dashboard?tab=questions&subject=${encodeURIComponent(subject)}&section=${encodeURIComponent(sectionName)}`;
            }, 1500);
        }

        function previewQuestionPaper() {
            const formData = collectFormData();
            console.log('Form data for preview:', formData);

            if (!validateFormData(formData, true)) { // true = isPreview
                return;
            }

            // Generate preview HTML
            const previewHTML = generatePreviewHTML(formData);

            // Show preview modal
            const previewModal = `
                <div id="previewModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[95vh] overflow-y-auto">
                        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                            <div class="flex items-center justify-between">
                                <h3 class="text-xl font-bold text-gray-800">Question Paper Preview</h3>
                                <button onclick="closePreviewModal()" class="text-gray-500 hover:text-gray-700">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>
                        <div class="p-6">
                            ${previewHTML}
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex justify-between">
                            <button onclick="closePreviewModal()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
                                Close Preview
                            </button>
                            <div class="space-x-3">
                                <button onclick="saveMarksChanges()" class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700">
                                    <i class="fas fa-edit mr-2"></i>Save Marks Changes
                                </button>
                                <button onclick="downloadPDF()" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                                    <i class="fas fa-download mr-2"></i>Download PDF
                                </button>
                                <button onclick="downloadAnswerKeyFromPreview()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                    <i class="fas fa-key mr-2"></i>Download Answer Key
                                </button>
                                <button onclick="finalizeQuestionPaper()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700">
                                    Create Paper
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', previewModal);
        }

        function generatePreviewHTML(formData) {
            const template = window.selectedTemplate.template;
            let html = `
                <div class="question-paper-preview">
                    <!-- Header -->
                    <div class="text-center mb-6 pb-4 border-b-2 border-gray-300">
                        <div class="mb-3">
                            <h2 class="text-lg font-semibold text-gray-700"><?= esc($school_name ?? 'School Name') ?></h2>
                            <p class="text-sm text-gray-500"><?= esc($school_address ?? '') ?></p>
                        </div>
                        <h1 class="text-2xl font-bold text-gray-800 mb-2">${formData.title}</h1>
                        <div class="text-sm text-gray-600 space-y-1">
                            <p><strong>Academic Year:</strong> ${formData.academicYear || 'Not specified'}</p>
                            <p><strong>Exam Type:</strong> ${formData.examType || 'Not specified'}</p>
                            <p><strong>Date:</strong> ${formData.examDate || 'Not specified'}</p>
                            <p><strong>Duration:</strong> ${formData.duration} Hours | <strong>Maximum Marks:</strong> <span id="preview-total-marks">${formData.totalMarks}</span></p>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Instructions to Students:</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <pre class="text-sm text-gray-700 whitespace-pre-wrap">${formData.instructions}</pre>
                        </div>
                    </div>

                    <!-- Editable Notice -->
                    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                            <p class="text-sm text-blue-800">
                                <strong>Note:</strong> You can edit the marks for each question by clicking on the marks input field.
                                Total marks will be automatically recalculated.
                            </p>
                        </div>
                    </div>

                    <!-- Sections -->
                    <div class="space-y-6">
            `;

            template.sections.forEach((section, index) => {
                // Filter questions for this section
                const selectedQuestions = formData.selectedQuestions.filter(q => q.section_name === section.name) || [];
                const sectionMarks = selectedQuestions.reduce((sum, q) => sum + q.marks, 0);

                html += `
                    <div class="section" data-section-index="${index}">
                        <div class="flex items-center justify-between mb-4 pb-2 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">${section.name} - ${section.type}</h3>
                            <div class="text-sm text-gray-600">
                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">${selectedQuestions.length} Questions</span>
                                <span class="bg-green-100 text-green-800 px-2 py-1 rounded ml-2" id="section-marks-${index}">${sectionMarks} Marks</span>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600 mb-4 italic">${section.instruction}</p>

                        <div class="questions space-y-4">
                `;

                if (selectedQuestions.length === 0) {
                    html += `
                        <div class="text-center py-8 bg-yellow-50 rounded-lg">
                            <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mb-2"></i>
                            <p class="text-yellow-700">No questions selected for this section</p>
                        </div>
                    `;
                } else {
                    selectedQuestions.forEach((question, qIndex) => {
                        html += `
                            <div class="question-item border border-gray-200 rounded-lg p-3 mb-3">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <p class="text-gray-800"><strong>${qIndex + 1}.</strong> ${question.text}</p>
                                        <div class="text-xs text-gray-500 mt-2">
                                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">Difficulty: ${question.difficulty}</span>
                                            ${question.chapter ? `<span class="bg-green-100 text-green-800 px-2 py-1 rounded ml-1">Chapter: ${question.chapter}</span>` : ''}
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2 ml-4">
                                        <label class="text-xs text-gray-600">Marks:</label>
                                        <input type="number"
                                               value="${question.marks}"
                                               min="1"
                                               max="20"
                                               class="w-16 px-2 py-1 text-xs border-2 border-orange-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-orange-50 font-semibold text-orange-800"
                                               onchange="updateQuestionMarks(${index}, ${qIndex}, this.value)"
                                               data-section-index="${index}"
                                               data-question-index="${qIndex}"
                                               title="Click to edit marks (1-20)">
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                }

                html += `
                        </div>
                    </div>
                `;
            });

            if (formData.additionalNotes) {
                html += `
                    <div class="mt-6 pt-4 border-t border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800 mb-3">Additional Notes:</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <pre class="text-sm text-gray-700 whitespace-pre-wrap">${formData.additionalNotes}</pre>
                        </div>
                    </div>
                `;
            }

            html += `
                    </div>
                </div>
            `;

            return html;
        }

        function collectFormData() {
            const template = window.selectedTemplate.template;
            const selectedQuestions = [];

            // Collect selected questions for each section
            template.sections.forEach((section, index) => {
                const checkboxes = document.querySelectorAll(`#questions-section-${index} input[type="checkbox"]:checked`);

                checkboxes.forEach(checkbox => {
                    // Get data from checkbox attributes (set when loading real questions)
                    const questionId = checkbox.getAttribute('data-question-id');
                    const questionText = checkbox.getAttribute('data-question-text');
                    const difficulty = checkbox.getAttribute('data-difficulty');
                    const marks = checkbox.getAttribute('data-marks') || section.marksEach;
                    const chapter = checkbox.getAttribute('data-chapter');

                    if (questionId) {
                        selectedQuestions.push({
                            question_id: parseInt(questionId),
                            text: questionText,
                            difficulty: difficulty,
                            marks: parseInt(marks),
                            chapter: chapter,
                            section_name: section.name,
                            section_type: section.type
                        });
                    }
                });
            });

            // Calculate total marks
            const totalMarks = selectedQuestions.reduce((sum, q) => sum + q.marks, 0);

            // Determine standard and subject based on template type
            let standard, subject;

            if (window.selectedTemplate.type === 'custom') {
                // For custom templates, get standard and subject from form or use defaults
                standard = document.getElementById('standardSelect')?.value || '12';
                subject = document.getElementById('subjectInput')?.value || template.name || 'Custom Subject';
            } else {
                // For predefined templates
                standard = window.selectedTemplate.type === 'sslc' ? '10' :
                          window.selectedTemplate.type === 'hsc1' ? '11' : '12';
                subject = window.selectedTemplate.subject ?
                         (window.selectedTemplate.subject.charAt(0).toUpperCase() + window.selectedTemplate.subject.slice(1)) :
                         'Unknown Subject';
            }

            return {
                title: document.getElementById('paperTitle').value,
                standard: standard,
                subject: subject,
                duration: document.getElementById('examDuration') ? parseFloat(document.getElementById('examDuration').value) : (template.duration ? parseFloat(template.duration.replace(' Hours', '')) : 3.0),
                totalMarks: totalMarks,
                status: 'draft',
                selectedQuestions: selectedQuestions,
                academicYear: document.getElementById('academicYear').value,
                examType: document.getElementById('examType').value,
                examDate: document.getElementById('examDate').value,
                instructions: document.getElementById('instructions').value,
                additionalNotes: document.getElementById('additionalNotes').value,
                templateType: window.selectedTemplate.type,
                template: template
            };
        }

        function validateFormData(formData, isPreview = false) {
            console.log('Validating form data:', formData);

            if (!formData.title || !formData.title.trim()) {
                showNotification('Please enter a paper title', 'error');
                return false;
            }

            if (!formData.standard || !formData.standard.trim()) {
                showNotification('Standard is required', 'error');
                return false;
            }

            if (!formData.subject || !formData.subject.trim()) {
                showNotification('Subject is required', 'error');
                return false;
            }

            // Only require exam date for final submission, not for preview
            if (!isPreview && !formData.examDate) {
                showNotification('Please select an exam date', 'error');
                return false;
            }

            // Check if at least some questions are selected
            if (!formData.selectedQuestions || formData.selectedQuestions.length === 0) {
                showNotification('Please select at least some questions for the paper', 'error');
                return false;
            }

            return true;
        }

        function closePreviewModal() {
            const modal = document.getElementById('previewModal');
            if (modal) {
                modal.remove();
            }
        }

        function updateQuestionMarks(sectionIndex, questionIndex, newMarks) {
            // Validate input
            const marks = parseInt(newMarks);
            if (isNaN(marks) || marks < 1 || marks > 20) {
                showNotification('Marks must be between 1 and 20', 'error');
                return;
            }

            // Mark the input as changed
            const input = event.target;
            input.classList.add('border-green-400', 'bg-green-50', 'text-green-800');
            input.classList.remove('border-orange-300', 'bg-orange-50', 'text-orange-800');

            // Get all marks inputs in this section to recalculate
            const sectionInputs = document.querySelectorAll(`[data-section-index="${sectionIndex}"] input[type="number"]`);
            let sectionMarks = 0;

            sectionInputs.forEach(input => {
                sectionMarks += parseInt(input.value) || 0;
            });

            // Update section marks display with highlight
            const sectionMarksSpan = document.getElementById(`section-marks-${sectionIndex}`);
            if (sectionMarksSpan) {
                sectionMarksSpan.textContent = sectionMarks + ' Marks';
                sectionMarksSpan.classList.add('bg-yellow-100', 'text-yellow-800');
                sectionMarksSpan.classList.remove('bg-green-100', 'text-green-800');
            }

            // Recalculate total marks from all sections
            let totalMarks = 0;
            const allSections = document.querySelectorAll('[data-section-index]');
            allSections.forEach(section => {
                const inputs = section.querySelectorAll('input[type="number"]');
                inputs.forEach(input => {
                    totalMarks += parseInt(input.value) || 0;
                });
            });

            // Update total marks display with highlight
            const totalMarksSpan = document.getElementById('preview-total-marks');
            if (totalMarksSpan) {
                totalMarksSpan.textContent = totalMarks;
                totalMarksSpan.classList.add('bg-yellow-200', 'px-2', 'py-1', 'rounded', 'font-bold');
            }

            showNotification('Marks updated successfully', 'success');
        }

        function saveMarksChanges() {
            // Get all marks inputs and update the form data
            const allInputs = document.querySelectorAll('#previewModal input[type="number"]');
            let updatedQuestions = [];

            allInputs.forEach(input => {
                const sectionIndex = input.getAttribute('data-section-index');
                const questionIndex = input.getAttribute('data-question-index');
                const marks = parseInt(input.value);

                updatedQuestions.push({
                    sectionIndex: sectionIndex,
                    questionIndex: questionIndex,
                    marks: marks
                });
            });

            // Store updated marks in a global variable for later use
            window.updatedMarks = updatedQuestions;

            showNotification('Marks changes saved! These will be applied when you create the paper.', 'success');
        }

        function downloadPDF() {
            const formData = collectFormData();

            // Apply updated marks if they exist
            if (window.updatedMarks && window.updatedMarks.length > 0) {
                const template = window.selectedTemplate.template;

                window.updatedMarks.forEach(update => {
                    const sectionName = template.sections[update.sectionIndex].name;
                    const questionInSection = formData.selectedQuestions.filter(q => q.section_name === sectionName)[update.questionIndex];

                    if (questionInSection) {
                        questionInSection.marks = update.marks;
                    }
                });

                // Recalculate total marks
                formData.totalMarks = formData.selectedQuestions.reduce((sum, q) => sum + parseInt(q.marks), 0);
            }

            if (!validateFormData(formData, true)) {
                return;
            }

            // Show loading state
            const downloadBtn = event.target;
            const originalText = downloadBtn.innerHTML;
            downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating PDF...';
            downloadBtn.disabled = true;

            // Send data to server for PDF generation
            fetch('<?= base_url() ?>schooladmin/downloadQuestionPaperPDF', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                // Try to get error message from response
                return response.text().then(text => {
                    let errorMessage = 'Failed to generate PDF';
                    try {
                        const jsonError = JSON.parse(text);
                        errorMessage = jsonError.message || errorMessage;
                    } catch (e) {
                        // If it's HTML error page, extract message
                        if (text.includes('<p>')) {
                            const match = text.match(/<p>(.*?)<\/p>/);
                            if (match) {
                                errorMessage = match[1];
                            }
                        }
                    }
                    throw new Error(errorMessage);
                });
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `question_paper_${formData.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.html`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showNotification('PDF downloaded successfully!', 'success');
            })
            .catch(error => {
                console.error('Download error:', error);
                showNotification('Failed to download PDF. Please try again.', 'error');
            })
            .finally(() => {
                // Restore button state
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            });
        }

        function finalizeQuestionPaper() {
            const formData = collectFormData();

            // Apply updated marks if they exist
            if (window.updatedMarks && window.updatedMarks.length > 0) {
                const template = window.selectedTemplate.template;

                window.updatedMarks.forEach(update => {
                    const sectionName = template.sections[update.sectionIndex].name;
                    const questionInSection = formData.selectedQuestions.filter(q => q.section_name === sectionName)[update.questionIndex];

                    if (questionInSection) {
                        questionInSection.marks = update.marks;
                    }
                });

                // Recalculate total marks
                formData.totalMarks = formData.selectedQuestions.reduce((sum, q) => sum + parseInt(q.marks), 0);

                showNotification('Applied updated marks to question paper', 'success');
            }

            handleQuestionPaperSubmission(null, formData);
            closePreviewModal();
        }

        function handleQuestionPaperSubmission(event, previewData = null) {
            if (event) {
                event.preventDefault();
            }

            const formData = previewData || collectFormData();

            if (!validateFormData(formData)) {
                return;
            }

            // Check if we're editing or creating
            const isEditing = window.editingPaperId;
            const actionText = isEditing ? 'Updating' : 'Creating';
            const successText = isEditing ? 'updated' : 'created';

            // Show loading state
            showNotification(`${actionText} question paper...`, 'info');

            // Prepare data for API
            const apiData = {
                title: formData.title,
                standard: formData.standard,
                subject: formData.subject,
                duration: formData.duration,
                totalMarks: formData.totalMarks,
                academicYear: formData.academicYear,
                examType: formData.examType,
                examDate: formData.examDate,
                instructions: formData.instructions,
                status: formData.status || 'draft',
                selectedQuestions: formData.selectedQuestions
            };

            // Determine API endpoint and method
            const apiUrl = isEditing
                ? `<?= base_url() ?>schooladmin/updateQuestionPaper/${window.editingPaperId}`
                : '<?= base_url() ?>schooladmin/createQuestionPaper';

            const method = isEditing ? 'PUT' : 'POST';

            // For PUT requests, we need to send JSON
            const requestOptions = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(apiData)
            };

            // For POST requests (create), use FormData
            if (!isEditing) {
                const formDataObj = new FormData();
                Object.keys(apiData).forEach(key => {
                    if (key === 'selectedQuestions') {
                        formDataObj.append(key, JSON.stringify(apiData[key]));
                    } else {
                        formDataObj.append(key, apiData[key]);
                    }
                });
                requestOptions.body = formDataObj;
                delete requestOptions.headers['Content-Type']; // Let browser set it for FormData
            }

            // Send data to server
            fetch(apiUrl, requestOptions)
            .then(response => response.json())
            .then(data => {
                console.log(`Question Paper ${actionText} Response:`, data);

                if (data.success) {
                    showNotification(`Question paper ${successText} successfully!`, 'success');
                    closeQuestionPaperForm();

                    // Clear editing mode
                    if (isEditing) {
                        window.editingPaperId = null;
                    }

                    // Refresh the question papers list
                    loadQuestionPapersList();

                    // Show the paper details
                    setTimeout(() => {
                        showQuestionPaperDetails(data.paper_id);
                    }, 1000);
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error(`Error ${actionText.toLowerCase()} question paper:`, error);
                showNotification(`Failed to ${actionText.toLowerCase()} question paper. Please try again.`, 'error');
            });
        }

        // Load and display question papers list
        function loadQuestionPapersList(status = null) {
            const container = document.querySelector('.bg-white.rounded-xl.shadow-sm.border.border-gray-200:last-child .p-6');

            // Show loading state
            container.innerHTML = `
                <div class="text-center py-12">
                    <i class="fas fa-spinner fa-spin text-indigo-500 text-3xl mb-3"></i>
                    <p class="text-gray-600">Loading question papers...</p>
                </div>
            `;

            // Build API URL
            let apiUrl = '<?= base_url() ?>schooladmin/getQuestionPapers';
            if (status) {
                apiUrl += `?status=${status}`;
            }

            fetch(apiUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayQuestionPapersList(data.data);
                    } else {
                        showNoQuestionPapersMessage();
                    }
                })
                .catch(error => {
                    console.error('Error loading question papers:', error);
                    showNoQuestionPapersMessage();
                });
        }

        function displayQuestionPapersList(papers) {
            const container = document.querySelector('.bg-white.rounded-xl.shadow-sm.border.border-gray-200:last-child .p-6');

            if (papers.length === 0) {
                showNoQuestionPapersMessage();
                return;
            }

            let papersHTML = `
                <div class="space-y-4">
            `;

            papers.forEach(paper => {
                const statusColor = {
                    'draft': 'bg-yellow-100 text-yellow-800',
                    'published': 'bg-green-100 text-green-800',
                    'archived': 'bg-gray-100 text-gray-800'
                };

                papersHTML += `
                    <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-center justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3">
                                    <h4 class="text-lg font-semibold text-gray-800">${paper.title}</h4>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full ${statusColor[paper.status] || 'bg-gray-100 text-gray-800'}">
                                        ${paper.status ? (paper.status.charAt(0).toUpperCase() + paper.status.slice(1)) : 'Unknown'}
                                    </span>
                                </div>
                                <div class="mt-2 flex items-center space-x-6 text-sm text-gray-600">
                                    <span><i class="fas fa-book mr-1"></i>${paper.subject}</span>
                                    <span><i class="fas fa-graduation-cap mr-1"></i>Standard ${paper.standard}</span>
                                    <span><i class="fas fa-clock mr-1"></i>${paper.duration} Hours</span>
                                    <span><i class="fas fa-star mr-1"></i>${paper.total_marks} Marks</span>
                                    <span><i class="fas fa-question-circle mr-1"></i>${paper.questions_count} Questions</span>
                                </div>
                                <div class="mt-2 text-xs text-gray-500">
                                    Created by ${paper.created_by_name || 'Unknown'} on ${new Date(paper.created_at).toLocaleDateString()}
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button onclick="showQuestionPaperDetails(${paper.id})"
                                        class="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700">
                                    <i class="fas fa-eye mr-1"></i>View
                                </button>
                                <button onclick="downloadQuestionPaper(${paper.id})"
                                        class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                                    <i class="fas fa-download mr-1"></i>PDF
                                </button>
                                <button onclick="downloadAnswerKey(${paper.id})"
                                        class="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700">
                                    <i class="fas fa-key mr-1"></i>Answer Key
                                </button>
                                ${paper.status === 'draft' ? `
                                    <button onclick="editQuestionPaper(${paper.id})"
                                            class="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700">
                                        <i class="fas fa-edit mr-1"></i>Edit
                                    </button>
                                    <button onclick="deleteQuestionPaper(${paper.id})"
                                            class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                                        <i class="fas fa-trash mr-1"></i>Delete
                                    </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            });

            papersHTML += `</div>`;
            container.innerHTML = papersHTML;
        }

        function showNoQuestionPapersMessage() {
            const container = document.querySelector('.bg-white.rounded-xl.shadow-sm.border.border-gray-200:last-child .p-6');
            container.innerHTML = `
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-file-alt text-2xl text-gray-400"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Question Papers Yet</h3>
                    <p class="text-gray-500 mb-6">Start by selecting a template above to create your first question paper.</p>
                    <button onclick="selectTemplate('tn-sslc')" class="bg-indigo-600 text-white px-6 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Create First Paper
                    </button>
                </div>
            `;
        }

        // Show question paper details in a modal
        function showQuestionPaperDetails(paperId) {
            // Show loading modal first
            const loadingModal = `
                <div id="paperDetailsModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                        <div class="p-6 text-center">
                            <i class="fas fa-spinner fa-spin text-indigo-500 text-3xl mb-3"></i>
                            <p class="text-gray-600">Loading question paper details...</p>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', loadingModal);

            // Fetch paper details
            fetch(`<?= base_url() ?>schooladmin/getQuestionPaper/${paperId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayQuestionPaperDetailsModal(data.data);
                    } else {
                        showNotification('Error loading question paper: ' + data.message, 'error');
                        closePaperDetailsModal();
                    }
                })
                .catch(error => {
                    console.error('Error loading question paper details:', error);
                    showNotification('Failed to load question paper details', 'error');
                    closePaperDetailsModal();
                });
        }

        function displayQuestionPaperDetailsModal(paper) {
            const modal = document.getElementById('paperDetailsModal');

            const statusColor = {
                'draft': 'bg-yellow-100 text-yellow-800',
                'published': 'bg-green-100 text-green-800',
                'archived': 'bg-gray-100 text-gray-800'
            };

            let questionsHTML = '';
            if (paper.questions && paper.questions.length > 0) {
                questionsHTML = paper.questions.map((q, index) => `
                    <div class="border border-gray-200 rounded-lg p-4 mb-4">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="bg-indigo-100 text-indigo-800 text-xs font-medium px-2 py-1 rounded">
                                        Q${index + 1}
                                    </span>
                                    <span class="text-xs text-gray-500">${q.question_type}</span>
                                    <span class="text-xs text-gray-500">${q.difficulty}</span>
                                </div>
                                <p class="text-gray-800 mb-3">${q.question_text}</p>

                                ${q.question_type === 'multiple_choice' ? `
                                    <div class="grid grid-cols-2 gap-2 text-sm">
                                        <div class="flex items-center space-x-2">
                                            <span class="font-medium">A)</span>
                                            <span class="${q.correct_answer === 'a' ? 'text-green-600 font-medium' : ''}">${q.option_a}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="font-medium">B)</span>
                                            <span class="${q.correct_answer === 'b' ? 'text-green-600 font-medium' : ''}">${q.option_b}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="font-medium">C)</span>
                                            <span class="${q.correct_answer === 'c' ? 'text-green-600 font-medium' : ''}">${q.option_c}</span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="font-medium">D)</span>
                                            <span class="${q.correct_answer === 'd' ? 'text-green-600 font-medium' : ''}">${q.option_d}</span>
                                        </div>
                                    </div>
                                ` : ''}

                                ${q.chapter ? `
                                    <div class="mt-2 text-xs text-gray-500">
                                        Chapter: ${q.chapter_name || q.chapter}
                                        ${q.topic_name ? ` | Topic: ${q.topic_name}` : ''}
                                    </div>
                                ` : ''}
                            </div>
                            <div class="text-right">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded">
                                    ${q.marks} Mark${q.marks > 1 ? 's' : ''}
                                </span>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                questionsHTML = '<p class="text-gray-500 text-center py-8">No questions found for this paper.</p>';
            }

            modal.innerHTML = `
                <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                    <div class="px-6 py-4 border-b border-gray-200 bg-indigo-600 text-white">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-xl font-bold">${paper.title}</h3>
                                <p class="text-indigo-100 text-sm">
                                    ${paper.subject} | Standard ${paper.standard} | ${paper.duration} Hours | ${paper.total_marks} Marks
                                </p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="px-2 py-1 text-xs font-medium rounded-full bg-white bg-opacity-20">
                                    ${paper.status ? (paper.status.charAt(0).toUpperCase() + paper.status.slice(1)) : 'Unknown'}
                                </span>
                                <button onclick="closePaperDetailsModal()" class="text-white hover:text-gray-200">
                                    <i class="fas fa-times text-xl"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="mb-6">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="text-2xl font-bold text-indigo-600">${paper.questions_count}</div>
                                    <div class="text-sm text-gray-600">Questions</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="text-2xl font-bold text-green-600">${paper.total_marks}</div>
                                    <div class="text-sm text-gray-600">Total Marks</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="text-2xl font-bold text-blue-600">${paper.duration}</div>
                                    <div class="text-sm text-gray-600">Hours</div>
                                </div>
                                <div class="bg-gray-50 rounded-lg p-3">
                                    <div class="text-2xl font-bold text-purple-600">${paper.standard}</div>
                                    <div class="text-sm text-gray-600">Standard</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <h4 class="text-lg font-semibold text-gray-800 mb-4">Questions</h4>
                            <div class="max-h-96 overflow-y-auto">
                                ${questionsHTML}
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                            <button onclick="downloadQuestionPaper(${paper.id})"
                                    class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                                <i class="fas fa-download mr-2"></i>Download PDF
                            </button>
                            <button onclick="downloadAnswerKey(${paper.id})"
                                    class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                <i class="fas fa-key mr-2"></i>Download Answer Key
                            </button>
                            ${paper.status === 'draft' ? `
                                <button onclick="editQuestionPaper(${paper.id})"
                                        class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700">
                                    <i class="fas fa-edit mr-2"></i>Edit Paper
                                </button>
                            ` : ''}
                            <button onclick="closePaperDetailsModal()"
                                    class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function closePaperDetailsModal() {
            const modal = document.getElementById('paperDetailsModal');
            if (modal) {
                modal.remove();
            }
        }

        function downloadQuestionPaper(paperId) {
            // Show loading notification
            showNotification('Generating PDF...', 'info');

            // Create a temporary link to download the PDF
            const downloadUrl = `<?= base_url() ?>schooladmin/downloadQuestionPaperPDF/${paperId}`;

            // Create a temporary anchor element and trigger download
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = `question_paper_${paperId}.html`;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            showNotification('PDF download started!', 'success');
        }

        function downloadAnswerKey(paperId) {
            // Show loading notification
            showNotification('Generating Answer Key PDF...', 'info');

            // Create a temporary link to download the answer key PDF
            const downloadUrl = `<?= base_url() ?>schooladmin/downloadAnswerKeyPDF/${paperId}`;

            // Create a temporary anchor element and trigger download
            const a = document.createElement('a');
            a.href = downloadUrl;
            a.download = `answer_key_${paperId}.pdf`;
            a.style.display = 'none';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            showNotification('Answer Key PDF download started!', 'success');
        }

        function downloadAnswerKeyFromPreview() {
            const formData = collectFormData();

            // Apply updated marks if they exist
            if (window.updatedMarks && window.updatedMarks.length > 0) {
                const template = window.selectedTemplate.template;

                window.updatedMarks.forEach(update => {
                    const sectionName = template.sections[update.sectionIndex].name;
                    const questionInSection = formData.selectedQuestions.filter(q => q.section_name === sectionName)[update.questionIndex];

                    if (questionInSection) {
                        questionInSection.marks = update.marks;
                    }
                });

                // Recalculate total marks
                formData.totalMarks = formData.selectedQuestions.reduce((sum, q) => sum + parseInt(q.marks), 0);
            }

            if (!validateFormData(formData, true)) {
                return;
            }

            // Show loading state
            const downloadBtn = event.target;
            const originalText = downloadBtn.innerHTML;
            downloadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Generating Answer Key...';
            downloadBtn.disabled = true;

            // Send data to server for answer key PDF generation
            fetch('<?= base_url() ?>schooladmin/downloadAnswerKeyPDF', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                }
                throw new Error('Failed to generate answer key PDF');
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `answer_key_${formData.title.replace(/[^a-zA-Z0-9]/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showNotification('Answer Key PDF downloaded successfully!', 'success');
            })
            .catch(error => {
                console.error('Download error:', error);
                showNotification('Failed to download Answer Key PDF. Please try again.', 'error');
            })
            .finally(() => {
                // Restore button state
                downloadBtn.innerHTML = originalText;
                downloadBtn.disabled = false;
            });
        }

        function editQuestionPaper(paperId) {
            // Clear any existing error notifications first
            clearErrorNotifications();

            // Show loading notification
            showNotification('Loading question paper for editing...', 'info');

            const editUrl = `<?= base_url() ?>schooladmin/getQuestionPaperForEdit/${paperId}`;
            console.log('Edit URL:', editUrl); // Debug logging

            // Fetch paper data for editing
            fetch(editUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
                .then(response => {
                    console.log('Edit response status:', response.status); // Debug logging
                    console.log('Edit response headers:', response.headers); // Debug logging
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Edit response:', data); // Debug logging
                    console.log('Paper data:', data.data?.paper); // Debug paper data specifically
                    console.log('Paper subject:', data.data?.paper?.subject); // Debug subject specifically
                    if (data.success) {
                        // Close the details modal first
                        closePaperDetailsModal();

                        // Load the paper data into the form
                        loadPaperForEditing(data.data);

                        showNotification('Question paper loaded for editing', 'success');
                    } else {
                        console.error('Edit error:', data.message); // Debug logging
                        showNotification('Error: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error loading paper for edit:', error);
                    showNotification('Failed to load question paper for editing: ' + error.message, 'error');
                });
        }

        function clearErrorNotifications() {
            // Remove any existing error notifications
            const errorNotifications = document.querySelectorAll('.notification.error, .alert-danger, .bg-red-100');
            errorNotifications.forEach(notification => {
                if (notification.textContent.includes('Failed to load question paper for editing')) {
                    notification.remove();
                }
            });
        }

        function loadPaperForEditing(paperData) {
            console.log('Loading paper for editing:', paperData); // Debug logging
            console.log('paperData.paper:', paperData.paper); // Debug paper object
            console.log('paperData.questions:', paperData.questions); // Debug questions array

            const paper = paperData.paper;
            const questions = paperData.questions;

            // Validate that we have the expected data structure
            if (!paper) {
                console.error('Paper data is missing from response:', paperData);
                showNotification('Error: Invalid response structure - paper data missing.', 'error');
                return;
            }

            // Set editing mode
            window.editingPaperId = paper.id;

            // Validate required paper data
            if (!paper.subject || paper.subject.trim() === '') {
                console.error('Paper subject is missing:', paper);
                showNotification('Error: Question paper subject is missing. Cannot load for editing.', 'error');
                return;
            }

            if (!paper.standard) {
                console.error('Paper standard is missing:', paper);
                showNotification('Error: Question paper standard is missing. Cannot load for editing.', 'error');
                return;
            }

            // Determine template based on subject and standard
            // Handle subject key mapping more robustly
            let subjectKey = (paper.subject || '').toLowerCase().replace(/\s+/g, '');

            // If subject key is empty, use a default
            if (!subjectKey) {
                console.warn('Empty subject key, using default mathematics');
                subjectKey = 'mathematics';
            }

            // Handle special cases for subject mapping
            const subjectKeyMappings = {
                'computerscience': 'computerScience',
                'businessmathematics': 'businessMaths',
                'communicativeenglish': 'communicativeEnglish',
                'politicalscience': 'politicalScience',
                'advancedtamil': 'advancedTamil',
                'homescience': 'homeScience',
                'basicmechanicalengineering': 'basicMechanicalEngineering',
                'computertechnology': 'computerTechnology',
                'textileanddressdesigning': 'textileAndDressDesigning',
                'officemanagementandsecretaryship': 'officeManagementAndSecretaryship'
            };

            // Apply mapping if exists
            if (subjectKeyMappings[subjectKey]) {
                subjectKey = subjectKeyMappings[subjectKey];
            }

            console.log('Subject mapping:', paper.subject, '->', subjectKey); // Debug logging

            let template;
            let templateType;

            // Determine template type based on standard and ensure templates are loaded
            if (paper.standard == 10) {
                templateType = 'sslc';
                // Load SSLC templates if not already loaded
                if (!window.sslcTemplates) {
                    loadSSLCTemplate(true);
                }
                console.log('Available SSLC templates:', Object.keys(window.sslcTemplates || {}));
                template = window.sslcTemplates && window.sslcTemplates[subjectKey] ?
                          window.sslcTemplates[subjectKey] :
                          (window.sslcTemplates && window.sslcTemplates.mathematics ? window.sslcTemplates.mathematics : null);
            } else if (paper.standard == 11) {
                templateType = 'hsc1';
                // Load HSC1 templates if not already loaded
                if (!window.hsc1Templates) {
                    loadHSC1Template(true);
                }
                console.log('Available HSC1 templates:', Object.keys(window.hsc1Templates || {}));
                template = window.hsc1Templates && window.hsc1Templates[subjectKey] ?
                          window.hsc1Templates[subjectKey] :
                          (window.hsc1Templates && window.hsc1Templates.mathematics ? window.hsc1Templates.mathematics : null);
            } else if (paper.standard == 12) {
                templateType = 'hsc2';
                // Load HSC2 templates if not already loaded
                if (!window.hsc2Templates) {
                    loadHSC2Template(true);
                }
                console.log('Available HSC2 templates:', Object.keys(window.hsc2Templates || {}));
                template = window.hsc2Templates && window.hsc2Templates[subjectKey] ?
                          window.hsc2Templates[subjectKey] :
                          (window.hsc2Templates && window.hsc2Templates.mathematics ? window.hsc2Templates.mathematics : null);
            } else {
                // Default to SSLC for other standards
                templateType = 'sslc';
                // Load SSLC templates if not already loaded
                if (!window.sslcTemplates) {
                    loadSSLCTemplate(true);
                }
                console.log('Available SSLC templates (default):', Object.keys(window.sslcTemplates || {}));
                template = window.sslcTemplates && window.sslcTemplates[subjectKey] ?
                          window.sslcTemplates[subjectKey] :
                          (window.sslcTemplates && window.sslcTemplates.mathematics ? window.sslcTemplates.mathematics : null);
            }

            console.log('Selected template:', template); // Debug logging

            // Check if template is valid
            if (!template || !template.sections) {
                console.error('Invalid template selected:', template);
                showNotification('Error: Could not load template for this subject and standard', 'error');
                return;
            }

            // Set selected template
            window.selectedTemplate = {
                subject: paper.subject,
                type: templateType,
                template: template
            };

            // Show the form
            showQuestionPaperForm();

            // Wait for form to be fully rendered before setting values
            setTimeout(() => {
                // Fill form fields with error checking
                const setFieldValue = (id, value) => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.value = value || '';
                    } else {
                        console.warn(`Form field with ID '${id}' not found`);
                    }
                };

                setFieldValue('paperTitle', paper.title);
                setFieldValue('academicYear', paper.academic_year);
                setFieldValue('examType', paper.exam_type);
                setFieldValue('examDate', paper.exam_date);
                setFieldValue('examDuration', paper.duration); // Use examDuration instead of duration
                setFieldValue('instructions', paper.instructions);

                // Load questions into sections
                loadQuestionsForEditing(questions, template);
            }, 100);

            // Update form title to indicate editing
            const formTitle = document.querySelector('#questionPaperForm h2');
            if (formTitle) {
                formTitle.innerHTML = '<i class="fas fa-edit mr-2"></i>Edit Question Paper';
                formTitle.classList.add('text-orange-600');
            }

            // Add editing indicator
            const formContainer = document.getElementById('questionPaperForm');
            if (formContainer) {
                const editingBadge = document.createElement('div');
                editingBadge.className = 'mb-4 p-3 bg-orange-50 border border-orange-200 rounded-lg';
                editingBadge.innerHTML = `
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-orange-600 mr-2"></i>
                        <p class="text-sm text-orange-800">
                            <strong>Editing Mode:</strong> You are editing an existing question paper.
                            Changes will update the current paper.
                        </p>
                    </div>
                `;
                editingBadge.id = 'editing-indicator';

                const firstChild = formContainer.firstElementChild;
                if (firstChild) {
                    formContainer.insertBefore(editingBadge, firstChild.nextSibling);
                }
            }

            // Change submit button text
            const submitBtn = document.querySelector('#questionPaperForm button[onclick="handleQuestionPaperSubmission(event)"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Update Question Paper';
                submitBtn.classList.remove('bg-indigo-600', 'hover:bg-indigo-700');
                submitBtn.classList.add('bg-orange-600', 'hover:bg-orange-700');
            }
        }

        function loadQuestionsForEditing(questions, template) {
            console.log('Loading questions for editing:', questions, template); // Debug logging

            // Validate inputs
            if (!template || !template.sections) {
                console.error('Invalid template provided to loadQuestionsForEditing:', template);
                return;
            }

            if (!Array.isArray(questions)) {
                console.error('Invalid questions array provided to loadQuestionsForEditing:', questions);
                return;
            }

            // Group questions by section
            const questionsBySection = {};
            questions.forEach(question => {
                const sectionName = question.section_name;
                if (!questionsBySection[sectionName]) {
                    questionsBySection[sectionName] = [];
                }
                questionsBySection[sectionName].push(question);
            });

            console.log('Questions grouped by section:', questionsBySection); // Debug logging

            // Load questions for each section
            template.sections.forEach((section, sectionIndex) => {
                const sectionQuestions = questionsBySection[section.name] || [];

                if (sectionQuestions.length > 0) {
                    // Simulate loading questions for this section
                    displayQuestionsInSection(sectionQuestions, sectionIndex, section.name, '', section);

                    // Pre-select the questions
                    setTimeout(() => {
                        sectionQuestions.forEach((question, qIndex) => {
                            const checkbox = document.querySelector(`#questions-section-${sectionIndex} input[value="${question.id}"]`);
                            if (checkbox) {
                                checkbox.checked = true;

                                // Set custom marks if different from default
                                if (question.marks !== section.marksEach) {
                                    checkbox.setAttribute('data-custom-marks', question.marks);
                                }

                                // Trigger change event to update counters
                                checkbox.dispatchEvent(new Event('change'));
                            }
                        });
                    }, 500);
                }
            });
        }

        function deleteQuestionPaper(paperId) {
            if (!confirm('Are you sure you want to delete this question paper? This action cannot be undone.')) {
                return;
            }

            // Show loading notification
            showNotification('Deleting question paper...', 'info');

            fetch(`<?= base_url() ?>schooladmin/deleteQuestionPaper/${paperId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    // Reload the question papers list
                    loadQuestionPapersList();
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting question paper:', error);
                showNotification('Failed to delete question paper', 'error');
            });
        }

        // Notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;

            // Set colors based on type
            switch(type) {
                case 'success':
                    notification.className += ' bg-green-500 text-white';
                    break;
                case 'error':
                    notification.className += ' bg-red-500 text-white';
                    break;
                case 'info':
                    notification.className += ' bg-blue-500 text-white';
                    break;
                default:
                    notification.className += ' bg-gray-500 text-white';
            }

            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-white hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // Handle navigation to dashboard sections
        document.addEventListener('DOMContentLoaded', function() {
            // Add click handlers for navigation links that go to dashboard sections
            const navLinks = document.querySelectorAll('a[href*="#"]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    const href = this.getAttribute('href');
                    if (href.includes('schooladmin/dashboard#')) {
                        // Let the browser handle the navigation normally
                        // The dashboard will handle the hash section switching
                        return true;
                    }
                });
            });
        });
    </script>
</body>
</html>
