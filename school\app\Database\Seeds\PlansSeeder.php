<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;
use App\Models\PlanModel;

class PlansSeeder extends Seeder
{
    public function run()
    {
        echo "Creating default subscription plans...\n";

        $planModel = new PlanModel();
        
        // Create default plans with features
        $planModel->createDefaultPlans();
        
        echo "Default plans created successfully!\n";
        echo "Plans created:\n";
        echo "- Free Plan (₹0/month)\n";
        echo "- Basic Plan (₹999/month, ₹9,999/year)\n";
        echo "- Professional Plan (₹2,499/month, ₹24,999/year) - Popular\n";
        echo "- Enterprise Plan (₹4,999/month, ₹49,999/year)\n";
        
        // Display plan features summary
        $plans = $planModel->getActivePlansWithFeatures();
        
        foreach ($plans as $plan) {
            echo "\n{$plan['display_name']} Features:\n";
            foreach ($plan['features'] as $feature) {
                $value = $feature['is_unlimited'] ? 'Unlimited' : $feature['feature_value'];
                echo "  - {$feature['feature_name']}: {$value}\n";
            }
        }
        
        echo "\nPlans seeding completed!\n";
    }
}
