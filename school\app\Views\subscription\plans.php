<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?> - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .billing-cycle {
            background-color: white !important;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e") !important;
            background-position: right 0.5rem center !important;
            background-repeat: no-repeat !important;
            background-size: 1.5em 1.5em !important;
            padding-right: 2.5rem !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
        }
        .billing-cycle:focus {
            outline: 2px solid #6366f1 !important;
            outline-offset: 2px !important;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-12">
        <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 mb-4"><?= $title ?></h1>
                <p class="text-xl text-gray-600">Choose the perfect plan for your school</p>
            </div>

            <!-- Plans Grid -->
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                
                <?php foreach ($plans as $plan): ?>
                <div class="<?= $plan['is_popular'] ? 'bg-indigo-50 border-2 border-indigo-500' : 'bg-white border-2 border-gray-200' ?> rounded-xl p-8 relative">
                    
                    <?php if ($plan['is_popular']): ?>
                    <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span class="bg-indigo-500 text-white px-4 py-1 rounded-full text-sm font-semibold">Most Popular</span>
                    </div>
                    <?php endif; ?>

                    <div class="text-center">
                        <!-- Plan Name -->
                        <h3 class="text-2xl font-bold mb-4"><?= $plan['display_name'] ?></h3>
                        
                        <!-- Price -->
                        <div class="mb-6">
                            <div class="text-4xl font-bold <?= $plan['is_popular'] ? 'text-indigo-600' : 'text-gray-800' ?> mb-2">
                                ₹<?= number_format($plan['price_monthly']) ?>
                            </div>
                            <p class="text-gray-600">
                                <?= $plan['price_monthly'] == 0 ? '30 days free trial' : 'per school/month' ?>
                            </p>
                            <?php if ($plan['price_yearly'] > 0): ?>
                            <p class="text-sm text-green-600 mt-1">
                                Save ₹<?= number_format(($plan['price_monthly'] * 12) - $plan['price_yearly']) ?> with yearly billing
                            </p>
                            <?php endif; ?>
                        </div>

                        <!-- Features -->
                        <ul class="text-left space-y-3 mb-8">
                            <?php foreach ($plan['features'] as $feature): ?>
                            <li class="flex items-start">
                                <i class="fas fa-check text-green-500 mr-3 mt-1"></i>
                                <span>
                                    <?= $feature['feature_name'] ?>: 
                                    <?php if ($feature['is_unlimited']): ?>
                                        <strong class="text-green-600">Unlimited</strong>
                                    <?php else: ?>
                                        <strong><?= $feature['feature_value'] ?></strong>
                                    <?php endif; ?>
                                </span>
                            </li>
                            <?php endforeach; ?>
                        </ul>

                        <!-- Billing Cycle Selection -->
                        <?php if ($plan['price_monthly'] > 0): ?>
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Billing Cycle</label>
                            <select class="billing-cycle w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white" data-plan-id="<?= $plan['id'] ?>">
                                <option value="monthly" selected>Monthly - ₹<?= number_format($plan['price_monthly']) ?></option>
                                <option value="yearly">Yearly - ₹<?= number_format($plan['price_yearly']) ?> (Save ₹<?= number_format(($plan['price_monthly'] * 12) - $plan['price_yearly']) ?>)</option>
                            </select>
                        </div>
                        <?php endif; ?>

                        <!-- Select Button -->
                        <button 
                            class="select-plan-btn w-full py-3 rounded-lg text-lg font-semibold transition duration-300 <?= $plan['is_popular'] ? 'bg-indigo-600 text-white hover:bg-indigo-700' : 'bg-gray-600 text-white hover:bg-gray-700' ?>"
                            data-plan-id="<?= $plan['id'] ?>"
                            data-plan-name="<?= $plan['name'] ?>"
                            data-plan-display="<?= $plan['display_name'] ?>"
                            data-monthly-price="<?= $plan['price_monthly'] ?>"
                            data-yearly-price="<?= $plan['price_yearly'] ?>"
                        >
                            <?= $plan['price_monthly'] == 0 ? 'Start Free Trial' : 'Choose Plan' ?>
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>

            </div>

            <!-- Back to Dashboard -->
            <div class="text-center mt-12">
                <a href="/schooladmin/dashboard" class="text-indigo-600 hover:text-indigo-800 font-medium">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
            </div>

        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Processing your selection...</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle billing cycle changes
            document.querySelectorAll('.billing-cycle').forEach(select => {
                select.addEventListener('change', function() {
                    const planId = this.dataset.planId;
                    const button = document.querySelector(`.select-plan-btn[data-plan-id="${planId}"]`);
                    const cycle = this.value;

                    // Update button text based on cycle
                    if (cycle === 'yearly') {
                        button.textContent = 'Choose Yearly Plan';
                    } else {
                        button.textContent = 'Choose Plan';
                    }
                });
            });

            // Handle plan selection
            document.querySelectorAll('.select-plan-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const planId = this.dataset.planId;
                    const planName = this.dataset.planName;
                    const monthlyPrice = parseFloat(this.dataset.monthlyPrice);
                    
                    // Get billing cycle
                    const cycleSelect = document.querySelector(`select[data-plan-id="${planId}"]`);
                    const billingCycle = cycleSelect ? cycleSelect.value : 'monthly';
                    
                    // Show loading
                    document.getElementById('loadingModal').classList.remove('hidden');
                    document.getElementById('loadingModal').classList.add('flex');
                    
                    // Send selection to server
                    fetch('/subscription/select-plan', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: `plan_id=${planId}&billing_cycle=${billingCycle}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('loadingModal').classList.add('hidden');
                        document.getElementById('loadingModal').classList.remove('flex');
                        
                        if (data.success) {
                            if (data.amount > 0) {
                                // Redirect to payment
                                window.location.href = `/payment/process?plan_id=${planId}&billing_cycle=${billingCycle}`;
                            } else {
                                // Free plan - redirect to dashboard
                                window.location.href = '/schooladmin/dashboard';
                            }
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        document.getElementById('loadingModal').classList.add('hidden');
                        document.getElementById('loadingModal').classList.remove('flex');
                        alert('An error occurred. Please try again.');
                        console.error('Error:', error);
                    });
                });
            });
        });
    </script>
</body>
</html>
