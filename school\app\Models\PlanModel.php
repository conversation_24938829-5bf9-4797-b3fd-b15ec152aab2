<?php

namespace App\Models;

use CodeIgniter\Model;

class PlanModel extends Model
{
    protected $table = 'plans';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name', 'display_name', 'description', 'price_monthly', 'price_yearly',
        'currency', 'billing_cycle', 'is_active', 'is_popular', 'sort_order'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'name' => 'required|max_length[100]|is_unique[plans.name,id,{id}]',
        'display_name' => 'required|max_length[100]',
        'price_monthly' => 'required|decimal',
        'price_yearly' => 'required|decimal',
        'currency' => 'required|max_length[3]',
        'billing_cycle' => 'required|in_list[monthly,yearly,lifetime]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;

    /**
     * Get all active plans with features
     */
    public function getActivePlansWithFeatures()
    {
        $plans = $this->where('is_active', true)
                     ->orderBy('sort_order', 'ASC')
                     ->findAll();

        foreach ($plans as &$plan) {
            $plan['features'] = $this->getPlanFeatures($plan['id']);
        }

        return $plans;
    }

    /**
     * Get plan features
     */
    public function getPlanFeatures($planId)
    {
        $planFeatureModel = new PlanFeatureModel();
        return $planFeatureModel->where('plan_id', $planId)->findAll();
    }

    /**
     * Get plan by name
     */
    public function getPlanByName($name)
    {
        return $this->where('name', $name)->first();
    }

    /**
     * Get popular plans
     */
    public function getPopularPlans()
    {
        return $this->where('is_active', true)
                   ->where('is_popular', true)
                   ->orderBy('sort_order', 'ASC')
                   ->findAll();
    }

    /**
     * Create default plans (matching landing page)
     */
    public function createDefaultPlans()
    {
        $defaultPlans = [
            [
                'name' => 'trial',
                'display_name' => 'Free Trial',
                'description' => '30 days free trial to get started with basic features',
                'price_monthly' => 0.00,
                'price_yearly' => 0.00,
                'currency' => 'INR',
                'billing_cycle' => 'monthly',
                'is_active' => true,
                'is_popular' => false,
                'sort_order' => 1
            ],
            [
                'name' => 'professional',
                'display_name' => 'Professional',
                'description' => 'Full-featured plan for schools with unlimited access',
                'price_monthly' => 2999.00,
                'price_yearly' => 29990.00,
                'currency' => 'INR',
                'billing_cycle' => 'monthly',
                'is_active' => true,
                'is_popular' => true,
                'sort_order' => 2
            ]
        ];

        foreach ($defaultPlans as $plan) {
            // Check if plan already exists
            if (!$this->getPlanByName($plan['name'])) {
                $planId = $this->insert($plan);
                
                // Create default features for this plan
                $this->createDefaultPlanFeatures($planId, $plan['name']);
            }
        }

        return true;
    }

    /**
     * Create default features for a plan
     */
    private function createDefaultPlanFeatures($planId, $planName)
    {
        $planFeatureModel = new PlanFeatureModel();
        
        $features = $this->getDefaultFeaturesByPlan($planName);
        
        foreach ($features as $feature) {
            $feature['plan_id'] = $planId;
            $planFeatureModel->insert($feature);
        }
    }

    /**
     * Get default features by plan name (updated for 2-plan system)
     */
    private function getDefaultFeaturesByPlan($planName)
    {
        switch ($planName) {
            case 'trial':
                return [
                    ['feature_key' => 'max_questions', 'feature_name' => 'Maximum Questions', 'feature_value' => '50', 'value_type' => 'integer'],
                    ['feature_key' => 'max_subjects', 'feature_name' => 'Maximum Subjects', 'feature_value' => '5', 'value_type' => 'integer'],
                    ['feature_key' => 'basic_user_roles', 'feature_name' => 'Basic User Roles', 'feature_value' => 'true', 'value_type' => 'boolean'],
                    ['feature_key' => 'email_support', 'feature_name' => 'Email Support', 'feature_value' => 'true', 'value_type' => 'boolean'],
                    ['feature_key' => 'trial_days', 'feature_name' => 'Trial Period Days', 'feature_value' => '30', 'value_type' => 'integer'],
                    ['feature_key' => 'max_staff', 'feature_name' => 'Maximum Staff Members', 'feature_value' => '10', 'value_type' => 'integer']
                ];

            case 'professional':
                return [
                    ['feature_key' => 'max_questions', 'feature_name' => 'Unlimited Questions', 'feature_value' => '0', 'value_type' => 'integer', 'is_unlimited' => 1],
                    ['feature_key' => 'max_subjects', 'feature_name' => 'Unlimited Subjects', 'feature_value' => '0', 'value_type' => 'integer', 'is_unlimited' => 1],
                    ['feature_key' => 'full_role_management', 'feature_name' => 'Full Role Management', 'feature_value' => 'true', 'value_type' => 'boolean'],
                    ['feature_key' => 'audit_logging', 'feature_name' => 'Audit Logging', 'feature_value' => 'true', 'value_type' => 'boolean'],
                    ['feature_key' => 'priority_support', 'feature_name' => 'Priority Support', 'feature_value' => 'true', 'value_type' => 'boolean'],
                    ['feature_key' => 'max_staff', 'feature_name' => 'Unlimited Staff Members', 'feature_value' => '0', 'value_type' => 'integer', 'is_unlimited' => 1],
                    ['feature_key' => 'export_papers', 'feature_name' => 'Export Question Papers', 'feature_value' => 'true', 'value_type' => 'boolean'],
                    ['feature_key' => 'bulk_import', 'feature_name' => 'Bulk Import Questions', 'feature_value' => 'true', 'value_type' => 'boolean'],
                    ['feature_key' => 'analytics', 'feature_name' => 'Advanced Analytics', 'feature_value' => 'true', 'value_type' => 'boolean']
                ];

            default:
                return [];
        }
    }
}
