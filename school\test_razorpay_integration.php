<?php
/**
 * Test Razorpay Integration
 * Run this from the school directory: php test_razorpay_integration.php
 */

require_once 'vendor/autoload.php';

// Load CodeIgniter
$pathsPath = realpath('app/Config/Paths.php');
require $pathsPath;
$paths = new Config\Paths();
require $paths->systemDirectory . '/Boot.php';

// Boot CodeIgniter
exit(\CodeIgniter\Boot::bootConsole($paths));

use App\Services\RazorpayService;
use Config\Razorpay as RazorpayConfig;

echo "=== Testing Razorpay Integration ===\n\n";

try {
    // Test 1: Check Configuration
    echo "1. Testing Razorpay Configuration:\n";
    $config = new RazorpayConfig();
    echo "   - Test Mode: " . ($config->testMode ? 'YES' : 'NO') . "\n";
    echo "   - Key ID: " . substr($config->getKeyId(), 0, 12) . "...\n";
    echo "   - Key Secret: " . (strlen($config->getKeySecret()) > 0 ? 'SET' : 'NOT SET') . "\n";
    echo "   - Currency: " . $config->currency . "\n\n";

    // Test 2: Test Service Initialization
    echo "2. Testing RazorpayService:\n";
    $razorpayService = new RazorpayService();
    echo "   - Service initialized successfully ✓\n\n";

    // Test 3: Test Order Creation
    echo "3. Testing Order Creation:\n";
    $orderResult = $razorpayService->createOrder(
        999, // ₹999
        'INR',
        'test_order_' . time(),
        ['test' => true, 'school_id' => 1]
    );

    if ($orderResult['success']) {
        echo "   - Order created successfully ✓\n";
        echo "   - Order ID: " . $orderResult['order_id'] . "\n";
        echo "   - Amount: ₹999\n\n";
        
        // Test 4: Test Payment Options
        echo "4. Testing Payment Options:\n";
        $paymentOptions = $razorpayService->getPaymentOptions(
            $orderResult['order_id'],
            999,
            'Test School',
            '<EMAIL>'
        );
        
        echo "   - Payment options generated ✓\n";
        echo "   - Key: " . substr($paymentOptions['key'], 0, 12) . "...\n";
        echo "   - Amount: ₹" . ($paymentOptions['amount'] / 100) . "\n";
        echo "   - Currency: " . $paymentOptions['currency'] . "\n";
        echo "   - School Name: " . $paymentOptions['prefill']['name'] . "\n\n";
        
        echo "✅ ALL TESTS PASSED!\n";
        echo "Razorpay integration is working correctly.\n";
        echo "You can now process payments through Razorpay.\n\n";
        
    } else {
        echo "   - Order creation failed ❌\n";
        echo "   - Error: " . $orderResult['error'] . "\n\n";
        echo "❌ RAZORPAY INTEGRATION HAS ISSUES\n";
        echo "Please check your API credentials.\n\n";
    }

} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
    echo "Razorpay integration failed.\n\n";
}

echo "=== Test Complete ===\n";
