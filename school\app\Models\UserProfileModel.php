<?php

namespace App\Models;

use CodeIgniter\Model;

class UserProfileModel extends Model
{
    protected $table = 'user_profiles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'user_id', 'school_id', 'designation', 'phone'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation rules
    protected $validationRules = [
        'user_id' => 'required|integer',
        'school_id' => 'required|integer',
        'phone' => 'permit_empty|min_length[10]|max_length[20]'
    ];

    /**
     * Get profile by user ID
     */
    public function getProfileByUser($userId)
    {
        return $this->where('user_id', $userId)->first();
    }

    /**
     * Create or update profile
     */
    public function saveProfile($userId, $schoolId, $data)
    {
        $existingProfile = $this->getProfileByUser($userId);
        
        $profileData = array_merge($data, [
            'user_id' => $userId,
            'school_id' => $schoolId
        ]);

        if ($existingProfile) {
            return $this->update($existingProfile['id'], $profileData);
        } else {
            return $this->insert($profileData);
        }
    }
}
