<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Reset Password' ?> - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .password-strength {
            height: 4px;
            border-radius: 2px;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Main Card -->
        <div class="bg-white rounded-2xl card-shadow overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6 text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-lock text-2xl text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-white">Reset Password</h1>
                <p class="text-indigo-100 mt-2">Create a new password for your account</p>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <!-- Success/Error Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            <?= session()->getFlashdata('success') ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Dynamic Message Container -->
                <div id="messageContainer" class="hidden mb-6"></div>

                <!-- Account Info -->
                <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-user text-blue-500 mr-3"></i>
                        <div class="text-sm text-blue-700">
                            <p class="font-medium">Resetting password for:</p>
                            <p class="text-blue-600"><?= esc($email) ?></p>
                        </div>
                    </div>
                </div>

                <!-- Reset Password Form -->
                <form id="resetPasswordForm" class="space-y-6">
                    <?= csrf_field() ?>
                    <input type="hidden" name="token" value="<?= esc($token) ?>">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            New Password <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="password" name="password" id="password" required
                                   class="w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                                   placeholder="Enter new password" minlength="6">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 flex items-center pr-4">
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                            </button>
                        </div>
                        <!-- Password Strength Indicator -->
                        <div class="mt-2">
                            <div class="password-strength bg-gray-200" id="passwordStrength"></div>
                            <p class="text-xs text-gray-500 mt-1" id="passwordStrengthText">Password strength</p>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm New Password <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="password" name="confirm_password" id="confirmPassword" required
                                   class="w-full px-4 py-3 pl-12 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                                   placeholder="Confirm new password" minlength="6">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <button type="button" id="toggleConfirmPassword" class="absolute inset-y-0 right-0 flex items-center pr-4">
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                            </button>
                        </div>
                        <div id="passwordMatch" class="mt-1 text-xs hidden"></div>
                    </div>

                    <!-- Password Requirements -->
                    <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <p class="text-sm font-medium text-gray-700 mb-2">Password Requirements:</p>
                        <ul class="text-xs text-gray-600 space-y-1">
                            <li class="flex items-center" id="req-length">
                                <i class="fas fa-circle text-gray-300 mr-2 text-xs"></i>
                                At least 6 characters long
                            </li>
                            <li class="flex items-center" id="req-match">
                                <i class="fas fa-circle text-gray-300 mr-2 text-xs"></i>
                                Passwords must match
                            </li>
                        </ul>
                    </div>

                    <button type="submit" id="submitBtn"
                            class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300 flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-save mr-2"></i>
                        Reset Password
                    </button>
                </form>

                <!-- Back to Login -->
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">
                        Remember your password?
                        <a href="<?= $userType === 'school' ? base_url('login/schooladmin') : base_url('staff/login') ?>" 
                           class="text-indigo-600 font-medium hover:text-indigo-500 hover:underline">
                            Back to Login
                        </a>
                    </p>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-6 text-white text-sm opacity-75">
            <p>&copy; <?= date('Y') ?> School Question Bank. All rights reserved.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('resetPasswordForm');
            const submitBtn = document.getElementById('submitBtn');
            const messageContainer = document.getElementById('messageContainer');
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            const passwordStrength = document.getElementById('passwordStrength');
            const passwordStrengthText = document.getElementById('passwordStrengthText');
            const passwordMatch = document.getElementById('passwordMatch');

            // Password visibility toggles
            document.getElementById('togglePassword').addEventListener('click', function() {
                togglePasswordVisibility('password', this);
            });

            document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
                togglePasswordVisibility('confirmPassword', this);
            });

            function togglePasswordVisibility(inputId, button) {
                const input = document.getElementById(inputId);
                const icon = button.querySelector('i');
                
                if (input.type === 'password') {
                    input.type = 'text';
                    icon.classList.remove('fa-eye');
                    icon.classList.add('fa-eye-slash');
                } else {
                    input.type = 'password';
                    icon.classList.remove('fa-eye-slash');
                    icon.classList.add('fa-eye');
                }
            }

            // Password strength checker
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                updatePasswordStrength(strength);
                updateRequirements();
            });

            // Password match checker
            confirmPasswordInput.addEventListener('input', function() {
                updatePasswordMatch();
                updateRequirements();
            });

            function calculatePasswordStrength(password) {
                let score = 0;
                if (password.length >= 6) score += 25;
                if (password.length >= 8) score += 25;
                if (/[a-z]/.test(password) && /[A-Z]/.test(password)) score += 25;
                if (/\d/.test(password)) score += 25;
                return score;
            }

            function updatePasswordStrength(strength) {
                let color, text;
                if (strength < 25) {
                    color = 'bg-red-500';
                    text = 'Very Weak';
                } else if (strength < 50) {
                    color = 'bg-orange-500';
                    text = 'Weak';
                } else if (strength < 75) {
                    color = 'bg-yellow-500';
                    text = 'Fair';
                } else {
                    color = 'bg-green-500';
                    text = 'Strong';
                }

                passwordStrength.className = `password-strength ${color}`;
                passwordStrength.style.width = strength + '%';
                passwordStrengthText.textContent = text;
            }

            function updatePasswordMatch() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                if (confirmPassword.length > 0) {
                    if (password === confirmPassword) {
                        passwordMatch.className = 'mt-1 text-xs text-green-600';
                        passwordMatch.innerHTML = '<i class="fas fa-check mr-1"></i>Passwords match';
                        passwordMatch.classList.remove('hidden');
                    } else {
                        passwordMatch.className = 'mt-1 text-xs text-red-600';
                        passwordMatch.innerHTML = '<i class="fas fa-times mr-1"></i>Passwords do not match';
                        passwordMatch.classList.remove('hidden');
                    }
                } else {
                    passwordMatch.classList.add('hidden');
                }
            }

            function updateRequirements() {
                const password = passwordInput.value;
                const confirmPassword = confirmPasswordInput.value;

                // Length requirement
                const lengthReq = document.getElementById('req-length');
                if (password.length >= 6) {
                    lengthReq.innerHTML = '<i class="fas fa-check text-green-500 mr-2 text-xs"></i>At least 6 characters long';
                } else {
                    lengthReq.innerHTML = '<i class="fas fa-circle text-gray-300 mr-2 text-xs"></i>At least 6 characters long';
                }

                // Match requirement
                const matchReq = document.getElementById('req-match');
                if (password.length > 0 && confirmPassword.length > 0 && password === confirmPassword) {
                    matchReq.innerHTML = '<i class="fas fa-check text-green-500 mr-2 text-xs"></i>Passwords must match';
                } else {
                    matchReq.innerHTML = '<i class="fas fa-circle text-gray-300 mr-2 text-xs"></i>Passwords must match';
                }

                // Enable/disable submit button
                const isValid = password.length >= 6 && password === confirmPassword;
                submitBtn.disabled = !isValid;
            }

            // Form submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const originalText = submitBtn.innerHTML;
                
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Resetting...';
                
                // Clear previous messages
                messageContainer.classList.add('hidden');
                
                fetch('<?= base_url('forgot-password/reset') ?>', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Reset password response:', data);
                    if (data.success) {
                        showMessage(data.message, 'success');
                        setTimeout(() => {
                            // Ensure the redirect URL is properly formatted
                            const redirectUrl = data.redirect.startsWith('http') ? data.redirect : '<?= base_url() ?>' + data.redirect;
                            console.log('Redirecting to:', redirectUrl);
                            window.location.href = redirectUrl;
                        }, 2000);
                    } else {
                        showMessage(data.message, 'error');
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('An error occurred. Please try again.', 'error');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                });
            });

            function showMessage(message, type) {
                const isSuccess = type === 'success';
                const bgColor = isSuccess ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
                const icon = isSuccess ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
                
                messageContainer.innerHTML = `
                    <div class="p-4 ${bgColor} border rounded-lg">
                        <div class="flex items-center">
                            <i class="${icon} mr-2"></i>
                            ${message}
                        </div>
                    </div>
                `;
                messageContainer.classList.remove('hidden');
                
                // Scroll to message
                messageContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    </script>
</body>
</html>
