<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>School Details</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-gray-50 to-indigo-50 min-h-screen p-6 font-['Poppins']">

    <div class="max-w-4xl mx-auto bg-white shadow-xl rounded-xl p-8 animate-fade-in backdrop-blur-sm bg-opacity-90 border border-white border-opacity-30">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-600 to-purple-600">
                School Details
            </h2>
            <div class="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center shadow-inner">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
            </div>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 text-gray-700">
            <!-- School Info Cards -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-indigo-100">
                <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    School Name
                </span>
                <div class="text-lg font-semibold text-gray-800"><?= esc($school['name']) ?></div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-indigo-100">
                <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    Email
                </span>
                <div class="text-lg font-semibold text-gray-800"><?= esc($school['email']) ?></div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-indigo-100">
                <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    Phone
                </span>
                <div class="text-lg font-semibold text-gray-800"><?= esc($school['phone']) ?></div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-indigo-100">
                <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Address
                </span>
                <div class="text-lg font-semibold text-gray-800"><?= esc($school['address']) ?></div>
            </div>

            <!-- Status Card with Enhanced Styling -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-indigo-100">
                <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Status
                </span>
                <div class="text-lg font-semibold">
                    <span class="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full text-sm font-medium
                        <?= $school['status'] === 'active' ? 'bg-green-100 text-green-800 shadow-green-sm' :
                            ($school['status'] === 'inactive' ? 'bg-yellow-100 text-yellow-800 shadow-yellow-sm' : 'bg-red-100 text-red-800 shadow-red-sm') ?>">
                        <span class="h-2 w-2 rounded-full 
                            <?= $school['status'] === 'active' ? 'bg-green-500' :
                                ($school['status'] === 'inactive' ? 'bg-yellow-500' : 'bg-red-500') ?>"></span>
                        <?= ucfirst(esc($school['status'])) ?>
                    </span>
                </div>
            </div>

            <!-- Plan ID Card -->
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-100 hover:shadow-sm transition-all duration-200 hover:border-indigo-100">
                <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Plan ID
                </span>
                <div class="text-lg font-semibold text-gray-800"><?= esc($school['plan_id']) ?></div>
            </div>

            <!-- Rejection Reason (if applicable) -->
            <?php if ($school['status'] === 'rejected' && !empty($school['rejection_reason'])): ?>
                <div class="sm:col-span-2 bg-red-50 p-4 rounded-lg border border-red-100 animate-pulse-once">
                    <span class="font-medium text-red-600 flex items-center gap-2 mb-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                        Rejection Reason
                    </span>
                    <div class="text-red-700"><?= esc($school['rejection_reason']) ?></div>
                </div>
            <?php endif; ?>

            <!-- Timestamps -->
            <div class="sm:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        Created
                    </span>
                    <div class="text-gray-800"><?= esc($school['created_at']) ?></div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-100">
                    <span class="font-medium text-gray-500 flex items-center gap-2 mb-1">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Last Updated
                    </span>
                    <div class="text-gray-800"><?= esc($school['updated_at']) ?></div>
                </div>
            </div>
        </div>

        <!-- Back Button with Enhanced Styling -->
        <a href="<?= site_url('superadmin/dashboard') ?>" 
           class="inline-flex items-center mt-8 px-5 py-2.5 bg-gradient-to-r from-indigo-600 to-indigo-700 text-white rounded-lg hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-0.5">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to Dashboard
        </a>
    </div>

    <style>
        @keyframes fade-in {
            from { opacity: 0; transform: translateY(10px); }
            to   { opacity: 1; transform: translateY(0); }
        }
        @keyframes pulse-once {
            0% { opacity: 0.6; transform: scale(0.98); }
            50% { opacity: 1; transform: scale(1.01); }
            100% { opacity: 1; transform: scale(1); }
        }
        .animate-fade-in {
            animation: fade-in 0.5s ease-out forwards;
        }
        .animate-pulse-once {
            animation: pulse-once 1s ease-in-out;
        }
        .shadow-green-sm {
            box-shadow: 0 1px 10px -2px rgba(16, 185, 129, 0.3);
        }
        .shadow-yellow-sm {
            box-shadow: 0 1px 10px -2px rgba(234, 179, 8, 0.3);
        }
        .shadow-red-sm {
            box-shadow: 0 1px 10px -2px rgba(239, 68, 68, 0.3);
        }
        body {
            font-family: 'Poppins', sans-serif;
        }
    </style>

</body>
</html>