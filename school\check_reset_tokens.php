<?php
/**
 * Check Reset Tokens in Database
 */

echo "=== Checking Reset Tokens in Database ===\n\n";

// Database connection
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'school';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Connected to database successfully\n";
    echo "🕒 Current server time: " . date('Y-m-d H:i:s') . "\n";

    // Check database time
    $stmt = $pdo->prepare("SELECT NOW() as db_time");
    $stmt->execute();
    $dbTime = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "🕒 Database time: " . $dbTime['db_time'] . "\n\n";
    
    // First, let's check the table structure
    $stmt = $pdo->prepare("DESCRIBE password_resets");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "📋 Table structure:\n";
    foreach ($columns as $column) {
        echo "  - " . $column['Field'] . " (" . $column['Type'] . ")\n";
    }
    echo "\n";

    // Clean up expired tokens first
    $stmt = $pdo->prepare("DELETE FROM password_resets WHERE expires_at < NOW()");
    $stmt->execute();
    $deletedCount = $stmt->rowCount();
    if ($deletedCount > 0) {
        echo "🧹 Cleaned up $deletedCount expired tokens\n\n";
    }

    // Check for ALL reset tokens first (to see what's happening)
    $stmt = $pdo->prepare("SELECT *, (expires_at > NOW()) as is_valid FROM password_resets ORDER BY created_at DESC LIMIT 5");
    $stmt->execute();
    $allTokens = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($allTokens)) {
        echo "📋 All recent tokens (including expired):\n";
        foreach ($allTokens as $token) {
            echo "  - Token: " . substr($token['token'], 0, 15) . "...\n";
            echo "    Email: " . $token['email'] . "\n";
            echo "    Expires: " . $token['expires_at'] . "\n";
            echo "    Valid: " . ($token['is_valid'] ? 'Yes' : 'No') . "\n";
            echo "    Used: " . ($token['used'] ? 'Yes' : 'No') . "\n\n";
        }
    }

    // Check for valid reset tokens (adjust query based on actual columns)
    $stmt = $pdo->prepare("SELECT * FROM password_resets WHERE expires_at > NOW() AND used = 0 ORDER BY created_at DESC LIMIT 5");
    $stmt->execute();
    $tokens = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($tokens)) {
        echo "❌ No valid reset tokens found\n";
        echo "Please request a password reset first.\n";
    } else {
        echo "✅ Found " . count($tokens) . " valid reset token(s):\n\n";
        
        foreach ($tokens as $index => $token) {
            echo "Token #" . ($index + 1) . ":\n";
            echo "  - Token: " . substr($token['token'], 0, 20) . "...\n";
            echo "  - Email: " . $token['email'] . "\n";
            echo "  - User Type: " . $token['user_type'] . "\n";
            echo "  - Expires: " . $token['expires_at'] . "\n";
            echo "  - Created: " . $token['created_at'] . "\n";
            if (isset($token['used_at'])) {
                echo "  - Used: " . ($token['used_at'] ? $token['used_at'] : 'Not used') . "\n\n";
            } else {
                echo "  - Status: Active\n\n";
            }
            
            // For testing, let's use the first token
            if ($index === 0) {
                echo "🔗 Reset URL for testing:\n";
                echo "http://localhost/schoolquestionbank/school/public/forgot-password/reset?token=" . $token['token'] . "\n\n";
            }
        }
    }
    
    // Also check all tokens (including expired)
    $stmt = $pdo->prepare("SELECT COUNT(*) as total,
                                  SUM(CASE WHEN expires_at < NOW() THEN 1 ELSE 0 END) as expired,
                                  SUM(CASE WHEN expires_at > NOW() THEN 1 ELSE 0 END) as valid
                           FROM password_resets");
    $stmt->execute();
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "📊 Token Statistics:\n";
    echo "  - Total tokens: " . $stats['total'] . "\n";
    echo "  - Expired tokens: " . $stats['expired'] . "\n";
    echo "  - Valid tokens: " . $stats['valid'] . "\n\n";
    
} catch (PDOException $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "=== Check Complete ===\n";
