<?php
// Simple test to check users in database
$host = 'localhost';
$dbname = 'school';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "❌ Users table does not exist!\n";
        exit;
    }
    
    // Get all users
    $stmt = $pdo->query("SELECT u.*, s.name as school_name FROM users u LEFT JOIN schools s ON s.id = u.school_id WHERE u.is_deleted = 0 ORDER BY u.created_at DESC");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "📊 Total users found: " . count($users) . "\n\n";
    
    if (count($users) == 0) {
        echo "No users found. Let's create some test users...\n\n";
        
        // Check if we have any schools
        $stmt = $pdo->query("SELECT * FROM schools WHERE status = 'active' LIMIT 1");
        $school = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$school) {
            echo "❌ No active schools found. Creating test school...\n";
            
            // Create test school
            $stmt = $pdo->prepare("INSERT INTO schools (name, email, password, phone, address, status, plan_id, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())");
            $result = $stmt->execute([
                'Test School',
                '<EMAIL>',
                password_hash('password123', PASSWORD_DEFAULT),
                '+1234567890',
                '123 Test Street, Test City',
                'active',
                1
            ]);
            
            if ($result) {
                $schoolId = $pdo->lastInsertId();
                echo "✅ Test school created with ID: $schoolId\n";
                $school = ['id' => $schoolId, 'name' => 'Test School'];
            } else {
                echo "❌ Failed to create test school\n";
                exit;
            }
        }
        
        echo "🏫 Using school: " . $school['name'] . " (ID: " . $school['id'] . ")\n\n";
        
        // Create test users
        $testUsers = [
            [
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'active'
            ],
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'active'
            ],
            [
                'name' => 'Mike Davis',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'inactive'
            ],
            [
                'name' => 'Emily Wilson',
                'email' => '<EMAIL>',
                'password' => password_hash('password123', PASSWORD_DEFAULT),
                'status' => 'active'
            ]
        ];
        
        foreach ($testUsers as $user) {
            $stmt = $pdo->prepare("INSERT INTO users (school_id, name, email, password, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
            $result = $stmt->execute([
                $school['id'],
                $user['name'],
                $user['email'],
                $user['password'],
                $user['status']
            ]);
            
            if ($result) {
                $userId = $pdo->lastInsertId();
                echo "✅ Created user: " . $user['name'] . " (ID: $userId, Status: " . $user['status'] . ")\n";
                
                // Create user profile
                $designations = ['Teacher', 'Principal', 'Vice Principal', 'Head of Department'];
                $designation = $designations[array_rand($designations)];
                
                $stmt = $pdo->prepare("INSERT INTO user_profiles (user_id, school_id, designation, phone, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())");
                $stmt->execute([
                    $userId,
                    $school['id'],
                    $designation,
                    '+123456789' . rand(0, 9)
                ]);
            } else {
                echo "❌ Failed to create user: " . $user['name'] . "\n";
            }
        }
        
        echo "\n✅ Test users created successfully!\n\n";
        
        // Fetch users again
        $stmt = $pdo->query("SELECT u.*, s.name as school_name FROM users u LEFT JOIN schools s ON s.id = u.school_id WHERE u.is_deleted = 0 ORDER BY u.created_at DESC");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Display users
    echo "👥 Current users:\n";
    echo str_repeat("-", 80) . "\n";
    printf("%-5s %-20s %-30s %-15s %-10s\n", "ID", "Name", "Email", "School", "Status");
    echo str_repeat("-", 80) . "\n";
    
    foreach ($users as $user) {
        printf("%-5s %-20s %-30s %-15s %-10s\n", 
            $user['id'], 
            substr($user['name'], 0, 19), 
            substr($user['email'], 0, 29),
            substr($user['school_name'] ?: 'No School', 0, 14),
            $user['status']
        );
    }
    
    echo str_repeat("-", 80) . "\n";
    echo "Total: " . count($users) . " users\n\n";
    
    echo "🔗 You can now test the User Management functionality at:\n";
    echo "http://localhost:3000/m-dev.php/superadmin/dashboard\n";
    echo "Login: <EMAIL> / 1234\n";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
}
