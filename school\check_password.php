<?php
// Simple database connection
$host = 'localhost';
$dbname = 'school';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // First, let's see what users exist
    $stmt = $pdo->query("SELECT id, email, name FROM users LIMIT 10");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "Existing users:\n";
    foreach ($users as $u) {
        echo "ID: {$u['id']}, Email: {$u['email']}, Name: {$u['name']}\n";
    }
    echo "\n";

    // Query the user
    $stmt = $pdo->prepare("SELECT email, password FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user) {
        echo "Email: " . $user['email'] . "\n";
        echo "Stored password hash: " . $user['password'] . "\n";

        // Test different passwords
        $testPasswords = ['123456', 'password', 'admin', 'schooladmin', 'arunkumar', 'test123', 'admin123'];

        foreach ($testPasswords as $testPassword) {
            if (password_verify($testPassword, $user['password'])) {
                echo "✅ Correct password: " . $testPassword . "\n";
                exit;
            } else {
                echo "❌ Wrong password: " . $testPassword . "\n";
            }
        }

        echo "\nNone of the test passwords worked. Let's create a new hash for '123456':\n";
        echo "New hash: " . password_hash('123456', PASSWORD_DEFAULT) . "\n";

    } else {
        echo "User not found\n";
    }
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
