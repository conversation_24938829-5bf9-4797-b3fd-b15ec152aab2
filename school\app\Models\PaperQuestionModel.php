<?php

namespace App\Models;

use CodeIgniter\Model;

class PaperQuestionModel extends Model
{
    protected $table = 'paper_questions';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'paper_id', 'question_id', 'question_order', 'marks', 'section_name'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = null; // No updated_at field

    // Validation
    protected $validationRules = [
        'paper_id' => 'required|integer',
        'question_id' => 'required|integer',
        'question_order' => 'required|integer|greater_than[0]',
        'marks' => 'required|integer|greater_than[0]',
        'section_name' => 'permit_empty|max_length[100]'
    ];

    protected $validationMessages = [
        'paper_id' => [
            'required' => 'Paper ID is required'
        ],
        'question_id' => [
            'required' => 'Question ID is required'
        ],
        'question_order' => [
            'required' => 'Question order is required',
            'greater_than' => 'Question order must be greater than 0'
        ],
        'marks' => [
            'required' => 'Marks is required',
            'greater_than' => 'Marks must be greater than 0'
        ]
    ];

    /**
     * Get questions for a paper with question details
     */
    public function getQuestionsForPaper($paperId)
    {
        return $this->select('
            paper_questions.*,
            questions.question_text,
            questions.question_type,
            questions.difficulty,
            questions.option_a,
            questions.option_b,
            questions.option_c,
            questions.option_d,
            questions.correct_answer,
            questions.answer,
            questions.chapter_name,
            questions.topic_name,
            questions.marks as original_marks
        ')
        ->join('questions', 'questions.id = paper_questions.question_id', 'left')
        ->where('paper_questions.paper_id', $paperId)
        ->orderBy('paper_questions.question_order', 'ASC')
        ->findAll();
    }

    /**
     * Update question order for a paper
     */
    public function updateQuestionOrder($paperId, $questionOrders)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            foreach ($questionOrders as $questionId => $order) {
                $this->where('paper_id', $paperId)
                     ->where('question_id', $questionId)
                     ->set('question_order', $order)
                     ->update();
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Failed to update question order');
            }

            return true;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * Update marks for questions in a paper
     */
    public function updateQuestionMarks($paperId, $questionMarks)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            foreach ($questionMarks as $questionId => $marks) {
                $this->where('paper_id', $paperId)
                     ->where('question_id', $questionId)
                     ->set('marks', $marks)
                     ->update();
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Failed to update question marks');
            }

            return true;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * Get questions grouped by section
     */
    public function getQuestionsBySection($paperId)
    {
        $questions = $this->getQuestionsForPaper($paperId);
        
        $questionsBySection = [];
        foreach ($questions as $question) {
            $sectionName = $question['section_name'] ?? 'Part I';
            if (!isset($questionsBySection[$sectionName])) {
                $questionsBySection[$sectionName] = [];
            }
            $questionsBySection[$sectionName][] = $question;
        }

        return $questionsBySection;
    }

    /**
     * Calculate total marks for a paper
     */
    public function calculateTotalMarks($paperId)
    {
        $result = $this->selectSum('marks')
                      ->where('paper_id', $paperId)
                      ->first();

        return $result['marks'] ?? 0;
    }

    /**
     * Get question count by section
     */
    public function getQuestionCountBySection($paperId)
    {
        return $this->select('section_name, COUNT(*) as count, SUM(marks) as total_marks')
                   ->where('paper_id', $paperId)
                   ->groupBy('section_name')
                   ->findAll();
    }

    /**
     * Remove question from paper
     */
    public function removeQuestionFromPaper($paperId, $questionId)
    {
        $db = \Config\Database::connect();
        $db->transStart();

        try {
            // Get the question order to be deleted
            $questionToDelete = $this->where('paper_id', $paperId)
                                   ->where('question_id', $questionId)
                                   ->first();

            if (!$questionToDelete) {
                throw new \Exception('Question not found in paper');
            }

            $deletedOrder = $questionToDelete['question_order'];

            // Delete the question
            $this->where('paper_id', $paperId)
                 ->where('question_id', $questionId)
                 ->delete();

            // Update order of remaining questions
            $this->where('paper_id', $paperId)
                 ->where('question_order >', $deletedOrder)
                 ->set('question_order', 'question_order - 1', false)
                 ->update();

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('Failed to remove question from paper');
            }

            return true;

        } catch (\Exception $e) {
            $db->transRollback();
            throw $e;
        }
    }

    /**
     * Add question to paper
     */
    public function addQuestionToPaper($paperId, $questionId, $marks = 1, $sectionName = 'Part I')
    {
        // Get the next order number
        $maxOrder = $this->selectMax('question_order')
                        ->where('paper_id', $paperId)
                        ->first();

        $nextOrder = ($maxOrder['question_order'] ?? 0) + 1;

        $data = [
            'paper_id' => $paperId,
            'question_id' => $questionId,
            'question_order' => $nextOrder,
            'marks' => $marks,
            'section_name' => $sectionName
        ];

        return $this->insert($data);
    }

    /**
     * Check if question exists in paper
     */
    public function questionExistsInPaper($paperId, $questionId)
    {
        return $this->where('paper_id', $paperId)
                   ->where('question_id', $questionId)
                   ->countAllResults() > 0;
    }

    /**
     * Get paper statistics
     */
    public function getPaperStatistics($paperId)
    {
        $questions = $this->getQuestionsForPaper($paperId);
        
        $stats = [
            'total_questions' => count($questions),
            'total_marks' => array_sum(array_column($questions, 'marks')),
            'by_difficulty' => [],
            'by_type' => [],
            'by_section' => []
        ];

        // Group by difficulty
        foreach ($questions as $question) {
            $difficulty = $question['difficulty'] ?? 'unknown';
            if (!isset($stats['by_difficulty'][$difficulty])) {
                $stats['by_difficulty'][$difficulty] = 0;
            }
            $stats['by_difficulty'][$difficulty]++;
        }

        // Group by type
        foreach ($questions as $question) {
            $type = $question['question_type'] ?? 'unknown';
            if (!isset($stats['by_type'][$type])) {
                $stats['by_type'][$type] = 0;
            }
            $stats['by_type'][$type]++;
        }

        // Group by section
        foreach ($questions as $question) {
            $section = $question['section_name'] ?? 'Part I';
            if (!isset($stats['by_section'][$section])) {
                $stats['by_section'][$section] = [
                    'count' => 0,
                    'marks' => 0
                ];
            }
            $stats['by_section'][$section]['count']++;
            $stats['by_section'][$section]['marks'] += $question['marks'];
        }

        return $stats;
    }
}
