<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CreateSchoolSettingsTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'school_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
            ],
            'setting_key' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
            ],
            'setting_value' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'setting_type' => [
                'type' => 'ENUM',
                'constraint' => ['string', 'integer', 'boolean', 'json'],
                'default' => 'string',
            ],
            'category' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'default' => 'general',
            ],
            'description' => [
                'type' => 'VARCHAR',
                'constraint' => 500,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'updated_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
            'deleted_at' => [
                'type' => 'DATETIME',
                'null' => true,
            ],
        ]);
        
        $this->forge->addKey('id', true);
        $this->forge->addKey(['school_id', 'setting_key'], false, true); // Unique composite key
        $this->forge->addForeignKey('school_id', 'schools', 'id', 'CASCADE', 'CASCADE');
        $this->forge->createTable('school_settings');

        // Insert default settings for existing schools
        $this->insertDefaultSettings();
    }

    public function down()
    {
        $this->forge->dropTable('school_settings', true);
    }

    private function insertDefaultSettings()
    {
        $db = \Config\Database::connect();
        
        // Get all existing schools
        $schools = $db->table('schools')->select('id')->get()->getResultArray();
        
        $defaultSettings = [
            // Question Bank Settings
            ['setting_key' => 'question_require_approval', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'question_bank', 'description' => 'Require admin approval for all questions'],
            ['setting_key' => 'question_auto_approve_senior', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'question_bank', 'description' => 'Auto-approve questions from senior staff'],
            ['setting_key' => 'question_notification_enabled', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'question_bank', 'description' => 'Send notifications for pending reviews'],
            ['setting_key' => 'question_auto_approval_days', 'setting_value' => '7', 'setting_type' => 'integer', 'category' => 'question_bank', 'description' => 'Auto-approval threshold in days'],
            
            // User Management Settings
            ['setting_key' => 'user_registration_method', 'setting_value' => 'admin-only', 'setting_type' => 'string', 'category' => 'user_management', 'description' => 'User registration method'],
            ['setting_key' => 'user_email_verification', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'user_management', 'description' => 'Require email verification'],
            ['setting_key' => 'user_self_profile_update', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'user_management', 'description' => 'Allow users to update their own profiles'],
            ['setting_key' => 'user_default_role', 'setting_value' => 'teacher', 'setting_type' => 'string', 'category' => 'user_management', 'description' => 'Default role for new users'],
            
            // Notification Settings
            ['setting_key' => 'notification_email_new_questions', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Email notifications for new questions'],
            ['setting_key' => 'notification_email_approvals', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Email notifications for approvals/rejections'],
            ['setting_key' => 'notification_email_registrations', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Email notifications for new registrations'],
            ['setting_key' => 'notification_email_maintenance', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Email notifications for maintenance alerts'],
            ['setting_key' => 'notification_email_weekly_summary', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Weekly activity summary emails'],
            ['setting_key' => 'notification_app_badges', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Show notification badges'],
            ['setting_key' => 'notification_app_sounds', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Play notification sounds'],
            ['setting_key' => 'notification_app_desktop', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'notifications', 'description' => 'Desktop notifications'],
            ['setting_key' => 'notification_retention_days', 'setting_value' => '30', 'setting_type' => 'integer', 'category' => 'notifications', 'description' => 'Notification retention period in days'],
            
            // System Settings
            ['setting_key' => 'system_timezone', 'setting_value' => 'Asia/Kolkata', 'setting_type' => 'string', 'category' => 'system', 'description' => 'System timezone'],
            ['setting_key' => 'system_date_format', 'setting_value' => 'DD/MM/YYYY', 'setting_type' => 'string', 'category' => 'system', 'description' => 'Date format'],
            ['setting_key' => 'system_language', 'setting_value' => 'en', 'setting_type' => 'string', 'category' => 'system', 'description' => 'System language'],
            ['setting_key' => 'system_maintenance_mode', 'setting_value' => 'false', 'setting_type' => 'boolean', 'category' => 'system', 'description' => 'Maintenance mode status'],
            ['setting_key' => 'system_data_retention_months', 'setting_value' => '12', 'setting_type' => 'integer', 'category' => 'system', 'description' => 'Data retention period in months'],
            ['setting_key' => 'system_auto_backup', 'setting_value' => 'true', 'setting_type' => 'boolean', 'category' => 'system', 'description' => 'Enable automatic backups'],
            ['setting_key' => 'system_backup_frequency', 'setting_value' => 'daily', 'setting_type' => 'string', 'category' => 'system', 'description' => 'Backup frequency'],
        ];

        // Insert settings for each school
        foreach ($schools as $school) {
            $settingsToInsert = [];
            foreach ($defaultSettings as $setting) {
                $settingsToInsert[] = array_merge($setting, [
                    'school_id' => $school['id'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
            
            if (!empty($settingsToInsert)) {
                $db->table('school_settings')->insertBatch($settingsToInsert);
            }
        }
    }
}
