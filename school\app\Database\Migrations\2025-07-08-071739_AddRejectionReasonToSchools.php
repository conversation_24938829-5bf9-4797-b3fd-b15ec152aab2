<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AddRejectionReasonToSchools extends Migration
{
    public function up()
{
    $this->forge->addColumn('schools', [
        'rejection_reason' => [
            'type' => 'TEXT',
            'null' => true,
            'after' => 'status' // adjust this based on your schema
        ]
    ]);
}


    public function down()
{
    $this->forge->dropColumn('schools', 'rejection_reason');
}

}
