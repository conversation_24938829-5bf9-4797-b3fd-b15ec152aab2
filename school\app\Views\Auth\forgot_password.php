<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Forgot Password' ?> - School Question Bank</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-shadow {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Main Card -->
        <div class="bg-white rounded-2xl card-shadow overflow-hidden">
            <!-- Header -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-6 text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-key text-2xl text-white"></i>
                </div>
                <h1 class="text-2xl font-bold text-white">Forgot Password</h1>
                <p class="text-indigo-100 mt-2">
                    <?= $userType === 'school' ? 'School Admin Account Recovery' : 'Staff Account Recovery' ?>
                </p>
            </div>

            <!-- Content -->
            <div class="px-8 py-6">
                <!-- Success/Error Messages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            <?= session()->getFlashdata('success') ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <?= session()->getFlashdata('error') ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Dynamic Message Container -->
                <div id="messageContainer" class="hidden mb-6"></div>

                <!-- Instructions -->
                <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                        <div class="text-sm text-blue-700">
                            <p class="font-medium mb-1">Password Reset Instructions:</p>
                            <ul class="list-disc list-inside space-y-1 text-xs">
                                <li>Enter your registered email address</li>
                                <li>Check your email for the reset link</li>
                                <li>The link will expire in 1 hour</li>
                                <li>Contact support if you don't receive the email</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Forgot Password Form -->
                <form id="forgotPasswordForm" class="space-y-6">
                    <input type="hidden" name="user_type" value="<?= esc($userType) ?>">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        <div class="relative">
                            <input type="email" name="email" id="email" required
                                   class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition"
                                   placeholder="Enter your registered email">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-4 pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                        </div>
                    </div>

                    <button type="submit" id="submitBtn"
                            class="w-full bg-indigo-600 text-white py-3 rounded-lg font-medium hover:bg-indigo-700 transition duration-300 flex items-center justify-center">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Reset Link
                    </button>
                </form>

                <!-- Back to Login -->
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-600">
                        Remember your password?
                        <a href="<?= $userType === 'school' ? base_url('login/schooladmin') : base_url('staff/login') ?>" 
                           class="text-indigo-600 font-medium hover:text-indigo-500 hover:underline">
                            Back to Login
                        </a>
                    </p>
                </div>

                <!-- Security Notice -->
                <div class="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="fas fa-shield-alt text-gray-500 mt-1 mr-3"></i>
                        <div class="text-xs text-gray-600">
                            <p class="font-medium mb-1">Security Notice:</p>
                            <p>For your security, we'll only send reset instructions to registered email addresses. If you don't receive an email, please contact your system administrator.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-6 text-white text-sm opacity-75">
            <p>&copy; <?= date('Y') ?> School Question Bank. All rights reserved.</p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('forgotPasswordForm');
            const submitBtn = document.getElementById('submitBtn');
            const messageContainer = document.getElementById('messageContainer');

            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const originalText = submitBtn.innerHTML;
                
                // Show loading state
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Sending...';
                
                // Clear previous messages
                messageContainer.classList.add('hidden');
                
                fetch('<?= base_url('forgot-password/send') ?>', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        form.reset();
                    } else {
                        showMessage(data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('An error occurred. Please try again.', 'error');
                })
                .finally(() => {
                    // Reset button state
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                });
            });

            function showMessage(message, type) {
                const isSuccess = type === 'success';
                const bgColor = isSuccess ? 'bg-green-100 border-green-400 text-green-700' : 'bg-red-100 border-red-400 text-red-700';
                const icon = isSuccess ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
                
                messageContainer.innerHTML = `
                    <div class="p-4 ${bgColor} border rounded-lg">
                        <div class="flex items-center">
                            <i class="${icon} mr-2"></i>
                            ${message}
                        </div>
                    </div>
                `;
                messageContainer.classList.remove('hidden');
                
                // Scroll to message
                messageContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    </script>
</body>
</html>
