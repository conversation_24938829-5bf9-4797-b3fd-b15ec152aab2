<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use App\Services\SubscriptionService;

class UsageLimitFilter implements FilterInterface
{
    protected $subscriptionService;

    public function __construct()
    {
        $this->subscriptionService = new SubscriptionService();
    }

    public function before(RequestInterface $request, $arguments = null)
    {
        // Check if user is logged in as school admin
        if (!session()->get('logged_in') || session()->get('role') !== 'schooladmin') {
            return null;
        }

        $schoolId = session()->get('school_id');
        if (!$schoolId) {
            return null;
        }

        // Get the feature to check from arguments
        $featureKey = $arguments[0] ?? 'general';
        $requestedAmount = $arguments[1] ?? 1;

        // Check if school can use this feature
        if (!$this->subscriptionService->canUseFeature($schoolId, $featureKey, $requestedAmount)) {
            
            // For AJAX requests, return JSON response
            if ($request->isAJAX()) {
                return service('response')->setJSON([
                    'success' => false,
                    'message' => 'You have reached your plan limit. Please upgrade to continue.',
                    'upgrade_required' => true,
                    'feature' => $featureKey
                ])->setStatusCode(403);
            }

            // For regular requests, redirect with error
            return redirect()->to('/subscription/upgrade')
                ->with('error', 'You have reached your plan limit. Please upgrade to continue.');
        }

        return null;
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        return null;
    }
}
